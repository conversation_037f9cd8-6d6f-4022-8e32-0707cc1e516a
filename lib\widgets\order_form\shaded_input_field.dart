import 'package:flutter/material.dart';

class ShadedInputField extends StatelessWidget {
  const ShadedInputField({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Background with diagonal lines
        CustomPaint(
          size: const Size(200, 50),
          painter: <PERSON>agonalLinesPainter(),
        ),
        // Input field
        TextField(
          decoration: InputDecoration(
            hintText: "1 minute",
            hintStyle: const TextStyle(color: Colors.grey),
            filled: true,
            fillColor: Colors.black.withOpacity(0.5),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12),
          ),
          style: const TextStyle(color: Colors.white),
        ),
      ],
    );
  }
}

class DiagonalLinesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1) // Light grey lines
      ..strokeWidth = 1;

    const double gap = 10; // Distance between lines

    for (double x = -size.height; x < size.width; x += gap) {
      canvas.drawLine(Offset(x, 0), Offset(x + size.height, size.height), paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}