//This is the model we are going to get from the socket connection
class TickDataModel {
  final int index;
  final String instrumentToken;
  final double lastPrice;
  final int lastTradedQuantity;

  TickDataModel(
      {required this.index,
      required this.instrumentToken,
      required this.lastPrice,
      required this.lastTradedQuantity});

  factory TickDataModel.fromJson(Map<String, dynamic> json) {
    print('3 Model');
    print(json.toString());
    return TickDataModel(
      index: json['index'],
      instrumentToken:
          json['data']['instrument_token'].toString(), // Convert to String
      lastPrice: (json['data']['last_price'] as num)
          .toDouble(), // Ensure it's a double
      lastTradedQuantity: json['data']['last_traded_quantity'],
    );
  }
}
