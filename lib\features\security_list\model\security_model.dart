import 'dart:convert';

class SecurityModel {
  final int zenId;
  final String tradingSymbol;
  final String? expiry;
  final String? expiryType;
  final String name;
  final double strike;
  final List<String> exchanges;
  final int lotSize;
  final String instrumentType;

  SecurityModel({
    required this.zenId,
    required this.tradingSymbol,
    required this.name,
    required this.expiryType,
    this.expiry,
    required this.strike,
    required this.exchanges,
    required this.lotSize,
    required this.instrumentType,
  });

  /// Factory method to create a SecurityModel object from JSON
  factory SecurityModel.fromJson(Map<String, dynamic> json) {
    return SecurityModel(
      zenId: json['zen_id'] as int,
      tradingSymbol: json['trading_symbol'] as String,
      expiry: json['expiry'] == "null" ? null : json['expiry'] as String?,
      expiryType: json['expiry_type'] == "null" ? null : json['expiry_type'] as String?,
      name: json['name'] as String,
      strike: (json['strike'] as num).toDouble(),
      exchanges: List<String>.from(json['exchanges']), // Convert list of exchanges
      lotSize: json['lot_size'] as int,
      instrumentType: json['instrument_type'] as String,
    );
  }

  /// Convert SecurityModel object to JSON
  Map<String, dynamic> toJson() {
    return {
      'zen_id': zenId,
      'trading_symbol': tradingSymbol,
      'expiry': expiry ?? "null", // Handle null expiry case
      'expiry_type': expiryType ?? "null", // Handle null expiry case
      'name': name,
      'strike': strike,
      'exchanges': exchanges, // Store exchanges as a list
      'lot_size': lotSize,
      'instrument_type': instrumentType,
    };
  }

  /// Parse list of securities from JSON response
  static List<SecurityModel> listFromJson(String str) {
    final List<dynamic> jsonData = json.decode(str);
    return jsonData.map((item) => SecurityModel.fromJson(item)).toList();
  }

  @override
  String toString() {
    return 'SecurityModel(zenId: $zenId, tradingSymbol: $tradingSymbol, expiry: $expiry, expiryType: $expiryType, name: $name, strike: $strike, exchanges: $exchanges, lotSize: $lotSize, instrumentType: $instrumentType)';
  }
}
