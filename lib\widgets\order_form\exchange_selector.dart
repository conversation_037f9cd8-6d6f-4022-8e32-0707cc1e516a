import 'package:flutter/material.dart';
import 'package:phoenix/widgets/custom_radio_button/custom_radio_widget.dart';

class ExchangeSelector extends StatelessWidget {
  final bool isNSE;
  final Function action1;
  final Function action2;

  const ExchangeSelector({
    super.key,
    required this.isNSE,
    required this.action1,
    required this.action2,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30,
      decoration: BoxDecoration(
          color: Color(0xffD9D9D9),
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(10),
          )),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: 10,
        children: [
          Row(
            children: [
              CustomRadioWidget(
                groupValue: isNSE,
                value: true,
                onChanged: (value) {
                  action1(value);
                },
                height: 15,
                width: 15,
                isActiveColor: Colors.black,
                isNotActiveColor: Colors.white,
                borderColor: Colors.black,
              ),
              Text(
                'NSE ₹432.15',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
          SizedBox(
            width: 4,
          ),
          Row(
            children: [
              CustomRadioWidget(
                groupValue: isNSE,
                value: false,
                onChanged: (value) {
                  action2(value);
                },
                height: 15,
                width: 15,
                isActiveColor: Colors.black,
                isNotActiveColor: Colors.white,
                borderColor: Colors.black,
              ),
              Text(
                'BSE ₹432.05',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
