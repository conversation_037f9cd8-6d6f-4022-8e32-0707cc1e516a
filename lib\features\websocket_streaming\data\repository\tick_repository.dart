import 'dart:convert';

import 'package:phoenix/features/websocket_streaming/data/provider/web_socket_data_provider.dart';
import 'package:phoenix/features/websocket_streaming/model/tick_data_model.dart';

///The repository interacts with the data provider and processes the data.
class TickRepository {
  final WebSocketDataProvider _dataProvider;

  TickRepository(this._dataProvider);

  Stream<TickDataModel> connect(String url, {Map<String,String>? headers}){
    final stream = _dataProvider.connect(url,headers: headers);
    print('2. tick repo');
    return stream.map((data){
      final decoded = jsonDecode(data);
      return TickDataModel.fromJson(decoded);
    });
  }

  void sendPing(){
    _dataProvider.sendPing();
  }

  void disconnect(){
    _dataProvider.disconnect();
  }

}