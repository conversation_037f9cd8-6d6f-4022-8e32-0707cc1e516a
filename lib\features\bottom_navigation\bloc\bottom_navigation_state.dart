part of 'bottom_navigation_bloc.dart';

@immutable
sealed class BottomNavigationState {}

final class BottomNavigationInitial extends BottomNavigationState {
  final int currentPageIndex = 0;
  final bool isNetWorthBarExpanded = false;
}

final class BottomNavigaionChanged extends BottomNavigationState {
  final int currentPageIndex;
  final bool isNetWorthBarExpanded;

  BottomNavigaionChanged({required this.currentPageIndex, required this.isNetWorthBarExpanded});
  
}
