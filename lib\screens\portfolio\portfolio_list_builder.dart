import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/broker_data_map/bloc/broker_data_map_bloc.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/portfolio_data/bloc/portfolio_bloc.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/empty_state/empty_container.dart';
import 'package:phoenix/widgets/tile/tile_generic.dart';

class PortfolioListBuilder extends StatelessWidget {
  final List<dynamic> data;
  final String emptyMessage;
  final AnimationController formSheetAnimeController;

  const PortfolioListBuilder(
      {required this.data,
      required this.emptyMessage,
      super.key,
      required this.formSheetAnimeController});

  @override
  Widget build(BuildContext context) {
    Future<void> pullRefresh() async {
      final authState = context.read<AuthBloc>().state; // Get current state

      if (authState is AuthAuthenticated) {
        context
            .read<PortfolioBloc>()
            .add(FetchPortfolio(authState.credentialsModel.clientId));
      } else {
        // Handle unauthenticated case (e.g., show login dialog)
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("You need to log in first")),
        );
      }
    }

    if (data.isEmpty) {
      return Center(
          child: EmptyContainer(
              title: "No Closed Positions",
              message: emptyMessage,
              imagePath: "images/database-positions-no.png"));
    }

    return BlocBuilder<WebSocketBloc, WebSocketState>(
      // Extract multiple stock prices if available
      builder: (context, state) {
        Map<int, double> stockPrices = {};

        if (state is WebSocketMultipleStockPricesUpdated) {
          stockPrices = state.stockPrices;
          //debugPrint(" ✌️ Reciving Stock prices length : ${stockPrices.length.toString()}");
        }

        final brokerDataMapState = context.read<BrokerDataMapBloc>().state;
        if (brokerDataMapState is! BrokerDataMapProcessedState) {
          return Center(child: Text("Something went wrong..."));
        }

        return RefreshIndicator(
          color: ThemeConstants.blue,
          backgroundColor: ThemeConstants.backgroundColor,
          onRefresh: pullRefresh,
          child: ListView.builder(
            clipBehavior: Clip.hardEdge,
            padding: const EdgeInsets.all(6.0),
            itemCount: data.length + 1,
            itemBuilder: (context, index) {
              if (index == data.length) {
                // Add a SizedBox at the end
                return const SizedBox(height: 50); // Adjust height as needed
              }
              final item = data[index] as PositionsModel;
              final int zenId = item.positionCompositeKey.zenSecId;

              // // Fetch the latest price for this zenId
              final double? priceUpdate = stockPrices[zenId];

              // Broker Account Strategy id Map
              final brokerName = brokerDataMapState
                      .brokerNameToLabelMap[item.positionCompositeKey.broker] ??
                  "N/A";
              final accountName = brokerDataMapState.accountIdToNameMap[
                      item.positionCompositeKey.accountId] ??
                  "N/A";
              final strategyName = brokerDataMapState.strategyIdToNameMap[
                      item.positionCompositeKey.strategyId] ??
                  "N/A";
              final brokerMetaData = BrokerAccountStrategyData(
                brokerName: brokerName,
                accountId: item.positionCompositeKey.accountId,
                strategyId: item.positionCompositeKey.strategyId,
                accountName: accountName,
                strategyName: strategyName,
              );

              return TileGeneric(
                data: item,
                tileType: "position",
                formAnimeController: formSheetAnimeController,
                prices: priceUpdate,
                brokerAccountStrategyMapData: brokerMetaData,
              );
            },
          ),
        );
      },
    );
  }
}
