import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';
import '../custom_number_field.dart';

class InputBox extends StatefulWidget {
  final String placeholder;
  final void Function(String) action;
  final double height;
  final double width;
  final bool isEnabled;
  final double placeHolderFontSize;
  final TextEditingController controller;
  final ValueNotifier<bool> notifier;
  final List<TextInputFormatter>? inputFormatter;

  const InputBox({
    super.key,
    required this.placeholder,
    required this.action,
    required this.height,
    required this.width,
    this.isEnabled = true,
    required this.controller,
    required this.notifier,
    this.placeHolderFontSize = 15,
    this.inputFormatter,
  });

  @override
  State<InputBox> createState() => _InputBoxState();
}

class _InputBoxState extends State<InputBox> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return ValueListenableBuilder<bool>(
          valueListenable: widget.notifier,
          builder: (context, isValid, child) {
            return SizedBox(
              height: widget.height,
              width: widget.width,
              child: Stack(
                children: [
                  if (!widget.isEnabled)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: CustomPaint(
                        size: Size(widget.width, widget.height),
                        painter: DiagonalLinesPainter(themeState.isDarkMode),
                      ),
                    ),
                  CustomNumberField(
                    controller: widget.controller,
                    enabled: widget.isEnabled,
                    style: TextStyle(
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                onChanged: (value) {
                  if (value.isNotEmpty) {
                    widget.action(value.isNotEmpty ? value : "0");
                    final numValue = double.tryParse(value);
                    widget.notifier.value = numValue != null && numValue > 0;
                  } else {
                    widget.notifier.value = false;
                    widget.action(""); // Ensure empty value is passed to action
                  }
                },
                    decoration: InputDecoration(
                      labelText: widget.placeholder,
                      labelStyle: TextStyle(
                        color: (!isValid) 
                            ? Color(0xffFF4A4A) 
                            : AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: widget.placeHolderFontSize,
                      ),
                      hintStyle: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 16,
                      ),
                      filled: true,
                      border: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: AppTheme.borderColor(themeState.isDarkMode),
                        ),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: AppTheme.borderColor(themeState.isDarkMode),
                        ),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      disabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide(
                          color: AppTheme.borderColor(themeState.isDarkMode).withOpacity(0.5),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: AppTheme.primaryColor(themeState.isDarkMode),
                        ),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      errorStyle: const TextStyle(fontSize: 0.01),
                      errorBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Colors.transparent),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: AppTheme.primaryColor(themeState.isDarkMode),
                        ),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      fillColor: (!widget.isEnabled)
                        ? Colors.transparent
                        : (!isValid)
                          ? const Color(0xff986561).withOpacity(0.5)
                          : AppTheme.cardColor(themeState.isDarkMode),
                  suffixIcon: (!isValid)
                    ? Padding(
                        padding: const EdgeInsets.all(3.0),
                        child: ImageIcon(
                          AssetImage("images/warning-icon.png"),
                          color: Color.fromARGB(255, 255, 41, 41),
                          size: 10,
                        ),
                      )
                    : null,
                  suffixIconConstraints: BoxConstraints(
                    minHeight: 25,
                    minWidth: 25,
                  ),
                  //This padding ensures of the input feild to not overflow hide cut when number is larger
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                ),
                formattersList: widget.inputFormatter,
              ),
            ],
          ),
        );
          },
        );
      },
    );
  }
}

class DiagonalLinesPainter extends CustomPainter {
  final bool isDarkMode;
  
  DiagonalLinesPainter(this.isDarkMode);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = isDarkMode 
          ? Colors.white.withOpacity(0.2) 
          : Colors.grey.withOpacity(0.3)
      ..strokeWidth = 1;

    const double gap = 10; // Distance between lines

    for (double x = size.width + size.height; x > 0; x -= gap) {
      canvas.drawLine(
        Offset(x, 0), // Start from top-right
        Offset(x - size.height, size.height), // End at bottom-left
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
