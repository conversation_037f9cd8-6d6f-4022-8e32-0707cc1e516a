import 'package:phoenix/features/common/position_comp_key.dart';

class UnifiedOrderData {
  final List<OrderFillState> orderFillState;
  final String status;
  final String message;
  final int zenOrderId;
  final int? bucketOrderId;
  final PositionCompKey positionCompKey;
  final String tradingSymbol;
  final String orderType;

  UnifiedOrderData({
    required this.orderFillState,
    required this.status,
    required this.message,
    required this.zenOrderId,
    this.bucketOrderId,
    required this.positionCompKey,
    required this.tradingSymbol,
    required this.orderType,
  });

  factory UnifiedOrderData.fromJson(Map<String, dynamic> json) {
    return UnifiedOrderData(
      orderFillState: (json['order_fill_state'] as List)
          .map((e) => OrderFillState.fromJson(e))
          .toList(),
      status: json['status'],
      message: json['message'],
      zenOrderId: json['zen_order_id'],
      bucketOrderId: json['bucket_order_id'],
      positionCompKey: PositionCompKey.fromJson(json['position_composite_key']),
      tradingSymbol: json['trading_symbol'],
      orderType: json['order_type'],

    );
  }
}

class OrderFillState {
  final int internalOrderId;
  final String transactionType;
  final String orderType;
  final int totalQuantity;
  final int totalFilledQuantity;
  final int currentFilledQuantity;
  final double averagePrice;
  final String orderStatus;
  final String message;
  final DateTime? exchangeTimestamp;
  final DateTime? brokerTimestamp;
  final String tradingSymbol;
  final String exchange;
  final int zenOrderId;

  OrderFillState({
    required this.internalOrderId,
    required this.transactionType,
    required this.orderType,
    required this.totalQuantity,
    required this.totalFilledQuantity,
    required this.currentFilledQuantity,
    required this.averagePrice,
    required this.orderStatus,
    required this.message,
    required this.exchangeTimestamp,
    required this.brokerTimestamp,
    required this.tradingSymbol,
    required this.exchange,
    required this.zenOrderId,
  });

  factory OrderFillState.fromJson(Map<String, dynamic> json) {
    return OrderFillState(
      internalOrderId: json['internal_order_id'],
      transactionType: json['transaction_type'],
      orderType: json['order_type'],
      totalQuantity: json['total_quantity'],
      totalFilledQuantity: json['total_filled_quantity'] ?? 0,
      currentFilledQuantity: json['current_filled_quantity'] ?? 0,
      averagePrice: ((json['average_price'] ?? 0.0) as num).toDouble(),
      orderStatus: json['order_status'],
      message: json['message'] ?? '',
      exchangeTimestamp: json['exchange_timestamp'] != null
          ? DateTime.tryParse(json['exchange_timestamp'])
          : null,
      brokerTimestamp: json['broker_timestamp'] != null
          ? DateTime.tryParse(json['broker_timestamp'])
          : null,
      tradingSymbol: json['trading_symbol'],
      exchange: json['exchange'],
      zenOrderId: json['zen_order_id'],
    );
  }
}
