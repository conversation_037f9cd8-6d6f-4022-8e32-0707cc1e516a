part of 'commissions_data_map_bloc.dart';

@immutable
sealed class CommissionsDataMapState {}

final class CommissionsDataMapInitial extends CommissionsDataMapState {}

class CommissionsDataMapLoading extends CommissionsDataMapState {}

class CommissionsDataMapLoaded extends CommissionsDataMapState {
  final dynamic data;

  CommissionsDataMapLoaded(this.data);
}

class CommissionsDataMapError extends CommissionsDataMapState {
  final String error;

  CommissionsDataMapError(this.error);
}
