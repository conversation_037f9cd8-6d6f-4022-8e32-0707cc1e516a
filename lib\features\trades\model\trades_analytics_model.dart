class TradesAnalyticsModel {
  final int totalTrades;
  final int buyTrades;
  final int sellTrades;
  final int totalVolume;
  final double totalValue;
  final double avgTradeSize;
  final Map<String, BrokerAnalytics> brokerAnalytics;

  TradesAnalyticsModel({
    required this.totalTrades,
    required this.buyTrades,
    required this.sellTrades,
    required this.totalVolume,
    required this.totalValue,
    required this.avgTradeSize,
    required this.brokerAnalytics,
  });

  factory TradesAnalyticsModel.empty() {
    return TradesAnalyticsModel(
      totalTrades: 0,
      buyTrades: 0,
      sellTrades: 0,
      totalVolume: 0,
      totalValue: 0.0,
      avgTradeSize: 0.0,
      brokerAnalytics: {},
    );
  }
}

class BrokerAnalytics {
  final String brokerName;
  final int tradeCount;
  final double totalInvested;
  final int volume;

  BrokerAnalytics({
    required this.brokerName,
    required this.tradeCount,
    required this.totalInvested,
    required this.volume,
  });
}
