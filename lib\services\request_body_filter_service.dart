import 'package:phoenix/services/shared_prefrences_service.dart';
// this will be used for filtering in pnl, positions and order state end points
class RequestBodyFilterService {
  static Future<Map<String, dynamic>> buildPositionCompKeyFilter(int clientId) async {
    final prefs = SharedPreferencesService.instance;
    final defaultAccount = prefs.getDefaultAccount(clientId);
    final defaultStrategy = prefs.getDefaultStrategy(clientId);
    final defaultBroker = prefs.getDefaultBroker(clientId);

    final Map<String, dynamic> filter = {};

    if (defaultAccount != null && defaultAccount.toLowerCase() != 'all') {
      final accountId = int.tryParse(defaultAccount);
      if (accountId != null) {
        filter['account_ids'] = [accountId]; 
      }
    }

    if (defaultStrategy != null && defaultStrategy.toLowerCase() != 'all') {
      final strategyId = int.tryParse(defaultStrategy);
      if (strategyId != null) {
        filter['strategy_ids'] = [strategyId];
      }
    }

    if (defaultBroker != null && defaultBroker.toLowerCase() != 'all') {
      filter['brokers'] = [defaultBroker];
    }

    return filter;
  }
}
