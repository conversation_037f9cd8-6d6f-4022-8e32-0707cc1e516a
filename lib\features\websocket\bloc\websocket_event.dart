part of 'websocket_bloc.dart';

@immutable
abstract class WebSocketEvent {
  const WebSocketEvent();
}

class WebSocketConnect extends WebSocketEvent {
  final String accessToken;
  const WebSocketConnect(this.accessToken);
}

class WebSocketDisconnect extends WebSocket<PERSON>vent {}

class WebSocketSendMessage extends WebSocket<PERSON>vent {
  final String message;
  const WebSocketSendMessage(this.message);
}

class WebSocketReceivedData extends WebSocketEvent {
  final List<Price> data;
  const WebSocketReceivedData(this.data);
}

class WebSocketSelectStock extends WebSocketEvent {
  final String? stockId;
  const WebSocketSelectStock(this.stockId);
}

class WebSocketSelectMultipleStocks extends WebSocketEvent {
  final List<int> stockIds;
  const WebSocketSelectMultipleStocks(this.stockIds);
}



class WebSocketErrorEvent extends WebSocketEvent {
  final String error;
  const WebSocketErrorEvent(this.error);
}

class WebSocketDisconnected extends WebSocket<PERSON>vent {}
