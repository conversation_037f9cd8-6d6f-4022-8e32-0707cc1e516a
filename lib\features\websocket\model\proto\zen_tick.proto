syntax = "proto3";

package websocket;

import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";
import "depth.proto";
import "ohlc.proto";

message ZenTick {
  google.protobuf.BoolValue tradable = 1;
  google.protobuf.StringValue mode = 2;
  google.protobuf.Int64Value instrument_token = 3;
  google.protobuf.DoubleValue last_price = 4;
  google.protobuf.Int32Value last_traded_quantity = 5;
  google.protobuf.DoubleValue average_traded_price = 6;
  google.protobuf.Int32Value volume_traded = 7;
  google.protobuf.Int32Value total_buy_quantity = 8;
  google.protobuf.Int32Value total_sell_quantity = 9;
  OHLC ohlc = 10;
  google.protobuf.DoubleValue change = 11;
  google.protobuf.Timestamp last_trade_time = 12;
  google.protobuf.Int32Value oi = 13;
  google.protobuf.Int32Value oi_day_high = 14;
  google.protobuf.Int32Value oi_day_low = 15;
  google.protobuf.Timestamp exchange_timestamp = 16;
  Depth depth = 17;
  google.protobuf.Timestamp phoenix_timestamp = 18;
  google.protobuf.Int64Value zen_id = 19;
}

message ZenTicks {
  repeated ZenTick zen_ticks = 1;
}
