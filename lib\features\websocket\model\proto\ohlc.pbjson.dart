//
//  Generated code. Do not modify.
//  source: ohlc.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use oHLCDescriptor instead')
const OHLC$json = {
  '1': 'OHLC',
  '2': [
    {'1': 'open', '3': 1, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'open'},
    {'1': 'high', '3': 2, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'high'},
    {'1': 'low', '3': 3, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'low'},
    {'1': 'close', '3': 4, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'close'},
  ],
};

/// Descriptor for `OHLC`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List oHLCDescriptor = $convert.base64Decode(
    'CgRPSExDEjAKBG9wZW4YASABKAsyHC5nb29nbGUucHJvdG9idWYuRG91YmxlVmFsdWVSBG9wZW'
    '4SMAoEaGlnaBgCIAEoCzIcLmdvb2dsZS5wcm90b2J1Zi5Eb3VibGVWYWx1ZVIEaGlnaBIuCgNs'
    'b3cYAyABKAsyHC5nb29nbGUucHJvdG9idWYuRG91YmxlVmFsdWVSA2xvdxIyCgVjbG9zZRgEIA'
    'EoCzIcLmdvb2dsZS5wcm90b2J1Zi5Eb3VibGVWYWx1ZVIFY2xvc2U=');

