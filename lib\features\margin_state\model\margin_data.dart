class MarginData {
  final int clientId;
  final String brokerName;
  final String accountName;
  final int accountId;
  final double availableCash;

  MarginData({
    required this.clientId,
    required this.brokerName,
    required this.accountName,
    required this.accountId,
    required this.availableCash,
  });

  // Factory method to create an instance from JSON
  factory MarginData.fromJson(Map<String, dynamic> json) {
    return MarginData(
      clientId: json['client_id'],
      brokerName: json['broker_name'],
      accountName: json['account_name'],
      accountId: json['account_id'],
      availableCash: double.parse(json['available_cash']),
    );
  }

  // Method to convert the object to JSON
  Map<String, dynamic> toJson() {
    return {
      'client_id': clientId,
      'broker_name': brokerName,
      'account_name': accountName,
      'account_id': accountId,
      'available_cash': availableCash.toString(),
    };
  }
}
