import 'dart:async';
//import 'dart:io';
//import 'package:fixnum/fixnum.dart';
//import 'package:phoenix/features/websocket/model/proto/google/protobuf/wrappers.pb.dart';
import 'package:phoenix/features/websocket/model/proto/price_proto/price.pb.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/status.dart' as status;
//import 'package:phoenix/features/websocket/model/proto/zen_tick.pb.dart';
//import 'package:phoenix/features/websocket/model/proto/req/req.pb.dart';
import 'package:flutter/foundation.dart';

class WebSocketProtoProvider {
  IOWebSocketChannel? _channel;
  final StreamController<List<Price>> _streamController = StreamController<List<Price>>.broadcast();
  final StreamController<bool> _connectionStatusController = StreamController<bool>.broadcast();
  bool _isConnected = false;

  WebSocketProtoProvider();

  /// Exposes a stream of filtered ZenTick messages.
  Stream<List<Price>> get stream => _streamController.stream;

  /// Exposes a stream for connection status updates.
  Stream<bool> get connectionStatusStream => _connectionStatusController.stream;

  /// Connects to the WebSocket.
  void connect(String url, {Map<String, String>? headers}) {
    if (_isConnected) return;

    debugPrint("🎈 Attempting WebSocket connection...");

    try {
      _channel = IOWebSocketChannel.connect(url, headers: headers, connectTimeout: Duration(days: 1));
      debugPrint("🎈🎈 Connection open");
      _isConnected = true;
      _connectionStatusController.add(true); // Emit connected status

      var request = PricesEmptyRequest();
      _channel?.sink.add(request.writeToBuffer());
      debugPrint("🎈🎈🎈 Initial request sent");

      _channel?.stream.listen(
        (message) {
          _processWebSocketMessage(message);
        },
        onError: (error) {
          debugPrint("❌ WebSocket Error: $error");
          _isConnected = false;
          _connectionStatusController.add(false); // Emit disconnected status
          _reconnect(url, headers: headers);
        },
        onDone: () {
          debugPrint("⚠️ WebSocket connection closed.");
          _isConnected = false;
          _connectionStatusController.add(false); // Emit disconnected status
          _reconnect(url, headers: headers);
        },
        cancelOnError: true,
      );
    } catch (e) {
      debugPrint("❌ Exception during WebSocket connection: $e");
      _isConnected = false;
      _connectionStatusController.add(false); // Emit disconnected status
      _reconnect(url, headers: headers);
    }
  }

  /// Filters and emits only the selected stock tick.
  /// Emits the full list of ZenTick objects without filtering.
void _processWebSocketMessage(dynamic data) {
  try {
    var prices = Prices.fromBuffer(data).prices;
    debugPrint("⚡ Received ${prices.length} ticks");
    _streamController.add(prices); // Emit the full list
  } catch (e) {
    debugPrint("😶‍🌫️ Error decoding ZenTicks: $e");
  }
}

  
  /// Sends a ping message to keep the connection alive.
  void sendPing(String message) {
    try {
      _channel?.sink.add(message);
    } catch (e) {
      print("😶‍🌫️ Error while sending ping: $e");
    }
  }

  /// Gracefully disconnects the WebSocket connection.
  void disconnect() {
    debugPrint("🔌 Disconnecting WebSocket...");
    _channel?.sink.close(status.goingAway);
    _isConnected = false;
    _connectionStatusController.add(false); // Emit disconnected status
  }

  /// Attempts to reconnect after a delay.
  void _reconnect(String url, {Map<String, String>? headers}) {
    if (_isConnected) return;
    
    _connectionStatusController.add(false); // Emit disconnected status
    Future.delayed(const Duration(seconds: 30), () {
      debugPrint("♻️ Attempting to reconnect WebSocket...");
      connect(url, headers: headers);
    });
  }
}
