class LatestOrderTypeModel {
  final bool isStoplossEnabled;
  final bool isTrailingStoplossEnabled;
  final bool isMarket;
  final String stoplossType;

  LatestOrderTypeModel({
    required this.isStoplossEnabled,
    required this.isTrailingStoplossEnabled,
    required this.isMarket,
    required this.stoplossType,
  });

  @override
  String toString() {
    return 'LatestOrderTypeModel(isStoplossEnabled: $isStoplossEnabled, isTrailingStoplossEnabled: $isTrailingStoplossEnabled, isMarket: $isMarket, stoplossType: "$stoplossType")';
  }
}

