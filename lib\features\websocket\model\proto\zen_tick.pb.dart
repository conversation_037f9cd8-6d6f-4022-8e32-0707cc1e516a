//
//  Generated code. Do not modify.
//  source: zen_tick.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'depth.pb.dart' as $3;
import 'google/protobuf/timestamp.pb.dart' as $2;
import 'google/protobuf/wrappers.pb.dart' as $0;
import 'ohlc.pb.dart' as $1;

class ZenTick extends $pb.GeneratedMessage {
  factory ZenTick({
    $0.BoolValue? tradable,
    $0.StringValue? mode,
    $0.Int64Value? instrumentToken,
    $0.DoubleValue? lastPrice,
    $0.Int32Value? lastTradedQuantity,
    $0.DoubleValue? averageTradedPrice,
    $0.Int32Value? volumeTraded,
    $0.Int32Value? totalBuyQuantity,
    $0.Int32Value? totalSellQuantity,
    $1.OHLC? ohlc,
    $0.DoubleValue? change,
    $2.Timestamp? lastTradeTime,
    $0.Int32Value? oi,
    $0.Int32Value? oiDayHigh,
    $0.Int32Value? oiDayLow,
    $2.Timestamp? exchangeTimestamp,
    $3.Depth? depth,
    $2.Timestamp? phoenixTimestamp,
    $0.Int64Value? zenId,
  }) {
    final $result = create();
    if (tradable != null) {
      $result.tradable = tradable;
    }
    if (mode != null) {
      $result.mode = mode;
    }
    if (instrumentToken != null) {
      $result.instrumentToken = instrumentToken;
    }
    if (lastPrice != null) {
      $result.lastPrice = lastPrice;
    }
    if (lastTradedQuantity != null) {
      $result.lastTradedQuantity = lastTradedQuantity;
    }
    if (averageTradedPrice != null) {
      $result.averageTradedPrice = averageTradedPrice;
    }
    if (volumeTraded != null) {
      $result.volumeTraded = volumeTraded;
    }
    if (totalBuyQuantity != null) {
      $result.totalBuyQuantity = totalBuyQuantity;
    }
    if (totalSellQuantity != null) {
      $result.totalSellQuantity = totalSellQuantity;
    }
    if (ohlc != null) {
      $result.ohlc = ohlc;
    }
    if (change != null) {
      $result.change = change;
    }
    if (lastTradeTime != null) {
      $result.lastTradeTime = lastTradeTime;
    }
    if (oi != null) {
      $result.oi = oi;
    }
    if (oiDayHigh != null) {
      $result.oiDayHigh = oiDayHigh;
    }
    if (oiDayLow != null) {
      $result.oiDayLow = oiDayLow;
    }
    if (exchangeTimestamp != null) {
      $result.exchangeTimestamp = exchangeTimestamp;
    }
    if (depth != null) {
      $result.depth = depth;
    }
    if (phoenixTimestamp != null) {
      $result.phoenixTimestamp = phoenixTimestamp;
    }
    if (zenId != null) {
      $result.zenId = zenId;
    }
    return $result;
  }
  ZenTick._() : super();
  factory ZenTick.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ZenTick.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ZenTick', package: const $pb.PackageName(_omitMessageNames ? '' : 'websocket'), createEmptyInstance: create)
    ..aOM<$0.BoolValue>(1, _omitFieldNames ? '' : 'tradable', subBuilder: $0.BoolValue.create)
    ..aOM<$0.StringValue>(2, _omitFieldNames ? '' : 'mode', subBuilder: $0.StringValue.create)
    ..aOM<$0.Int64Value>(3, _omitFieldNames ? '' : 'instrumentToken', subBuilder: $0.Int64Value.create)
    ..aOM<$0.DoubleValue>(4, _omitFieldNames ? '' : 'lastPrice', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.Int32Value>(5, _omitFieldNames ? '' : 'lastTradedQuantity', subBuilder: $0.Int32Value.create)
    ..aOM<$0.DoubleValue>(6, _omitFieldNames ? '' : 'averageTradedPrice', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.Int32Value>(7, _omitFieldNames ? '' : 'volumeTraded', subBuilder: $0.Int32Value.create)
    ..aOM<$0.Int32Value>(8, _omitFieldNames ? '' : 'totalBuyQuantity', subBuilder: $0.Int32Value.create)
    ..aOM<$0.Int32Value>(9, _omitFieldNames ? '' : 'totalSellQuantity', subBuilder: $0.Int32Value.create)
    ..aOM<$1.OHLC>(10, _omitFieldNames ? '' : 'ohlc', subBuilder: $1.OHLC.create)
    ..aOM<$0.DoubleValue>(11, _omitFieldNames ? '' : 'change', subBuilder: $0.DoubleValue.create)
    ..aOM<$2.Timestamp>(12, _omitFieldNames ? '' : 'lastTradeTime', subBuilder: $2.Timestamp.create)
    ..aOM<$0.Int32Value>(13, _omitFieldNames ? '' : 'oi', subBuilder: $0.Int32Value.create)
    ..aOM<$0.Int32Value>(14, _omitFieldNames ? '' : 'oiDayHigh', subBuilder: $0.Int32Value.create)
    ..aOM<$0.Int32Value>(15, _omitFieldNames ? '' : 'oiDayLow', subBuilder: $0.Int32Value.create)
    ..aOM<$2.Timestamp>(16, _omitFieldNames ? '' : 'exchangeTimestamp', subBuilder: $2.Timestamp.create)
    ..aOM<$3.Depth>(17, _omitFieldNames ? '' : 'depth', subBuilder: $3.Depth.create)
    ..aOM<$2.Timestamp>(18, _omitFieldNames ? '' : 'phoenixTimestamp', subBuilder: $2.Timestamp.create)
    ..aOM<$0.Int64Value>(19, _omitFieldNames ? '' : 'zenId', subBuilder: $0.Int64Value.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ZenTick clone() => ZenTick()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ZenTick copyWith(void Function(ZenTick) updates) => super.copyWith((message) => updates(message as ZenTick)) as ZenTick;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ZenTick create() => ZenTick._();
  ZenTick createEmptyInstance() => create();
  static $pb.PbList<ZenTick> createRepeated() => $pb.PbList<ZenTick>();
  @$core.pragma('dart2js:noInline')
  static ZenTick getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ZenTick>(create);
  static ZenTick? _defaultInstance;

  @$pb.TagNumber(1)
  $0.BoolValue get tradable => $_getN(0);
  @$pb.TagNumber(1)
  set tradable($0.BoolValue v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTradable() => $_has(0);
  @$pb.TagNumber(1)
  void clearTradable() => clearField(1);
  @$pb.TagNumber(1)
  $0.BoolValue ensureTradable() => $_ensure(0);

  @$pb.TagNumber(2)
  $0.StringValue get mode => $_getN(1);
  @$pb.TagNumber(2)
  set mode($0.StringValue v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasMode() => $_has(1);
  @$pb.TagNumber(2)
  void clearMode() => clearField(2);
  @$pb.TagNumber(2)
  $0.StringValue ensureMode() => $_ensure(1);

  @$pb.TagNumber(3)
  $0.Int64Value get instrumentToken => $_getN(2);
  @$pb.TagNumber(3)
  set instrumentToken($0.Int64Value v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasInstrumentToken() => $_has(2);
  @$pb.TagNumber(3)
  void clearInstrumentToken() => clearField(3);
  @$pb.TagNumber(3)
  $0.Int64Value ensureInstrumentToken() => $_ensure(2);

  @$pb.TagNumber(4)
  $0.DoubleValue get lastPrice => $_getN(3);
  @$pb.TagNumber(4)
  set lastPrice($0.DoubleValue v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasLastPrice() => $_has(3);
  @$pb.TagNumber(4)
  void clearLastPrice() => clearField(4);
  @$pb.TagNumber(4)
  $0.DoubleValue ensureLastPrice() => $_ensure(3);

  @$pb.TagNumber(5)
  $0.Int32Value get lastTradedQuantity => $_getN(4);
  @$pb.TagNumber(5)
  set lastTradedQuantity($0.Int32Value v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasLastTradedQuantity() => $_has(4);
  @$pb.TagNumber(5)
  void clearLastTradedQuantity() => clearField(5);
  @$pb.TagNumber(5)
  $0.Int32Value ensureLastTradedQuantity() => $_ensure(4);

  @$pb.TagNumber(6)
  $0.DoubleValue get averageTradedPrice => $_getN(5);
  @$pb.TagNumber(6)
  set averageTradedPrice($0.DoubleValue v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasAverageTradedPrice() => $_has(5);
  @$pb.TagNumber(6)
  void clearAverageTradedPrice() => clearField(6);
  @$pb.TagNumber(6)
  $0.DoubleValue ensureAverageTradedPrice() => $_ensure(5);

  @$pb.TagNumber(7)
  $0.Int32Value get volumeTraded => $_getN(6);
  @$pb.TagNumber(7)
  set volumeTraded($0.Int32Value v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasVolumeTraded() => $_has(6);
  @$pb.TagNumber(7)
  void clearVolumeTraded() => clearField(7);
  @$pb.TagNumber(7)
  $0.Int32Value ensureVolumeTraded() => $_ensure(6);

  @$pb.TagNumber(8)
  $0.Int32Value get totalBuyQuantity => $_getN(7);
  @$pb.TagNumber(8)
  set totalBuyQuantity($0.Int32Value v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasTotalBuyQuantity() => $_has(7);
  @$pb.TagNumber(8)
  void clearTotalBuyQuantity() => clearField(8);
  @$pb.TagNumber(8)
  $0.Int32Value ensureTotalBuyQuantity() => $_ensure(7);

  @$pb.TagNumber(9)
  $0.Int32Value get totalSellQuantity => $_getN(8);
  @$pb.TagNumber(9)
  set totalSellQuantity($0.Int32Value v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasTotalSellQuantity() => $_has(8);
  @$pb.TagNumber(9)
  void clearTotalSellQuantity() => clearField(9);
  @$pb.TagNumber(9)
  $0.Int32Value ensureTotalSellQuantity() => $_ensure(8);

  @$pb.TagNumber(10)
  $1.OHLC get ohlc => $_getN(9);
  @$pb.TagNumber(10)
  set ohlc($1.OHLC v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasOhlc() => $_has(9);
  @$pb.TagNumber(10)
  void clearOhlc() => clearField(10);
  @$pb.TagNumber(10)
  $1.OHLC ensureOhlc() => $_ensure(9);

  @$pb.TagNumber(11)
  $0.DoubleValue get change => $_getN(10);
  @$pb.TagNumber(11)
  set change($0.DoubleValue v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasChange() => $_has(10);
  @$pb.TagNumber(11)
  void clearChange() => clearField(11);
  @$pb.TagNumber(11)
  $0.DoubleValue ensureChange() => $_ensure(10);

  @$pb.TagNumber(12)
  $2.Timestamp get lastTradeTime => $_getN(11);
  @$pb.TagNumber(12)
  set lastTradeTime($2.Timestamp v) { setField(12, v); }
  @$pb.TagNumber(12)
  $core.bool hasLastTradeTime() => $_has(11);
  @$pb.TagNumber(12)
  void clearLastTradeTime() => clearField(12);
  @$pb.TagNumber(12)
  $2.Timestamp ensureLastTradeTime() => $_ensure(11);

  @$pb.TagNumber(13)
  $0.Int32Value get oi => $_getN(12);
  @$pb.TagNumber(13)
  set oi($0.Int32Value v) { setField(13, v); }
  @$pb.TagNumber(13)
  $core.bool hasOi() => $_has(12);
  @$pb.TagNumber(13)
  void clearOi() => clearField(13);
  @$pb.TagNumber(13)
  $0.Int32Value ensureOi() => $_ensure(12);

  @$pb.TagNumber(14)
  $0.Int32Value get oiDayHigh => $_getN(13);
  @$pb.TagNumber(14)
  set oiDayHigh($0.Int32Value v) { setField(14, v); }
  @$pb.TagNumber(14)
  $core.bool hasOiDayHigh() => $_has(13);
  @$pb.TagNumber(14)
  void clearOiDayHigh() => clearField(14);
  @$pb.TagNumber(14)
  $0.Int32Value ensureOiDayHigh() => $_ensure(13);

  @$pb.TagNumber(15)
  $0.Int32Value get oiDayLow => $_getN(14);
  @$pb.TagNumber(15)
  set oiDayLow($0.Int32Value v) { setField(15, v); }
  @$pb.TagNumber(15)
  $core.bool hasOiDayLow() => $_has(14);
  @$pb.TagNumber(15)
  void clearOiDayLow() => clearField(15);
  @$pb.TagNumber(15)
  $0.Int32Value ensureOiDayLow() => $_ensure(14);

  @$pb.TagNumber(16)
  $2.Timestamp get exchangeTimestamp => $_getN(15);
  @$pb.TagNumber(16)
  set exchangeTimestamp($2.Timestamp v) { setField(16, v); }
  @$pb.TagNumber(16)
  $core.bool hasExchangeTimestamp() => $_has(15);
  @$pb.TagNumber(16)
  void clearExchangeTimestamp() => clearField(16);
  @$pb.TagNumber(16)
  $2.Timestamp ensureExchangeTimestamp() => $_ensure(15);

  @$pb.TagNumber(17)
  $3.Depth get depth => $_getN(16);
  @$pb.TagNumber(17)
  set depth($3.Depth v) { setField(17, v); }
  @$pb.TagNumber(17)
  $core.bool hasDepth() => $_has(16);
  @$pb.TagNumber(17)
  void clearDepth() => clearField(17);
  @$pb.TagNumber(17)
  $3.Depth ensureDepth() => $_ensure(16);

  @$pb.TagNumber(18)
  $2.Timestamp get phoenixTimestamp => $_getN(17);
  @$pb.TagNumber(18)
  set phoenixTimestamp($2.Timestamp v) { setField(18, v); }
  @$pb.TagNumber(18)
  $core.bool hasPhoenixTimestamp() => $_has(17);
  @$pb.TagNumber(18)
  void clearPhoenixTimestamp() => clearField(18);
  @$pb.TagNumber(18)
  $2.Timestamp ensurePhoenixTimestamp() => $_ensure(17);

  @$pb.TagNumber(19)
  $0.Int64Value get zenId => $_getN(18);
  @$pb.TagNumber(19)
  set zenId($0.Int64Value v) { setField(19, v); }
  @$pb.TagNumber(19)
  $core.bool hasZenId() => $_has(18);
  @$pb.TagNumber(19)
  void clearZenId() => clearField(19);
  @$pb.TagNumber(19)
  $0.Int64Value ensureZenId() => $_ensure(18);
}

class ZenTicks extends $pb.GeneratedMessage {
  factory ZenTicks({
    $core.Iterable<ZenTick>? zenTicks,
  }) {
    final $result = create();
    if (zenTicks != null) {
      $result.zenTicks.addAll(zenTicks);
    }
    return $result;
  }
  ZenTicks._() : super();
  factory ZenTicks.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ZenTicks.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ZenTicks', package: const $pb.PackageName(_omitMessageNames ? '' : 'websocket'), createEmptyInstance: create)
    ..pc<ZenTick>(1, _omitFieldNames ? '' : 'zenTicks', $pb.PbFieldType.PM, subBuilder: ZenTick.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ZenTicks clone() => ZenTicks()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ZenTicks copyWith(void Function(ZenTicks) updates) => super.copyWith((message) => updates(message as ZenTicks)) as ZenTicks;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ZenTicks create() => ZenTicks._();
  ZenTicks createEmptyInstance() => create();
  static $pb.PbList<ZenTicks> createRepeated() => $pb.PbList<ZenTicks>();
  @$core.pragma('dart2js:noInline')
  static ZenTicks getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ZenTicks>(create);
  static ZenTicks? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<ZenTick> get zenTicks => $_getList(0);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
