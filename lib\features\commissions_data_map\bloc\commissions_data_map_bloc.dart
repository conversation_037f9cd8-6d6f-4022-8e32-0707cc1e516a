import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/commissions_data_map/provider/commissions_provider.dart';

part 'commissions_data_map_event.dart';
part 'commissions_data_map_state.dart';

class CommissionsDataMapBloc extends Bloc<CommissionsDataMapEvent, CommissionsDataMapState> {
  final CommissionsProvider _commissionsProvider;

  CommissionsDataMapBloc(this._commissionsProvider) : super(CommissionsDataMapInitial()) {
    on<FetchCommissionsData>(_onFetchCommissionsData);
  }

  void _onFetchCommissionsData(
    FetchCommissionsData event,
    Emitter<CommissionsDataMapState> emit,
  ) async {
    emit(CommissionsDataMapLoading());
    debugPrint("Reached _onFetchCommissionsData");
    try {
      final data = await _commissionsProvider.getCharges();
      emit(CommissionsDataMapLoaded(data));
    } catch (e) {
      emit(CommissionsDataMapError(e.toString()));
    }
  }
}
