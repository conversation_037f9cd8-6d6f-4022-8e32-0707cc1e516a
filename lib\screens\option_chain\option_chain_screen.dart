import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/option_greeks_websocket/bloc/option_greeks_websocket_bloc.dart';
import 'package:phoenix/features/option_greeks_rest/bloc/option_greeks_rest_bloc.dart';
import 'package:phoenix/features/option_greeks_rest/utils/option_greeks_converter.dart';
import 'package:phoenix/features/option_greeks_rest/data/provider/option_greeks_rest_provider.dart';
import 'package:phoenix/features/orders/model/spider_order_data.dart';
import 'package:phoenix/features/websocket/model/proto/option_greeks/option_greeks.pb.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/screens/option_chain/widgets/greeks_icon.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/http_service.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/screens/orders/order_form_bottom_sheet.dart';
import 'package:phoenix/screens/orders/order_type.dart';
import 'package:phoenix/services/watchlist_service.dart';
import 'package:phoenix/widgets/order_form/security_list_dropdown/security_list_text_formatter.dart';

import 'package:phoenix/widgets/toast/toast_utils.dart';
import 'package:phoenix/widgets/toast/custom_toast.dart';

class OptionChainScreen extends StatefulWidget {
  const OptionChainScreen({super.key});

  @override
  State<OptionChainScreen> createState() => _OptionChainScreenState();
}

class _OptionChainScreenState extends State<OptionChainScreen> with TickerProviderStateMixin {
  String? selectedUnderlyingId;
  String searchQuery = 'NIFTY 50';
  late TabController _tabController;
  late TabController _greeksTabController;
  List<DateTime> expiryDates = [];
  DateTime? selectedExpiry;
  bool _sortAscending = true;
  String _selectedGreek = 'OI'; // OI, IV, Delta, Gamma, Theta, Vega
  bool _showAbsoluteChange = true; // true for absolute, false for percentage
  bool _inlineScrollingEnabled = false; // true for inline scrolling mode
  
  // Sorting state for LTP columns
  String _sortBy = 'Strike'; // Strike, CallLTP, PutLTP, or Greek name
  bool _ltpSortAscending = true;

  final List<String> _greekOptions = ['OI', 'IV', 'Delta', 'Gamma', 'Theta', 'Vega'];

  // Store reference to the blocs to avoid context access in dispose
  OptionGreeksWebSocketBloc? _optionGreeksBloc;
  OptionGreeksRestBloc? _optionGreeksRestBloc;
  WebSocketBloc? _webSocketBloc;
  
  // Track data source and connection status
  bool _isUsingRestFallback = false;
  
  // Store live prices from WebSocket
  Map<int, double> _livePrices = {};
  Map<int, double> _previousPrices = {};
  bool _isPriceWebSocketConnected = false;
  
  // Store SOD prices for accurate price change calculation
  Map<int, double> _sodPrices = {};
  bool _isSodPricesLoaded = false;
  
  // Option greeks REST provider for fetching initial data
  final OptionGreeksRestProvider _optionGreeksRestProvider = OptionGreeksRestProvider();
  
  // Scroll controllers for synchronized scrolling
  late ScrollController _leftScrollController;
  late ScrollController _rightScrollController;
  late ScrollController _verticalScrollController; // Single controller for all vertical scrolling
  
  // Additional vertical scroll controllers for inline scrolling mode
  late ScrollController _leftVerticalScrollController;
  late ScrollController _strikeVerticalScrollController;
  late ScrollController _rightVerticalScrollController;
  
  // Initial scroll positions for proper column alignment
  double _initialLeftScrollPosition = 0.0;
  double _initialRightScrollPosition = 0.0;

  // Available underlying assets for option chain
  final List<Map<String, dynamic>> _availableUnderlyings = [
    {
      'name': 'NIFTY 50',
      'symbol': 'NIFTY',
      'zenId': '37761',
      'price': 24866.65,
      'change': -195.45,
      'changePercent': -0.77,
      
    },
    {
      'name': 'BANK NIFTY',
      'symbol': 'BANKNIFTY',
      'zenId': '37766',
      'price': 51234.80,
      'change': 234.50,
      'changePercent': 0.46,
    },
  ];

  // Mock data for demonstration - replace with actual data
  final Map<String, dynamic> mockData = {
    'NIFTY 50': {
      'price': 24866.65,
      'change': -195.45,
      'changePercent': -0.77,
    },
    'BANK NIFTY': {
      'price': 51234.80,
      'change': 234.50,
      'changePercent': 0.46,
    }
  };

  @override
  void initState() {
    super.initState();

    // Initialize with default underlying (NIFTY zen ID)
    selectedUnderlyingId = '37761';

    // Initialize with empty tab controller
    _tabController = TabController(length: 0, vsync: this);
    _greeksTabController = TabController(length: _greekOptions.length, vsync: this);
    
    // Initialize scroll controllers for synchronized scrolling
    _leftScrollController = ScrollController();
    _rightScrollController = ScrollController();
    _verticalScrollController = ScrollController();
    
    // Initialize additional vertical scroll controllers for inline scrolling mode
    _leftVerticalScrollController = ScrollController();
    _strikeVerticalScrollController = ScrollController();
    _rightVerticalScrollController = ScrollController();
    
    // Add listeners for synchronized scrolling
    _leftScrollController.addListener(_onLeftHorizontalScroll);
    _rightScrollController.addListener(_onRightHorizontalScroll);
    
    // Add listeners for vertical scroll synchronization in inline mode
    _leftVerticalScrollController.addListener(_onLeftVerticalScroll);
    _strikeVerticalScrollController.addListener(_onStrikeVerticalScroll);
    _rightVerticalScrollController.addListener(_onRightVerticalScroll);
    
    // Initialize scroll positions for proper column alignment
    _initializeScrollPositions();
    
    // Fetch SOD prices for option chain data on initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchSodPricesForOptionChain();
    });
  }
  
  void _onLeftHorizontalScroll() {
    if (!_inlineScrollingEnabled || _isRightScrollSyncing) return;
    
    if (_leftScrollController.hasClients && _rightScrollController.hasClients) {
      final leftOffset = _leftScrollController.offset;
      final leftMaxScroll = _leftScrollController.position.maxScrollExtent;
      final rightMaxScroll = _rightScrollController.position.maxScrollExtent;
      
      // Only apply mirroring if both controllers have valid scroll extents
      if (leftMaxScroll > 0 && rightMaxScroll > 0) {
        // Calculate mirrored position for right side
        // When left scrolls right, right scrolls left (mirrored)
        final scrollRatio = leftOffset / leftMaxScroll;
        final mirroredOffset = rightMaxScroll * (1.0 - scrollRatio);
        final clampedOffset = mirroredOffset.clamp(0.0, rightMaxScroll);
        
        // Only update if the difference is significant
        if ((clampedOffset - _rightScrollController.offset).abs() > 2.0) {
          _isLeftScrollSyncing = true;
          _rightScrollController.removeListener(_onRightHorizontalScroll);
          try {
            _rightScrollController.jumpTo(clampedOffset);
          } catch (e) {
            // Handle any scroll controller errors gracefully
          } finally {
            _rightScrollController.addListener(_onRightHorizontalScroll);
            _isLeftScrollSyncing = false;
          }
        }
      }
    }
  }
  
  void _onRightHorizontalScroll() {
    if (!_inlineScrollingEnabled || _isLeftScrollSyncing) return;
    
    if (_leftScrollController.hasClients && _rightScrollController.hasClients) {
      final rightOffset = _rightScrollController.offset;
      final rightMaxScroll = _rightScrollController.position.maxScrollExtent;
      final leftMaxScroll = _leftScrollController.position.maxScrollExtent;
      
      // Only apply mirroring if both controllers have valid scroll extents
      if (leftMaxScroll > 0 && rightMaxScroll > 0) {
        // Calculate mirrored position for left side
        // When right scrolls right, left scrolls left (mirrored)
        final scrollRatio = rightOffset / rightMaxScroll;
        final mirroredOffset = leftMaxScroll * (1.0 - scrollRatio);
        final clampedOffset = mirroredOffset.clamp(0.0, leftMaxScroll);
        
        // Only update if the difference is significant
        if ((clampedOffset - _leftScrollController.offset).abs() > 2.0) {
          _isRightScrollSyncing = true;
          _leftScrollController.removeListener(_onLeftHorizontalScroll);
          try {
            _leftScrollController.jumpTo(clampedOffset);
          } catch (e) {
            // Handle any scroll controller errors gracefully
          } finally {
            _leftScrollController.addListener(_onLeftHorizontalScroll);
            _isRightScrollSyncing = false;
          }
        }
      }
    }
  }
  
  // Track if initial positions have been set to prevent repeated resets
  bool _initialPositionsSet = false;
  
  // Track scroll synchronization state to prevent conflicts
  bool _isLeftScrollSyncing = false;
  bool _isRightScrollSyncing = false;
  
  // Initialize scroll positions for proper column alignment in inline mode
  void _initializeScrollPositions() {
    // Only set initial positions once to prevent scroll resets
    if (_initialPositionsSet || !_inlineScrollingEnabled) return;
    
    // Calculate initial positions to show LTP columns properly
    // Left side: scroll to show Call LTP (last column) prominently
    const columnWidth = 70.0;
    const leftColumns = 7; // Gamma, Vega, Theta, Delta, OI, IV, Call LTP
    _initialLeftScrollPosition = (leftColumns - 3) * columnWidth; // Show OI, IV, Call LTP
    
    // Right side: start from beginning to show Put LTP first
    _initialRightScrollPosition = 0.0;
    
    // Apply initial positions when controllers are ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_leftScrollController.hasClients && _rightScrollController.hasClients && !_initialPositionsSet) {
        _leftScrollController.jumpTo(_initialLeftScrollPosition);
        _rightScrollController.jumpTo(_initialRightScrollPosition);
        _initialPositionsSet = true;
      }
    });
  }
  
  // Reset scroll positions to default
  void _resetScrollPositions() {
    _initialPositionsSet = false;
    _isLeftScrollSyncing = false;
    _isRightScrollSyncing = false;
    
    if (_leftScrollController.hasClients) {
      _leftScrollController.jumpTo(0.0);
    }
    if (_rightScrollController.hasClients) {
      _rightScrollController.jumpTo(0.0);
    }
  }

  // Helper method to calculate price change using SOD prices
  double _calculatePriceChange(int zenId, double currentPrice) {
    final sodPrice = _sodPrices[zenId];
    if (sodPrice != null && sodPrice > 0) {
      return currentPrice - sodPrice;
    }
    
    // Fallback to mock data for initial change calculation
    final underlying = _availableUnderlyings.firstWhere(
      (u) => int.parse(u['zenId']) == zenId,
      orElse: () => {},
    );
    return underlying['change'] ?? 0.0;
  }

  // Helper method to calculate percentage change using SOD prices
  double _calculatePercentageChange(int zenId, double currentPrice) {
    final sodPrice = _sodPrices[zenId];
    if (sodPrice != null && sodPrice > 0) {
      return ((currentPrice - sodPrice) / sodPrice) * 100;
    }
    
    // Fallback to mock data for initial percentage calculation
    final underlying = _availableUnderlyings.firstWhere(
      (u) => int.parse(u['zenId']) == zenId,
      orElse: () => {},
    );
    return underlying['changePercent'] ?? 0.0;
  }

  // Helper method to get current price for a zen ID (from live prices or fallback)
  double _getCurrentPrice(int zenId) {
    // First try to get from live WebSocket prices
    final livePrice = _livePrices[zenId];
    if (livePrice != null) {
      return livePrice;
    }
    
    // Fallback to mock data for underlying assets
    final underlying = _availableUnderlyings.firstWhere(
      (u) => int.parse(u['zenId']) == zenId,
      orElse: () => {},
    );
    return underlying['price']?.toDouble() ?? 0.0;
  }

  // Helper method to check if SOD price is available for a zen ID
  bool _hasSodPrice(int zenId) {
    return _sodPrices.containsKey(zenId) && _sodPrices[zenId]! > 0;
  }

  // Build LTP cell with price change indicator
  Widget _buildLtpCell(ZenOptionGreeks? option, double width, ThemeState themeState) {
    if (option == null || !option.hasLastPrice()) {
      return _buildInlineCell('0.00', width, themeState, option);
    }

    final zenId = option.zenId.value.toInt();
    final currentPrice = option.lastPrice.value;
    final priceChange = _calculatePriceChange(zenId, currentPrice);
    final percentChange = _calculatePercentageChange(zenId, currentPrice);
    
    // Determine color based on price change
    Color changeColor = AppTheme.textSecondary(themeState.isDarkMode);
    if (priceChange > 0) {
      changeColor = Colors.green;
    } else if (priceChange < 0) {
      changeColor = Colors.red;
    }

    Widget cellContent = Container(
      width: width,
      height: 50,
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(
            color: AppTheme.borderColor(themeState.isDarkMode).withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // LTP
          Text(
            currentPrice.toStringAsFixed(2),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary(themeState.isDarkMode),
            ),
            textAlign: TextAlign.center,
          ),
          // Price change (only show if SOD price is available)
          if (_hasSodPrice(zenId)) ...[
            const SizedBox(height: 2),
            Text(
              _showAbsoluteChange
                  ? '${priceChange >= 0 ? '+' : ''}${priceChange.toStringAsFixed(2)}'
                  : '${percentChange >= 0 ? '+' : ''}${percentChange.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: changeColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );

    // Make the cell clickable
    return GestureDetector(
      onTap: () => _showOptionDetails(option, themeState),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.transparent,
        ),
        child: cellContent,
      ),
    );
  }

  // Fetch SOD prices for option chain data and underlying assets
  Future<void> _fetchSodPricesForOptionChain() async {
    try {
      debugPrint('🔄 Fetching option chain data to extract zen IDs...');
      
      // First, fetch option chain data from REST API to get all zen IDs
      final optionGreeksResponse = await _optionGreeksRestProvider.fetchOptionGreeks();
      
      // Extract all zen IDs from option chain data
      final Set<int> allZenIds = {};
      
      // Add option zen IDs
      for (final option in optionGreeksResponse.payload) {
        if (option.zenId != 0) {
          allZenIds.add(option.zenId);
        }
        // Also add underlying zen IDs
        if (option.underlyingZenId != 0) {
          allZenIds.add(option.underlyingZenId);
        }
      }
      
      // Add underlying zen IDs from available underlyings
      for (final underlying in _availableUnderlyings) {
        final zenId = int.tryParse(underlying['zenId']);
        if (zenId != null && zenId != 0) {
          allZenIds.add(zenId);
        }
      }
      
      debugPrint('📊 Found ${allZenIds.length} unique zen IDs for SOD price fetch');
      
      if (allZenIds.isEmpty) {
        debugPrint('⚠️ No zen IDs found for SOD price fetch');
        return;
      }
      
      // Fetch SOD prices for all zen IDs
      await _fetchSodPrices(allZenIds.toList());
      
    } catch (e) {
      debugPrint('❌ Error fetching SOD prices for option chain: $e');
    }
  }

  // Fetch SOD prices from API
  Future<void> _fetchSodPrices(List<int> zenIds) async {
    if (zenIds.isEmpty) return;

    try {
      debugPrint('🌐 Fetching SOD prices for ${zenIds.length} zen IDs...');
      
      final customHttpService = HttpService();
      final response = await customHttpService.post(
        Uri.parse(ApiPath.getSodPrice()),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'zen_ids': zenIds}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 'SUCCESS' && data['payload'] != null) {
          final List<dynamic> payload = data['payload'];
          _sodPrices.clear();
          
          for (final item in payload) {
            final zenId = item['zen_id'] as int;
            final price = (item['price'] as num).toDouble();
            _sodPrices[zenId] = price;
          }
          
          _isSodPricesLoaded = true;
          debugPrint('✅ Successfully loaded ${_sodPrices.length} SOD prices');
          debugPrint('📈 SOD prices: $_sodPrices');
          
          // Trigger UI update if mounted
          if (mounted) {
            setState(() {});
          }
        } else {
          debugPrint('⚠️ SOD price API returned unsuccessful status: ${data['status']}');
        }
      } else {
        debugPrint('❌ Failed to fetch SOD prices: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('💥 Error fetching SOD prices: $e');
    }
  }
  
  // Vertical scroll synchronization methods for inline scrolling mode
  void _onLeftVerticalScroll() {
    if (!_inlineScrollingEnabled) return;
    
    final offset = _leftVerticalScrollController.offset;
    
    // Sync with strike column
    if (_strikeVerticalScrollController.hasClients && 
        (_strikeVerticalScrollController.offset - offset).abs() > 1.0) {
      _strikeVerticalScrollController.removeListener(_onStrikeVerticalScroll);
      _strikeVerticalScrollController.jumpTo(offset);
      _strikeVerticalScrollController.addListener(_onStrikeVerticalScroll);
    }
    
    // Sync with right section
    if (_rightVerticalScrollController.hasClients && 
        (_rightVerticalScrollController.offset - offset).abs() > 1.0) {
      _rightVerticalScrollController.removeListener(_onRightVerticalScroll);
      _rightVerticalScrollController.jumpTo(offset);
      _rightVerticalScrollController.addListener(_onRightVerticalScroll);
    }
  }
  
  void _onStrikeVerticalScroll() {
    if (!_inlineScrollingEnabled) return;
    
    final offset = _strikeVerticalScrollController.offset;
    
    // Sync with left section
    if (_leftVerticalScrollController.hasClients && 
        (_leftVerticalScrollController.offset - offset).abs() > 1.0) {
      _leftVerticalScrollController.removeListener(_onLeftVerticalScroll);
      _leftVerticalScrollController.jumpTo(offset);
      _leftVerticalScrollController.addListener(_onLeftVerticalScroll);
    }
    
    // Sync with right section
    if (_rightVerticalScrollController.hasClients && 
        (_rightVerticalScrollController.offset - offset).abs() > 1.0) {
      _rightVerticalScrollController.removeListener(_onRightVerticalScroll);
      _rightVerticalScrollController.jumpTo(offset);
      _rightVerticalScrollController.addListener(_onRightVerticalScroll);
    }
  }
  
  void _onRightVerticalScroll() {
    if (!_inlineScrollingEnabled) return;
    
    final offset = _rightVerticalScrollController.offset;
    
    // Sync with left section
    if (_leftVerticalScrollController.hasClients && 
        (_leftVerticalScrollController.offset - offset).abs() > 1.0) {
      _leftVerticalScrollController.removeListener(_onLeftVerticalScroll);
      _leftVerticalScrollController.jumpTo(offset);
      _leftVerticalScrollController.addListener(_onLeftVerticalScroll);
    }
    
    // Sync with strike column
    if (_strikeVerticalScrollController.hasClients && 
        (_strikeVerticalScrollController.offset - offset).abs() > 1.0) {
      _strikeVerticalScrollController.removeListener(_onStrikeVerticalScroll);
      _strikeVerticalScrollController.jumpTo(offset);
      _strikeVerticalScrollController.addListener(_onStrikeVerticalScroll);
    }
  }
  


  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Store bloc references safely
    _optionGreeksBloc = context.read<OptionGreeksWebSocketBloc>();
    _optionGreeksRestBloc = context.read<OptionGreeksRestBloc>();
    _webSocketBloc = context.read<WebSocketBloc>();

    // Start with REST API fallback immediately
    _optionGreeksRestBloc?.add(OptionGreeksRestFetch());
    _isUsingRestFallback = true;

    // Try to connect to WebSocket
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      _optionGreeksBloc?.add(
        OptionGreeksWebSocketConnect(authState.credentialsModel.accessToken)
      );
      
      // Connect to price WebSocket and subscribe to all underlying assets
      _webSocketBloc?.add(WebSocketConnect(authState.credentialsModel.accessToken));
      
      // Subscribe to all underlying assets for live price updates
      final zenIds = _availableUnderlyings.map((u) => int.parse(u['zenId'])).toList();
      debugPrint('🔥 Subscribing to WebSocket for zenIds: $zenIds');
      _webSocketBloc?.add(WebSocketSelectMultipleStocks(zenIds));
      
      // Initialize previous prices with mock data for baseline calculations
      for (final underlying in _availableUnderlyings) {
        final zenId = int.parse(underlying['zenId']);
        _previousPrices[zenId] = underlying['price'];
      }
    }
    
    // Trigger initial underlying selection
    if (selectedUnderlyingId != null) {
      _optionGreeksBloc?.add(OptionGreeksWebSocketSelectUnderlying(selectedUnderlyingId!));
    }
  }

  void _initializeExpiryDates(List<ZenOptionGreeks> optionGreeksList) {
    final Set<DateTime> uniqueExpiries = {};
    
    for (final option in optionGreeksList) {
      if (option.hasExpiry()) {
        final expiry = DateTime(
          option.expiry.year,
          option.expiry.month,
          option.expiry.day,
        );
        uniqueExpiries.add(expiry);
      }
    }
    
    final sortedExpiries = uniqueExpiries.toList()..sort();
    
    if (expiryDates.length != sortedExpiries.length || 
        !expiryDates.every((date) => sortedExpiries.contains(date))) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            expiryDates = sortedExpiries;
            if (expiryDates.isNotEmpty && selectedExpiry == null) {
              selectedExpiry = expiryDates.first;
            }
            if (_tabController.length != expiryDates.length) {
              _tabController.dispose(); // Dispose old controller
              _tabController = TabController(length: expiryDates.length, vsync: this);
            }
          });
        }
      });
    }
  }

  List<ZenOptionGreeks> _filterByExpiry(List<ZenOptionGreeks> optionGreeksList) {
    if (selectedExpiry == null) return optionGreeksList;

    return optionGreeksList.where((option) {
      if (!option.hasExpiry()) return false;

      final optionExpiry = DateTime(
        option.expiry.year,
        option.expiry.month,
        option.expiry.day,
      );

      return optionExpiry.isAtSameMomentAs(selectedExpiry!);
    }).toList();
  }

  List<ZenOptionGreeks> _filterByUnderlying(List<ZenOptionGreeks> optionGreeksList) {
    if (selectedUnderlyingId == null) return optionGreeksList;

    return optionGreeksList.where((option) {
      if (!option.hasUnderlyingZenId()) return false;

      final underlyingZenId = option.underlyingZenId.value.toString();

      // Filter based on selected underlying zen ID
      return underlyingZenId == selectedUnderlyingId;
    }).toList();
  }

  String _formatExpiryDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    final monthStr = months[date.month - 1];
    final dayStr = date.day.toString().padLeft(2, '0');

    // Format as a single line with clear, concise format
    if (difference == 0) {
      return '$dayStr $monthStr (Today)';
    } else if (difference == 1) {
      return '$dayStr $monthStr (Tomorrow)';
    } else if (difference <= 7) {
      return '$dayStr $monthStr (${difference}d)';
    } else if (difference <= 30) {
      final weeks = (difference / 7).round();
      return '$dayStr $monthStr (${weeks}w)';
    } else {
      final months = (difference / 30).round();
      return '$dayStr $monthStr (${months}m)';
    }
  }

  String _getGreekLabel() {
    switch (_selectedGreek) {
      case 'OI':
        return 'OI';
      case 'IV':
        return 'IV (%)';
      case 'Delta':
        return 'Delta';
      case 'Gamma':
        return 'Gamma';
      case 'Theta':
        return 'Theta';
      case 'Vega':
        return 'Vega';
      default:
        return 'OI';
    }
  }

  String _getGreekValue(ZenOptionGreeks? option) {
    if (option == null) return '0.00';
    
    switch (_selectedGreek) {
      case 'OI':
        // Return actual OI value from protobuf
        final oi = option.hasOi() ? option.oi.value.toDouble() : 0.0;
        return oi.toStringAsFixed(0); // OI is typically shown as whole numbers
      case 'IV':
        return option.hasImpliedVolatility() 
            ? (option.impliedVolatility.value * 100).toStringAsFixed(2)
            : '0.00';
      case 'Delta':
        return option.hasDelta() 
            ? option.delta.value.toStringAsFixed(4)
            : '0.0000';
      case 'Gamma':
        return option.hasGamma() 
            ? option.gamma.value.toStringAsFixed(4)
            : '0.0000';
      case 'Theta':
        return option.hasTheta() 
            ? option.theta.value.toStringAsFixed(4)
            : '0.0000';
      case 'Vega':
        return option.hasVega() 
            ? option.vega.value.toStringAsFixed(4)
            : '0.0000';
      default:
        return '0.00';
    }
  }

  double _getGreekPercentage(ZenOptionGreeks? option, double strike) {
    if (option == null) return 0.0;
    
    switch (_selectedGreek) {
      case 'OI':
        // For OI percentage, calculate change from day low to current
        final currentOi = option.hasOi() ? option.oi.value.toDouble() : 0.0;
        final dayLowOi = option.hasOiDayLow() ? option.oiDayLow.value.toDouble() : 0.0;
        return dayLowOi > 0 ? ((currentOi - dayLowOi) / dayLowOi * 100) : 0.0;
      case 'IV':
        final iv = option.hasImpliedVolatility() ? option.impliedVolatility.value : 0.0;
        return iv > 0 ? ((iv - 0.15) / 0.15 * 100) : 0.0;
      case 'Delta':
        final delta = option.hasDelta() ? option.delta.value : 0.0;
        return delta * 100; // Delta as percentage
      case 'Gamma':
        final gamma = option.hasGamma() ? option.gamma.value : 0.0;
        return gamma * 1000; // Gamma scaled for percentage display
      case 'Theta':
        final theta = option.hasTheta() ? option.theta.value : 0.0;
        return theta * 100; // Theta as percentage
      case 'Vega':
        final vega = option.hasVega() ? option.vega.value : 0.0;
        return vega * 10; // Vega scaled for percentage display
      default:
        return 0.0;
    }
  }

  ZenOptionGreeks? _getPrimaryOptionForSorting(ZenOptionGreeks? callOption, ZenOptionGreeks? putOption, double strike) {
    // For sorting, prefer the option with higher value or the call option if both are similar
    if (callOption == null && putOption == null) return null;
    if (callOption == null) return putOption;
    if (putOption == null) return callOption;

    final callValue = _getGreekValueForSorting(callOption, strike);
    final putValue = _getGreekValueForSorting(putOption, strike);

    return callValue >= putValue ? callOption : putOption;
  }

  // Generic sorting function that can sort by different criteria
  List<double> _sortStrikes(Map<double, Map<String, ZenOptionGreeks>> groupedOptions, String sortBy, bool ascending) {
    final strikes = groupedOptions.keys.toList();
    
    strikes.sort((a, b) {
      int comparison = 0;
      
      switch (sortBy) {
        case 'Strike':
          comparison = a.compareTo(b);
          break;
        case 'CallLTP':
          final callA = groupedOptions[a]?['call'];
          final callB = groupedOptions[b]?['call'];
          final ltpA = callA?.hasLastPrice() == true ? callA!.lastPrice.value : 0.0;
          final ltpB = callB?.hasLastPrice() == true ? callB!.lastPrice.value : 0.0;
          comparison = ltpA.compareTo(ltpB);
          break;
        case 'PutLTP':
          final putA = groupedOptions[a]?['put'];
          final putB = groupedOptions[b]?['put'];
          final ltpA = putA?.hasLastPrice() == true ? putA!.lastPrice.value : 0.0;
          final ltpB = putB?.hasLastPrice() == true ? putB!.lastPrice.value : 0.0;
          comparison = ltpA.compareTo(ltpB);
          break;
        default:
          // Sort by Greek value - handle both regular mode and inline mode Greek columns
          if (['Gamma', 'Vega', 'Theta', 'Delta', 'OI', 'IV'].contains(sortBy)) {
            // For inline mode Greek columns, get values from both call and put options and use the higher value
            final callA = groupedOptions[a]?['call'];
            final callB = groupedOptions[b]?['call'];
            final putA = groupedOptions[a]?['put'];
            final putB = groupedOptions[b]?['put'];
            
            final callValueA = _getGreekValueByColumnName(callA, sortBy);
            final putValueA = _getGreekValueByColumnName(putA, sortBy);
            final maxValueA = math.max(callValueA.abs(), putValueA.abs());
            
            final callValueB = _getGreekValueByColumnName(callB, sortBy);
            final putValueB = _getGreekValueByColumnName(putB, sortBy);
            final maxValueB = math.max(callValueB.abs(), putValueB.abs());
            
            comparison = maxValueA.compareTo(maxValueB);
          } else {
            // Sort by selected Greek value (for regular mode)
            final optionA = _getPrimaryOptionForSorting(groupedOptions[a]?['call'], groupedOptions[a]?['put'], a);
            final optionB = _getPrimaryOptionForSorting(groupedOptions[b]?['call'], groupedOptions[b]?['put'], b);
            final valueA = _getGreekValueForSorting(optionA, a);
            final valueB = _getGreekValueForSorting(optionB, b);
            comparison = valueA.compareTo(valueB);
          }
          break;
      }
      
      return ascending ? comparison : -comparison;
    });
    
    return strikes;
  }

  double _getGreekValueForSorting(ZenOptionGreeks? option, double strike) {
    if (option == null) return 0.0;

    switch (_selectedGreek) {
      case 'OI':
        // Return actual OI value from protobuf
        return option.hasOi() ? option.oi.value.toDouble() : 0.0;
      case 'IV':
        return option.hasImpliedVolatility()
            ? option.impliedVolatility.value * 100
            : 0.0;
      case 'Delta':
        return option.hasDelta()
            ? option.delta.value.abs()
            : 0.0;
      case 'Gamma':
        return option.hasGamma()
            ? option.gamma.value.abs()
            : 0.0;
      case 'Theta':
        return option.hasTheta()
            ? option.theta.value.abs()
            : 0.0;
      case 'Vega':
        return option.hasVega()
            ? option.vega.value.abs()
            : 0.0;
      default:
        return 0.0;
    }
  }

  Widget _buildFlowingIndicatorLine(double callPercentage, double putPercentage, ThemeState themeState) {
    // Calculate the width of each side based on percentage (max 80px each side for better proportion)
    const maxWidth = 80.0;
    final leftWidth = (callPercentage.abs() / 50 * maxWidth).clamp(0.0, maxWidth); // Scale based on 50% max
    final rightWidth = (putPercentage.abs() / 50 * maxWidth).clamp(0.0, maxWidth);

    // Determine colors based on positive/negative values with better contrast
    final leftColor = callPercentage >= 0 ? Colors.green.shade600 : Colors.red.shade600;
    final rightColor = putPercentage >= 0 ? Colors.green.shade600 : Colors.red.shade600;

    return SizedBox(
      height: 2,
      child: Row(
        children: [
          // Left side (Call) - grows from center to left
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  height: 2,
                  width: leftWidth,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        leftColor.withValues(alpha: 0.3),
                        leftColor,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              ],
            ),
          ),
          // Center point (white gap)
          Container(
            width: 4,
            height: 2,
            color: Colors.white,
          ),
          // Right side (Put) - grows from center to right
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  height: 2,
                  width: rightWidth,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        rightColor,
                        rightColor.withValues(alpha: 0.3),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showUnderlyingSelector(ThemeState themeState) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor(themeState.isDarkMode),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 20),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              child: Row(
                children: [
                  Text(
                    'Select Underlying',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                  ),
                ],
              ),
            ),
            // Options list
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _availableUnderlyings.length,
              itemBuilder: (context, index) {
                final underlying = _availableUnderlyings[index];
                final isSelected = searchQuery == underlying['name'];

                return ListTile(
                  onTap: () {
                    setState(() {
                      searchQuery = underlying['name'];
                      selectedUnderlyingId = underlying['zenId'];
                      // Reset expiry selection when changing underlying
                      selectedExpiry = null;
                      expiryDates.clear();
                    });
                    
                    // Trigger underlying selection in WebSocket bloc
                    _optionGreeksBloc?.add(OptionGreeksWebSocketSelectUnderlying(selectedUnderlyingId!));
                    
                    // Also select this specific stock for price updates
                    _webSocketBloc?.add(WebSocketSelectStock(selectedUnderlyingId!));
                    
                    // Fetch SOD prices for the new underlying and its options
                    _fetchSodPricesForOptionChain();
                    
                    Navigator.pop(context);
                  },
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.2)
                          : AppTheme.cardColor(themeState.isDarkMode),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected
                            ? AppTheme.primaryColor(themeState.isDarkMode)
                            : AppTheme.borderColor(themeState.isDarkMode),
                      ),
                    ),
                    child: Center(
                      child: Text(
                        underlying['symbol'].substring(0, 2),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: isSelected
                              ? AppTheme.primaryColor(themeState.isDarkMode)
                              : AppTheme.textSecondary(themeState.isDarkMode),
                        ),
                      ),
                    ),
                  ),
                  title: Text(
                    underlying['name'],
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                  ),
                  subtitle: Builder(
                    builder: (context) {
                      // Get live price from WebSocket or fallback to mock data
                      final zenId = int.parse(underlying['zenId']);
                      final livePrice = _livePrices[zenId];
                      final currentPrice = livePrice ?? underlying['price'];
                      final change = livePrice != null ? _calculatePriceChange(zenId, livePrice) : underlying['change'];
                      final changePercent = livePrice != null ? _calculatePercentageChange(zenId, livePrice) : underlying['changePercent'];
                      
                      return Row(
                        children: [
                          // Show live indicator if using WebSocket data
                          if (livePrice != null && _isPriceWebSocketConnected) ...[
                            Container(
                              width: 6,
                              height: 6,
                              decoration: const BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 6),
                          ],
                          Expanded(
                            child: Text(
                              '${currentPrice.toStringAsFixed(2)} (${change >= 0 ? '+' : ''}${change.toStringAsFixed(2)} | ${changePercent.toStringAsFixed(2)}%)',
                              style: TextStyle(
                                fontSize: 14,
                                color: change >= 0 ? Colors.green : Colors.red,
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  trailing: isSelected
                      ? Icon(
                          Icons.check_circle,
                          color: AppTheme.primaryColor(themeState.isDarkMode),
                        )
                      : null,
                );
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showOptionsMenu(ThemeState themeState) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor(themeState.isDarkMode),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 20),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              child: Row(
                children: [
                  Text(
                    'Display Options',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                  ),
                ],
              ),
            ),
            // Price Change Display Option
            ListTile(
              leading: Icon(
                _showAbsoluteChange ? Icons.currency_rupee : Icons.percent,
                color: AppTheme.primaryColor(themeState.isDarkMode),
              ),
              title: Text(
                'Price Change Display',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                ),
              ),
              subtitle: Text(
                _showAbsoluteChange ? 'Absolute Change' : 'Percentage Change',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                ),
              ),
              trailing: Switch(
                value: _showAbsoluteChange,
                onChanged: (value) {
                  setState(() {
                    _showAbsoluteChange = value;
                  });
                  Navigator.pop(context);
                },
                activeColor: AppTheme.primaryColor(themeState.isDarkMode),
              ),
            ),
            // Inline Scrolling Option
            ListTile(
              leading: Icon(
                _inlineScrollingEnabled ? Icons.view_column : Icons.table_chart,
                color: AppTheme.primaryColor(themeState.isDarkMode),
              ),
              title: Text(
                'Inline Scrolling',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                ),
              ),
              subtitle: Text(
                _inlineScrollingEnabled ? 'All Greeks in table' : 'Tab-based Greeks',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                ),
              ),
              trailing: Switch(
                value: _inlineScrollingEnabled,
                onChanged: (value) {
                  setState(() {
                    _inlineScrollingEnabled = value;
                  });
                  
                  // Reset scroll positions when switching modes
                  _resetScrollPositions();
                  
                  // Initialize scroll positions when enabling inline scrolling
                  if (value) {
                    _initializeScrollPositions();
                  }
                  Navigator.pop(context);
                },
                activeColor: AppTheme.primaryColor(themeState.isDarkMode),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showOptionDetails(ZenOptionGreeks option, ThemeState themeState) {

    final secListState = context.read<SecurityListBloc>().state;
    SecurityModel? security;
    if (secListState is SecurityListLoaded) {
      security = secListState.featuresList.firstWhere(
        (sec) => sec.zenId == option.zenId.value.toInt(),
        orElse: () => SecurityModel(
          zenId: option.hasZenId() ? option.zenId.value.toInt() : 0,
          tradingSymbol: option.hasTradingSymbol() ? option.tradingSymbol.value : "Unknown",
          strike: option.hasStrike() ? option.strike.value : 0.0,
          exchanges: ["NSE"],
          lotSize: 1,
          instrumentType: "OPT",
          expiryType: "MONTHLY",
          expiry: option.hasExpiry() ? "${option.expiry.year}-${option.expiry.month.toString().padLeft(2, '0')}-${option.expiry.day.toString().padLeft(2, '0')}" : null,
          name: option.hasTradingSymbol() ? option.tradingSymbol.value : "Unknown",
        ),
      );
    }
    if (security == null) {
      // Handle the case where security is not found or state is not loaded
      ToastUtil.showToast(
        context,
        'Security not found Tradable list',
        ToastType.error,
      );
      return;
    }


    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor(themeState.isDarkMode),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Modern handle bar
            Container(
              margin: const EdgeInsets.only(top: 16, bottom: 24),
              width: 48,
              height: 5,
              decoration: BoxDecoration(
                color: AppTheme.textSecondary(themeState.isDarkMode).withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            // Modern title with trading symbol
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          security!.tradingSymbol != 'Unknown' ? SecurityListTextFormatter.format(security, 'feature' ) : 'Option Details',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimary(themeState.isDarkMode),
                          ),
                        ),
                        if (option.hasLastPrice()) ...[
                          Text(
                            "LTP ₹${option.lastPrice.value.toStringAsFixed(2)}",
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textSecondary(themeState.isDarkMode),
                            ),
                          ),
                          // Show price change if SOD price is available
                          if (_hasSodPrice(option.zenId.value.toInt())) ...[
                            const SizedBox(height: 2),
                            Builder(
                              builder: (context) {
                                final zenId = option.zenId.value.toInt();
                                final currentPrice = option.lastPrice.value;
                                final priceChange = _calculatePriceChange(zenId, currentPrice);
                                final percentChange = _calculatePercentageChange(zenId, currentPrice);
                                
                                Color changeColor = AppTheme.textSecondary(themeState.isDarkMode);
                                if (priceChange > 0) {
                                  changeColor = Colors.green;
                                } else if (priceChange < 0) {
                                  changeColor = Colors.red;
                                }
                                
                                return Text(
                                  "${priceChange >= 0 ? '+' : ''}${priceChange.toStringAsFixed(2)} (${percentChange >= 0 ? '+' : ''}${percentChange.toStringAsFixed(1)}%)",
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: changeColor,
                                  ),
                                );
                              },
                            ),
                          ],
                        ],

                      ],
                    ),
                  ),
                  // const Spacer(),
                  // Container(
                  //   decoration: BoxDecoration(
                  //     color: AppTheme.textSecondary(themeState.isDarkMode).withValues(alpha: 0.1),
                  //     borderRadius: BorderRadius.circular(50),
                  //   ),
                  //   child: IconButton(
                  //     onPressed: () => Navigator.pop(context),
                  //     icon: Icon(
                  //       Icons.close,
                  //       color: AppTheme.textSecondary(themeState.isDarkMode),
                  //       size: 16,
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),
            // Modern option details with cards
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppTheme.cardColor(themeState.isDarkMode),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppTheme.borderColor(themeState.isDarkMode).withValues(alpha: 0.1),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildModernDetailRow('OI', option.hasOi() ? option.oi.value.toStringAsFixed(2) : 'N/A', themeState, Icons.change_circle),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernDetailRow('IV', option.hasImpliedVolatility() ? option.impliedVolatility.value.toStringAsFixed(4) : 'N/A', themeState, Icons.functions),
                        ),
                      ],
                    ),
                    
                    
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildModernDetailRow('Delta', option.hasDelta() ? option.delta.value.toStringAsFixed(4) : 'N/A', themeState, Icons.change_circle,),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernDetailRow('Gamma', option.hasGamma() ? option.gamma.value.toStringAsFixed(4) : 'N/A', themeState, Icons.functions,),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildModernDetailRow('Theta', option.hasTheta() ? option.theta.value.toStringAsFixed(4) : 'N/A', themeState, Icons.schedule,),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernDetailRow('Vega', option.hasVega() ? option.vega.value.toStringAsFixed(4) : 'N/A', themeState, Icons.waves,),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            // Modern action buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  // Buy and Sell buttons with modern styling
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: LinearGradient(
                              colors: [
                                Colors.green.shade600,
                                Colors.green.shade500,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.green.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ElevatedButton.icon(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            onPressed: () => _openOrderForm(option, 'BUY'),
                            icon: const Icon(
                              Icons.trending_up,
                              color: Colors.white,
                              size: 20,
                            ),
                            label: const Text(
                              'BUY',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: LinearGradient(
                              colors: [
                                Colors.red.shade600,
                                Colors.red.shade500,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.red.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ElevatedButton.icon(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            onPressed: () => _openOrderForm(option, 'SELL'),
                            icon: const Icon(
                              Icons.trending_down,
                              color: Colors.white,
                              size: 20,
                            ),
                            label: const Text(
                              'SELL',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Add to watchlist button (removed view chart button)
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        side: BorderSide(
                          color: AppTheme.primaryColor(themeState.isDarkMode),
                          width: 1.5,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        backgroundColor: AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.05),
                      ),
                      onPressed: () => _addToWatchlist(option),
                      icon: Icon(
                        Icons.bookmark_add_outlined,
                        color: AppTheme.primaryColor(themeState.isDarkMode),
                        size: 20,
                      ),
                      label: Text(
                        'Add to Watchlist',
                        style: TextStyle(
                          color: AppTheme.primaryColor(themeState.isDarkMode),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, ThemeState themeState) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondary(themeState.isDarkMode),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary(themeState.isDarkMode),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernDetailRow(String label, String value, ThemeState themeState, IconData icon, {double fontSize = 18}) {
    return Row(
      children: [
        GreeksIcon(name: label, fontSize: fontSize),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _openOrderForm(ZenOptionGreeks option, String action) {
    Navigator.pop(context); // Close the details modal first

    // Find the security from the cached security list
    final secListState = context.read<SecurityListBloc>().state;
    SecurityModel? selectedSecurity;

    if (secListState is SecurityListLoaded && option.hasZenId()) {
      // Search in both equity and features lists
      final zenIdValue = option.zenId.value.toInt();
      selectedSecurity = secListState.equityList.firstWhere(
        (data) => data.zenId == zenIdValue,
        orElse: () => secListState.featuresList.firstWhere(
          (data) => data.zenId == zenIdValue,
          orElse: () => SecurityModel(
            zenId: option.hasZenId() ? option.zenId.value.toInt() : 0,
            tradingSymbol: option.hasTradingSymbol() ? option.tradingSymbol.value : "Unknown",
            strike: option.hasStrike() ? option.strike.value : 0.0,
            exchanges: ["NSE"],
            lotSize: 1,
            instrumentType: "OPT",
            expiryType: "MONTHLY",
            expiry: option.hasExpiry() ? "${option.expiry.year}-${option.expiry.month.toString().padLeft(2, '0')}-${option.expiry.day.toString().padLeft(2, '0')}" : null,
            name: option.hasTradingSymbol() ? option.tradingSymbol.value : "Unknown",
          ),
        ),
      );
    }

    if (selectedSecurity != null) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        builder: (modalContext) {
          return SingleChildScrollView(
            reverse: true,
            child: OrderFormSheet(
              openOrderType: FormOpenOrderType.spider,
              spiderMetaData: SpiderOrderData(
                zenId: option.zenId.value.toInt(),
                tradingSymbol: option.tradingSymbol.value,
                transactionType: action,
              ),
              // You may need to create a custom constructor or modify OrderFormSheet
              // to accept a SecurityModel directly for pre-selection
            ),
          );
        },
      ).whenComplete(() {
        debugPrint("💲Order form from option chain closed");
      });
    } else {
      ToastUtil.showToast(
        context,
        'Security not found in cache',
        ToastType.error,
      );
    }
  }

  void _addToWatchlist(ZenOptionGreeks option) async {
    Navigator.pop(context); // Close the details modal first

    final authState = context.read<AuthBloc>().state;
    if (authState is! AuthAuthenticated) {
      ToastUtil.showToast(
        context,
        'Please login to add to watchlist',
        ToastType.error,
      );
      return;
    }

    // Find the security from the cached security list
    final secListState = context.read<SecurityListBloc>().state;
    SecurityModel? selectedSecurity;

    if (secListState is SecurityListLoaded && option.hasZenId()) {
      // Search in both equity and features lists
      final zenIdValue = option.zenId.value.toInt();
      selectedSecurity = secListState.equityList.firstWhere(
        (data) => data.zenId == zenIdValue,
        orElse: () => secListState.featuresList.firstWhere(
          (data) => data.zenId == zenIdValue,
          orElse: () => SecurityModel(
            zenId: option.hasZenId() ? option.zenId.value.toInt() : 0,
            tradingSymbol: option.hasTradingSymbol() ? option.tradingSymbol.value : "Unknown",
            strike: option.hasStrike() ? option.strike.value : 0.0,
            exchanges: ["NSE"],
            lotSize: 1,
            instrumentType: "OPT",
            expiryType: "MONTHLY",
            expiry: option.hasExpiry() ? "${option.expiry.year}-${option.expiry.month.toString().padLeft(2, '0')}-${option.expiry.day.toString().padLeft(2, '0')}" : null,
            name: option.hasTradingSymbol() ? option.tradingSymbol.value : "Unknown",
          ),
        ),
      );
    }

    if (selectedSecurity != null) {
      try {
        final watchlistService = WatchlistService();
        await watchlistService.addSecurityToWatchlist(
          authState.credentialsModel.clientId.toString(),
          0, // Always add to first watchlist (index 0)
          selectedSecurity,
        );

        if (mounted) {
          ToastUtil.showToast(
            context,
            '${selectedSecurity.tradingSymbol} added to watchlist',
            ToastType.success,
          );
        }
      } catch (e) {
        if (mounted) {
          ToastUtil.showToast(
            context,
            'Error adding to watchlist: $e',
            ToastType.error,
          );
        }
      }
    } else {
      if (mounted) {
        ToastUtil.showToast(
          context,
          'Security not found in cache',
          ToastType.error,
        );
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _greeksTabController.dispose();
    _leftScrollController.dispose();
    _rightScrollController.dispose();
    _verticalScrollController.dispose();
    _leftVerticalScrollController.dispose();
    _strikeVerticalScrollController.dispose();
    _rightVerticalScrollController.dispose();
    // Disconnect from option Greeks WebSocket when screen is closed
    // Use stored bloc reference to avoid context access after dispose
    _optionGreeksBloc?.add(OptionGreeksWebSocketDisconnect());
    // Stop REST periodic refresh
    _optionGreeksRestBloc?.stopPeriodicRefresh();
    // Clear selected stocks from price WebSocket
    _webSocketBloc?.add(WebSocketClearSelectedStocks());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          appBar: _buildAppBar(themeState),
          body: MultiBlocListener(
            listeners: [
              // Listen to WebSocket connection status
              BlocListener<OptionGreeksWebSocketBloc, OptionGreeksWebSocketState>(
                listener: (context, state) {
                  if (state is OptionGreeksWebSocketConnected || 
                      state is OptionGreeksWebSocketDataReceived ||
                      state is OptionGreeksWebSocketUnderlyingSelected) {
                    // WebSocket is connected and has data, switch from REST fallback
                    if (_isUsingRestFallback) {
                      setState(() {
                        _isUsingRestFallback = false;
                      });
                      // Stop REST periodic refresh
                      _optionGreeksRestBloc?.stopPeriodicRefresh();
                    }
                  } else if (state is OptionGreeksWebSocketDisconnectedState || 
                             state is OptionGreeksWebSocketError) {
                    // WebSocket disconnected, fallback to REST
                    if (!_isUsingRestFallback) {
                      setState(() {
                        _isUsingRestFallback = true;
                      });
                      // Start REST fallback
                      _optionGreeksRestBloc?.add(OptionGreeksRestFetch());
                    }
                  }
                },
              ),
              // Listen to price WebSocket updates
              BlocListener<WebSocketBloc, WebSocketState>(
                listener: (context, state) {
                  if (state is WebSocketConnected || state is WebSocketDataReceived) {
                    setState(() {
                      _isPriceWebSocketConnected = true;
                    });
                  } else if (state is WebSocketError || state is WebSocketDisconnectedState) {
                    setState(() {
                      _isPriceWebSocketConnected = false;
                    });
                  } else if (state is WebSocketMultipleStockPricesUpdated) {
                    debugPrint('🔥 Received price updates for ${state.stockPrices.length} stocks: ${state.stockPrices}');
                    setState(() {
                      // Store previous prices before updating
                      for (final entry in state.stockPrices.entries) {
                        if (_livePrices.containsKey(entry.key)) {
                          _previousPrices[entry.key] = _livePrices[entry.key]!;
                        }
                      }
                      _livePrices = state.stockPrices;
                      _isPriceWebSocketConnected = true;
                    });
                  }
                },
              ),
            ],
            child: _buildDataSourceBody(themeState),
          ),
        );
      },
    );
  }

  Widget _buildDataSourceBody(ThemeState themeState) {
    if (_isUsingRestFallback) {
      // Use REST API data
      return BlocBuilder<OptionGreeksRestBloc, OptionGreeksRestState>(
        builder: (context, restState) {
          if (restState is OptionGreeksRestLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    'Loading option data...',
                    style: TextStyle(
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                  ),
                ],
              ),
            );
          }

          if (restState is OptionGreeksRestError) {
            return _buildErrorState(restState.message, themeState);
          }

          if (restState is OptionGreeksRestLoaded) {
            // Convert REST data to WebSocket format for compatibility
            final convertedData = OptionGreeksConverter.convertRestToWebSocket(restState.optionGreeks);
            return _buildOptionChainContent(convertedData, themeState, isFromRest: true);
          }

          return _buildEmptyState(themeState);
        },
      );
    } else {
      // Use WebSocket data
      return BlocBuilder<OptionGreeksWebSocketBloc, OptionGreeksWebSocketState>(
        builder: (context, wsState) {
          if (wsState is OptionGreeksWebSocketConnecting) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    'Connecting to live data...',
                    style: TextStyle(
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                  ),
                ],
              ),
            );
          }

          if (wsState is OptionGreeksWebSocketError) {
            return _buildErrorState(wsState.error ?? 'WebSocket connection failed', themeState);
          }

          if (wsState is OptionGreeksWebSocketDisconnectedState) {
            return _buildDisconnectedState(themeState);
          }

          final optionGreeksList = wsState.optionGreeksList ?? [];
          return _buildOptionChainContent(optionGreeksList, themeState, isFromRest: false);
        },
      );
    }
  }

/// Screen Starts here
/// MAIN CONTENT STARTS HERE
  Widget _buildOptionChainContent(List<ZenOptionGreeks> optionGreeksList, ThemeState themeState, {required bool isFromRest}) {
    if (optionGreeksList.isEmpty) {
      return _buildEmptyState(themeState);
    }

    // Filter options by selected underlying first
    final underlyingFilteredOptions = _filterByUnderlying(optionGreeksList);

    // Initialize expiry dates from filtered data
    _initializeExpiryDates(underlyingFilteredOptions);

    // Filter options by selected expiry
    final filteredOptions = _filterByExpiry(underlyingFilteredOptions);

    return Column(
      children: [
        _buildDataSourceIndicator(themeState, isFromRest),
        _buildSearchBar(themeState),
        if (expiryDates.isNotEmpty) _buildExpiryTabs(themeState),
        if (!_inlineScrollingEnabled) _buildGreeksTabs(themeState),
        _inlineScrollingEnabled 
          ? _buildInlineScrollingTable(filteredOptions, themeState)
          : _buildOptionChainTable(filteredOptions, themeState),
        _buildBottomStats(filteredOptions, themeState),
      ],
    );
  }

  Widget _buildDataSourceIndicator(ThemeState themeState, bool isFromRest) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      color: isFromRest 
        ? Colors.orange.withValues(alpha: 0.1) 
        : Colors.green.withValues(alpha: 0.1),
      child: Row(
        children: [
          Icon(
            isFromRest ? Icons.cloud_download : Icons.wifi,
            size: 16,
            color: isFromRest ? Colors.orange : Colors.green,
          ),
          const SizedBox(width: 8),
          Text(
            isFromRest 
              ? 'Using REST API (Fallback Mode)' 
              : 'Live Streaming Data',
            style: TextStyle(
              fontSize: 12,
              color: isFromRest ? Colors.orange : Colors.green,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (isFromRest) ...[
            const Spacer(),
            Text(
              'Updates every 30s',
              style: TextStyle(
                fontSize: 10,
                color: AppTheme.textSecondary(themeState.isDarkMode),
              ),
            ),
          ],
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeState themeState) {
    return AppBar(
      backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: AppTheme.textPrimary(themeState.isDarkMode)),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          Text(
            'Option Chain',
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 24,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.more_vert, color: AppTheme.textPrimary(themeState.isDarkMode)),
          onPressed: () => _showOptionsMenu(themeState),
        ),
      ],
    );
  }

  Widget _buildSearchBar(ThemeState themeState) {
    // Get current underlying data
    final currentUnderlying = _availableUnderlyings.firstWhere(
      (u) => u['name'] == searchQuery,
      orElse: () => _availableUnderlyings.first,
    );
    
    // Get live price from WebSocket or fallback to mock data
    final zenId = int.parse(currentUnderlying['zenId']);
    final livePrice = _livePrices[zenId];
    final mockData = this.mockData[searchQuery];
    
    // Use live price if available, otherwise use mock data
    final currentPrice = livePrice ?? mockData?['price'] ?? 0.0;
    final change = livePrice != null ? _calculatePriceChange(zenId, livePrice) : (mockData?['change'] ?? 0.0);
    final changePercent = livePrice != null ? _calculatePercentageChange(zenId, livePrice) : (mockData?['changePercent'] ?? 0.0);
    
    return GestureDetector(
      onTap: () => _showUnderlyingSelector(themeState),
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor(themeState.isDarkMode),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.borderColor(themeState.isDarkMode)),
        ),
        child: Row(
          children: [
            Icon(Icons.search, color: AppTheme.textSecondary(themeState.isDarkMode), size: 20),
            const SizedBox(width: 12),
            Text(
              searchQuery,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary(themeState.isDarkMode),
              ),
            ),
            const SizedBox(width: 8),
            Icon(Icons.keyboard_arrow_down, color: AppTheme.textSecondary(themeState.isDarkMode), size: 20),
            const Spacer(),
            if (currentPrice > 0) ...[
              // Show live indicator if using WebSocket data
              if (livePrice != null && _isPriceWebSocketConnected) ...[
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
              ],
              Text(
                currentPrice.toStringAsFixed(2),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _showAbsoluteChange
                    ? '${change >= 0 ? '+' : ''}${change.toStringAsFixed(2)}'
                    : '${changePercent >= 0 ? '+' : ''}${changePercent.toStringAsFixed(2)}%',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: change >= 0 ? Colors.green : Colors.red,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildExpiryTabs(ThemeState themeState) {
    if (expiryDates.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 32,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        labelColor: AppTheme.textPrimary(themeState.isDarkMode),
        unselectedLabelColor: AppTheme.textSecondary(themeState.isDarkMode),
        indicator: BoxDecoration(
          color: AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppTheme.primaryColor(themeState.isDarkMode),
            width: 1,
          ),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        dividerColor: Colors.transparent,
        labelPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
        labelStyle: const TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.3,
        ),
        onTap: (index) {
          setState(() {
            selectedExpiry = expiryDates[index];
          });
          
          // Fetch SOD prices for the new expiry's options
          _fetchSodPricesForOptionChain();
        },
        tabs: expiryDates.map((date) {
          return Tab(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // Increased padding
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                
              ),
              child: Text(
                _formatExpiryDate(date),
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  //letterSpacing: 0.4,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.visible,
                softWrap: false,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildGreeksTabs(ThemeState themeState) {
    return Container(
      height: 32,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: TabBar(
        controller: _greeksTabController,
        isScrollable: true,
        tabAlignment: TabAlignment.startOffset,
        labelColor: AppTheme.textPrimary(themeState.isDarkMode),
        unselectedLabelColor: AppTheme.textSecondary(themeState.isDarkMode),
        indicator: BoxDecoration(
          color: AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppTheme.primaryColor(themeState.isDarkMode),
            width: 1,
          ),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        dividerColor: Colors.transparent,
        labelPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
        labelStyle: const TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.3,
        ),
        onTap: (index) {
          setState(() {
            _selectedGreek = _greekOptions[index];
          });
        },
        tabs: _greekOptions.map((greek) {
          return Tab(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 8), // Increased padding
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),

              ),
              child: Text(
                greek,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.4,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.visible,
                softWrap: false,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildOptionChainTable(List<ZenOptionGreeks> optionGreeksList, ThemeState themeState) {
    // Group options by strike price and separate calls/puts
    final Map<double, Map<String, ZenOptionGreeks>> groupedOptions = {};
    
    for (final option in optionGreeksList) {
      if (!option.hasStrike()) continue;
      
      final strike = option.strike.value;
      if (!groupedOptions.containsKey(strike)) {
        groupedOptions[strike] = {};
      }
      
      // Determine if it's a call or put based on trading symbol
      final symbol = option.hasTradingSymbol() ? option.tradingSymbol.value : '';
      final isCall = symbol.contains('CE') || symbol.contains('CALL');
      final isPut = symbol.contains('PE') || symbol.contains('PUT');
      
      if (isCall) {
        groupedOptions[strike]!['call'] = option;
      } else if (isPut) {
        groupedOptions[strike]!['put'] = option;
      }
    }
    
    // Sort strikes using the new generic sorting function
    final sortedStrikes = _sortStrikes(groupedOptions, _sortBy, _ltpSortAscending);
    
    return Expanded(
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor(themeState.isDarkMode),
              border: Border(
                bottom: BorderSide(color: AppTheme.borderColor(themeState.isDarkMode)),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        if (_sortBy == _selectedGreek) {
                          _ltpSortAscending = !_ltpSortAscending;
                        } else {
                          _sortBy = _selectedGreek;
                          _ltpSortAscending = true;
                        }
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _getGreekLabel(),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 4),
                        if (_sortBy == _selectedGreek)
                          Icon(
                            _ltpSortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                            size: 12,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                          ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        if (_sortBy == 'CallLTP') {
                          _ltpSortAscending = !_ltpSortAscending;
                        } else {
                          _sortBy = 'CallLTP';
                          _ltpSortAscending = false; // Default to descending for LTP (highest first)
                        }
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Call LTP',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                          ),
                        ),
                        const SizedBox(width: 4),
                        if (_sortBy == 'CallLTP')
                          Icon(
                            _ltpSortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                            size: 12,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                          ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        if (_sortBy == 'Strike') {
                          _ltpSortAscending = !_ltpSortAscending;
                        } else {
                          _sortBy = 'Strike';
                          _ltpSortAscending = true;
                        }
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Strike',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                          ),
                        ),
                        const SizedBox(width: 4),
                        if (_sortBy == 'Strike')
                          Icon(
                            _ltpSortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                            size: 12,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                          ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        if (_sortBy == 'PutLTP') {
                          _ltpSortAscending = !_ltpSortAscending;
                        } else {
                          _sortBy = 'PutLTP';
                          _ltpSortAscending = false; // Default to descending for LTP (highest first)
                        }
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Put LTP',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                          ),
                        ),
                        const SizedBox(width: 4),
                        if (_sortBy == 'PutLTP')
                          Icon(
                            _ltpSortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                            size: 12,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                          ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        if (_sortBy == _selectedGreek) {
                          _ltpSortAscending = !_ltpSortAscending;
                        } else {
                          _sortBy = _selectedGreek;
                          _ltpSortAscending = true;
                        }
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _getGreekLabel(),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                          ),
                        ),
                        const SizedBox(width: 4),
                        if (_sortBy == _selectedGreek)
                          Icon(
                            _ltpSortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                            size: 12,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Table Body
          Expanded(
            child: ListView.builder(
              itemCount: sortedStrikes.length,
              itemBuilder: (context, index) {
                final strike = sortedStrikes[index];
                final options = groupedOptions[strike]!;
                final callOption = options['call'];
                final putOption = options['put'];
                
                return _buildOptionRow(strike, callOption, putOption, themeState);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionRow(double strike, ZenOptionGreeks? callOption, ZenOptionGreeks? putOption, ThemeState themeState) {
    // Get LTP values
    final callLTP = (callOption?.hasLastPrice() == true) ? callOption!.lastPrice.value : 0.0;
    final putLTP = (putOption?.hasLastPrice() == true) ? putOption!.lastPrice.value : 0.0;

    // Get Greek values based on selected Greek
    final callGreekValue = _getGreekValue(callOption);
    final putGreekValue = _getGreekValue(putOption);

    // Get percentage changes
    final callGreekPercentage = _getGreekPercentage(callOption, strike);
    final putGreekPercentage = _getGreekPercentage(putOption, strike);

    // No longer need individual indicator colors since line is under strike

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppTheme.borderColor(themeState.isDarkMode).withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
          // Call Greek Value
          Expanded(
            flex: 2,
            child: _buildOptionCellWithoutIndicator(
              value: callGreekValue,
              percentage: '${callGreekPercentage.toStringAsFixed(1)}%',
              isPositive: callGreekPercentage >= 0,
              themeState: themeState,
              option: callOption,
            ),
          ),
          // Call LTP
          Expanded(
            flex: 2,
            child: _buildNormalModeLtpCell(
              value: callLTP.toStringAsFixed(2),
              option: callOption,
              themeState: themeState,
            ),
          ),
          // Strike
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                children: [
                  Text(
                    strike.toInt().toString(),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
          // Put LTP
          Expanded(
            flex: 2,
            child: _buildNormalModeLtpCell(
              value: putLTP.toStringAsFixed(2),
              option: putOption,
              themeState: themeState,
            ),
          ),
          // Put Greek Value
          Expanded(
            flex: 2,
            child: _buildOptionCellWithoutIndicator(
              value: putGreekValue,
              percentage: '${putGreekPercentage.toStringAsFixed(1)}%',
              isPositive: putGreekPercentage >= 0,
              themeState: themeState,
              option: putOption,
            ),
          ),
            ],
          ),
          const SizedBox(height: 8),
          // Flowing indicator line across the entire row
          _buildFlowingIndicatorLine(callGreekPercentage, putGreekPercentage, themeState),
        ],
      ),
    );
  }

  Widget _buildOptionCellWithoutIndicator({
    required String value,
    required String percentage,
    required bool isPositive,
    required ThemeState themeState,
    ZenOptionGreeks? option,
  }) {
    Widget cellContent = Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary(themeState.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          percentage,
          style: TextStyle(
            fontSize: 12,
            color: isPositive ? Colors.green : Colors.red,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );

    // Make the cell clickable if option is provided
    if (option != null) {
      return GestureDetector(
        onTap: () => _showOptionDetails(option, themeState),
        child: cellContent,
      );
    }

    return cellContent;
  }

  // Build LTP cell for normal mode table with proper price change calculation
  Widget _buildNormalModeLtpCell({
    required String value,
    required ZenOptionGreeks? option,
    required ThemeState themeState,
  }) {
    if (option == null || !option.hasLastPrice()) {
      return _buildOptionCellWithoutIndicator(
        value: value,
        percentage: '0.00%',
        isPositive: false,
        themeState: themeState,
        option: option,
      );
    }

    final zenId = option.zenId.value.toInt();
    final currentPrice = option.lastPrice.value;
    final priceChange = _calculatePriceChange(zenId, currentPrice);
    final percentChange = _calculatePercentageChange(zenId, currentPrice);
    
    // Determine if change is positive
    final isPositive = priceChange >= 0;
    
    // Show either absolute or percentage change based on toggle
    final changeText = _showAbsoluteChange
        ? '${priceChange >= 0 ? '+' : ''}${priceChange.toStringAsFixed(2)}'
        : '${percentChange >= 0 ? '+' : ''}${percentChange.toStringAsFixed(1)}%';

    Widget cellContent = Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary(themeState.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
        // Only show price change if SOD price is available
        if (_hasSodPrice(zenId)) ...[
          const SizedBox(height: 2),
          Text(
            changeText,
            style: TextStyle(
              fontSize: 12,
              color: isPositive ? Colors.green : Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );

    return GestureDetector(
      onTap: () => _showOptionDetails(option, themeState),
      child: cellContent,
    );
  }

  Widget _buildInlineScrollingTable(List<ZenOptionGreeks> optionGreeksList, ThemeState themeState) {
    // Group options by strike price and separate calls/puts
    final Map<double, Map<String, ZenOptionGreeks>> groupedOptions = {};
    
    for (final option in optionGreeksList) {
      if (!option.hasStrike()) continue;
      
      final strike = option.strike.value;
      if (!groupedOptions.containsKey(strike)) {
        groupedOptions[strike] = {};
      }
      
      // Determine if it's a call or put based on trading symbol
      final symbol = option.hasTradingSymbol() ? option.tradingSymbol.value : '';
      final isCall = symbol.contains('CE') || symbol.contains('CALL');
      final isPut = symbol.contains('PE') || symbol.contains('PUT');
      
      if (isCall) {
        groupedOptions[strike]!['call'] = option;
      } else if (isPut) {
        groupedOptions[strike]!['put'] = option;
      }
    }
    
    // Sort strikes using the new generic sorting function
    final sortedStrikes = _sortStrikes(groupedOptions, _sortBy, _ltpSortAscending);
    
    // Define column headers in the order: Gamma, Vega, Theta, Delta, OI, IV, Call LTP, Strike, Put LTP, IV, OI, Delta, Theta, Vega, Gamma
    final leftColumns = ['Gamma', 'Vega', 'Theta', 'Delta', 'OI', 'IV', 'Call LTP'];
    final rightColumns = ['Put LTP', 'IV', 'OI', 'Delta', 'Theta', 'Vega', 'Gamma'];
    
    // Initialize scroll positions only once when first building the table
    if (!_initialPositionsSet) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeScrollPositions();
      });
    }
    
    return Expanded(
      child: Row(
        children: [
          // Left scrollable section (header + data together)
          Expanded(
            child: SingleChildScrollView(
              controller: _leftScrollController,
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: leftColumns.length * 70.0, // Fixed width for all left columns
                child: Column(
                  children: [
                    // Left header
                    Container(
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceColor(themeState.isDarkMode),
                        border: Border(
                          bottom: BorderSide(color: AppTheme.borderColor(themeState.isDarkMode)),
                        ),
                      ),
                      child: Row(
                        children: leftColumns.map((column) => _buildClickableHeader(column, 70, themeState)).toList(),
                      ),
                    ),
                    // Left data rows
                    Expanded(
                      child: ListView.builder(
                        controller: _leftVerticalScrollController,
                        itemCount: sortedStrikes.length,
                        itemBuilder: (context, index) {
                          final strike = sortedStrikes[index];
                          final options = groupedOptions[strike]!;
                          final callOption = options['call'];
                          
                          return Container(
                            height: 50,
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: AppTheme.borderColor(themeState.isDarkMode).withValues(alpha: 0.3),
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                // Call Gamma
                                _buildInlineCell(_getGreekValueByName(callOption, 'Gamma'), 70, themeState, callOption),
                                // Call Vega
                                _buildInlineCell(_getGreekValueByName(callOption, 'Vega'), 70, themeState, callOption),
                                // Call Theta
                                _buildInlineCell(_getGreekValueByName(callOption, 'Theta'), 70, themeState, callOption),
                                // Call Delta
                                _buildInlineCell(_getGreekValueByName(callOption, 'Delta'), 70, themeState, callOption),
                                // Call OI
                                _buildInlineCell(_getGreekValueByName(callOption, 'OI'), 70, themeState, callOption),
                                // Call IV
                                _buildInlineCell(_getGreekValueByName(callOption, 'IV'), 70, themeState, callOption),
                                // Call LTP with price change
                                _buildLtpCell(callOption, 70, themeState),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Fixed Strike column (header + data)
          Container(
            width: 80,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.05),
              border: Border.symmetric(
                vertical: BorderSide(color: AppTheme.borderColor(themeState.isDarkMode)),
              ),
            ),
            child: Column(
              children: [
                // Strike header with sort button
                Container(
                  height: 50,
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.1),
                    border: Border(
                      bottom: BorderSide(color: AppTheme.borderColor(themeState.isDarkMode)),
                    ),
                  ),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        if (_sortBy == 'Strike') {
                          _ltpSortAscending = !_ltpSortAscending;
                        } else {
                          _sortBy = 'Strike';
                          _ltpSortAscending = true;
                        }
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Strike',
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimary(themeState.isDarkMode),
                          ),
                        ),
                        const SizedBox(width: 4),
                        if (_sortBy == 'Strike')
                          Icon(
                            _ltpSortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                            size: 14,
                            color: AppTheme.textPrimary(themeState.isDarkMode),
                          ),
                      ],
                    ),
                  ),
                ),
                // Strike data rows
                Expanded(
                  child: ListView.builder(
                    controller: _strikeVerticalScrollController,
                    itemCount: sortedStrikes.length,
                    itemBuilder: (context, index) {
                      final strike = sortedStrikes[index];
                      
                      return Container(
                        height: 50,
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppTheme.borderColor(themeState.isDarkMode).withValues(alpha: 0.3),
                            ),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            strike.toInt().toString(),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimary(themeState.isDarkMode),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          // Right scrollable section (header + data together)
          Expanded(
            child: SingleChildScrollView(
              controller: _rightScrollController,
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: rightColumns.length * 70.0, // Fixed width for all right columns
                child: Column(
                  children: [
                    // Right header
                    Container(
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceColor(themeState.isDarkMode),
                        border: Border(
                          bottom: BorderSide(color: AppTheme.borderColor(themeState.isDarkMode)),
                        ),
                      ),
                      child: Row(
                        children: rightColumns.map((column) => _buildClickableHeader(column, 70, themeState)).toList(),
                      ),
                    ),
                    // Right data rows
                    Expanded(
                      child: ListView.builder(
                        controller: _rightVerticalScrollController,
                        itemCount: sortedStrikes.length,
                        itemBuilder: (context, index) {
                          final strike = sortedStrikes[index];
                          final options = groupedOptions[strike]!;
                          final putOption = options['put'];
                          
                          return Container(
                            height: 50,
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: AppTheme.borderColor(themeState.isDarkMode).withValues(alpha: 0.3),
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                // Put LTP with price change
                                _buildLtpCell(putOption, 70, themeState),
                                // Put IV
                                _buildInlineCell(_getGreekValueByName(putOption, 'IV'), 70, themeState, putOption),
                                // Put OI
                                _buildInlineCell(_getGreekValueByName(putOption, 'OI'), 70, themeState, putOption),
                                // Put Delta
                                _buildInlineCell(_getGreekValueByName(putOption, 'Delta'), 70, themeState, putOption),
                                // Put Theta
                                _buildInlineCell(_getGreekValueByName(putOption, 'Theta'), 70, themeState, putOption),
                                // Put Vega
                                _buildInlineCell(_getGreekValueByName(putOption, 'Vega'), 70, themeState, putOption),
                                // Put Gamma
                                _buildInlineCell(_getGreekValueByName(putOption, 'Gamma'), 70, themeState, putOption),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClickableHeader(String column, double width, ThemeState themeState) {
    String sortKey = column;
    if (column == 'Call LTP') sortKey = 'CallLTP';
    if (column == 'Put LTP') sortKey = 'PutLTP';
    // For Greek columns, we'll use the column name directly
    
    bool isCurrentSort = _sortBy == sortKey;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          if (_sortBy == sortKey) {
            _ltpSortAscending = !_ltpSortAscending;
          } else {
            _sortBy = sortKey;
            if (sortKey == 'CallLTP' || sortKey == 'PutLTP') {
              _ltpSortAscending = false; // Default to descending for LTP
            } else {
              _ltpSortAscending = true;
            }
          }
        });
      },
      child: Container(
        width: width,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        decoration: BoxDecoration(
          border: Border(
            right: BorderSide(
              color: AppTheme.borderColor(themeState.isDarkMode).withValues(alpha: 0.3),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                column,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (isCurrentSort) ...[
              const SizedBox(width: 2),
              Icon(
                _ltpSortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                size: 10,
                color: AppTheme.textSecondary(themeState.isDarkMode),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInlineCell(String value, double width, ThemeState themeState, ZenOptionGreeks? option) {
    // Determine if the value represents a positive or negative change for color coding
    final numValue = double.tryParse(value) ?? 0.0;
    final isPositive = numValue > 0;
    final isNegative = numValue < 0;
    
    Widget cellContent = Container(
      width: width,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(
            color: AppTheme.borderColor(themeState.isDarkMode).withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
      ),
      child: Text(
        value,
        style: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          color: option != null 
            ? (isPositive ? Colors.green.shade600 : 
               isNegative ? Colors.red.shade600 : 
               AppTheme.textPrimary(themeState.isDarkMode))
            : AppTheme.textSecondary(themeState.isDarkMode),
        ),
        textAlign: TextAlign.center,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );

    // Make the cell clickable if option is provided
    if (option != null) {
      return GestureDetector(
        onTap: () => _showOptionDetails(option, themeState),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.transparent,
          ),
          child: cellContent,
        ),
      );
    }

    return cellContent;
  }

  double _getGreekValueByColumnName(ZenOptionGreeks? option, String columnName) {
    if (option == null) return 0.0;
    
    switch (columnName) {
      case 'OI':
        return option.hasOi() ? option.oi.value.toDouble() : 0.0;
      case 'Gamma':
        return option.hasGamma() ? option.gamma.value : 0.0;
      case 'Vega':
        return option.hasVega() ? option.vega.value : 0.0;
      case 'Theta':
        return option.hasTheta() ? option.theta.value : 0.0;
      case 'Delta':
        return option.hasDelta() ? option.delta.value : 0.0;
      case 'IV':
        return option.hasImpliedVolatility() ? option.impliedVolatility.value * 100 : 0.0;
      default:
        return 0.0;
    }
  }

  String _getGreekValueByName(ZenOptionGreeks? option, String greekName) {
    if (option == null) return '0.00';
    
    switch (greekName) {
      case 'OI':
        return option.hasOi() 
            ? option.oi.value.toStringAsFixed(0)
            : '0';
      case 'IV':
        return option.hasImpliedVolatility() 
            ? (option.impliedVolatility.value * 100).toStringAsFixed(2)
            : '0.00';
      case 'Delta':
        return option.hasDelta() 
            ? option.delta.value.toStringAsFixed(4)
            : '0.0000';
      case 'Gamma':
        return option.hasGamma() 
            ? option.gamma.value.toStringAsFixed(4)
            : '0.0000';
      case 'Theta':
        return option.hasTheta() 
            ? option.theta.value.toStringAsFixed(4)
            : '0.0000';
      case 'Vega':
        return option.hasVega() 
            ? option.vega.value.toStringAsFixed(4)
            : '0.0000';
      default:
        return '0.00';
    }
  }

  Widget _buildBottomStats(List<ZenOptionGreeks> filteredOptions, ThemeState themeState) {
    // Calculate stats from actual data
    final callOptions = filteredOptions.where((option) {
      final symbol = option.hasTradingSymbol() ? option.tradingSymbol.value : '';
      return symbol.contains('CE') || symbol.contains('CALL');
    }).toList();
    
    final putOptions = filteredOptions.where((option) {
      final symbol = option.hasTradingSymbol() ? option.tradingSymbol.value : '';
      return symbol.contains('PE') || symbol.contains('PUT');
    }).toList();
    
    // Calculate PCR (Put-Call Ratio) - simplified calculation
    final pcr = putOptions.isNotEmpty && callOptions.isNotEmpty 
        ? (putOptions.length / callOptions.length).toStringAsFixed(2)
        : '0.00';
    
    // Calculate average IV
    final allIVs = filteredOptions
        .where((option) => option.hasImpliedVolatility())
        .map((option) => option.impliedVolatility.value)
        .toList();
    
    final avgIV = allIVs.isNotEmpty 
        ? (allIVs.reduce((a, b) => a + b) / allIVs.length).toStringAsFixed(2)
        : '0.00';
    
    // Find strike with most activity (simplified max pain calculation)
    final strikeActivity = <double, int>{};
    for (final option in filteredOptions) {
      if (option.hasStrike()) {
        strikeActivity[option.strike.value] = (strikeActivity[option.strike.value] ?? 0) + 1;
      }
    }
    
    final maxPain = strikeActivity.isNotEmpty 
        ? strikeActivity.entries.reduce((a, b) => a.value > b.value ? a : b).key.toInt().toString()
        : '0';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor(themeState.isDarkMode),
        border: Border(
          top: BorderSide(color: AppTheme.borderColor(themeState.isDarkMode)),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('PCR', pcr, themeState),
          _buildStatItem('Max Pain', maxPain, themeState),
          _buildStatItem('ATM IV', avgIV, themeState),
          _buildStatItem('IV Percentile', '$avgIV - ${(double.tryParse(avgIV) ?? 0) > 15 ? 'High' : 'Low'}', themeState),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, ThemeState themeState) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppTheme.textSecondary(themeState.isDarkMode),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary(themeState.isDarkMode),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(String error, ThemeState themeState) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Connection Error',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary(themeState.isDarkMode),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(color: AppTheme.textSecondary(themeState.isDarkMode)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDisconnectedState(ThemeState themeState) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.wifi_off, size: 64, color: Colors.orange),
          const SizedBox(height: 16),
          Text(
            'Connection Lost',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary(themeState.isDarkMode),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Attempting to reconnect...',
            style: TextStyle(color: AppTheme.textSecondary(themeState.isDarkMode)),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeState themeState) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.data_usage_outlined,
            size: 64,
            color: AppTheme.textSecondary(themeState.isDarkMode),
          ),
          const SizedBox(height: 16),
          Text(
            'No Option Data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary(themeState.isDarkMode),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Waiting for option Greeks data...',
            style: TextStyle(color: AppTheme.textSecondary(themeState.isDarkMode)),
          ),
        ],
      ),
    );
  }
}