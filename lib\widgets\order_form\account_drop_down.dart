import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';

class AccountDropDown extends StatelessWidget {
  final String? selectedValue;
  final void Function(String) onChanged;
  final Map<int, String> list; // List passed as a prop (key-value pair)

  const AccountDropDown({
    super.key,
    required this.selectedValue,
    required this.onChanged,
    required this.list,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(2),
      alignment: Alignment.center,
      height: 40,
      width: 170,
      decoration: BoxDecoration(
        color: const Color(0xff353535),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: DropdownButtonHideUnderline(
          child: DropdownButton2<String>(
            hint: const Text(
              "Account",
              style: TextStyle(
                  color: Color(0xffADADAD),
                  fontSize: 16,
                  fontWeight: FontWeight.w400),
            ),
            isExpanded: true,
            items: list.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key.toString(),
                child: Text(
                  entry.value,
                  style: const TextStyle(
                    fontSize: 15,
                    color: Color(0xffADADAD),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              );
            }).toList(),
            value: selectedValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
              }
            },
            buttonStyleData: const ButtonStyleData(
              padding: EdgeInsets.symmetric(horizontal: 16),
              height: 100,
              width: 400,
            ),
            dropdownStyleData: DropdownStyleData(
                decoration: BoxDecoration(
                  color: const Color(0xff353535),
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                  shape: BoxShape.rectangle,
                ),
                maxHeight: 500,
                width: 200),
            menuItemStyleData: const MenuItemStyleData(
              height: 40,
            ),
          ),
        ),
      ),
    );
  }
}
