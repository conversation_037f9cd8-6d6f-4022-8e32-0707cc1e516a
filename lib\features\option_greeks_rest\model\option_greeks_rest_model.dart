class OptionGreeksRestResponse {
  final String status;
  final int code;
  final String message;
  final List<OptionGreeksRestData> payload;

  OptionGreeksRestResponse({
    required this.status,
    required this.code,
    required this.message,
    required this.payload,
  });

  factory OptionGreeksRestResponse.fromJson(Map<String, dynamic> json) {
    return OptionGreeksRestResponse(
      status: json['status'] ?? '',
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      payload: (json['payload'] as List<dynamic>?)
          ?.map((item) => OptionGreeksRestData.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'code': code,
      'message': message,
      'payload': payload.map((item) => item.toJson()).toList(),
    };
  }
}

class OptionGreeksRestData {
  final int zenId;
  final int underlyingZenId;
  final String tradingSymbol;
  final double strike;
  final double lastPrice;
  final String moneyness;
  final double delta;
  final double vega;
  final double gamma;
  final double theta;
  final double delta2;
  final double charm;
  final double color;
  final double rho;
  final double impliedVolatility;
  final String expiry;
  final int oi;
  final int oiDayHigh;
  final int oiDayLow;

  OptionGreeksRestData({
    required this.zenId,
    required this.underlyingZenId,
    required this.tradingSymbol,
    required this.strike,
    required this.lastPrice,
    required this.moneyness,
    required this.delta,
    required this.vega,
    required this.gamma,
    required this.theta,
    required this.delta2,
    required this.charm,
    required this.color,
    required this.rho,
    required this.impliedVolatility,
    required this.expiry,
    required this.oi,
    required this.oiDayHigh,
    required this.oiDayLow,
  });

  factory OptionGreeksRestData.fromJson(Map<String, dynamic> json) {
    return OptionGreeksRestData(
      zenId: json['zen_id'] ?? 0,
      underlyingZenId: json['underlying_zen_id'] ?? 0,
      tradingSymbol: json['trading_symbol'] ?? '',
      strike: (json['strike'] ?? 0.0).toDouble(),
      lastPrice: (json['last_price'] ?? 0.0).toDouble(),
      moneyness: json['moneyness'] ?? '',
      delta: (json['delta'] ?? 0.0).toDouble(),
      vega: (json['vega'] ?? 0.0).toDouble(),
      gamma: (json['gamma'] ?? 0.0).toDouble(),
      theta: (json['theta'] ?? 0.0).toDouble(),
      delta2: (json['delta2'] ?? 0.0).toDouble(),
      charm: (json['charm'] ?? 0.0).toDouble(),
      color: (json['color'] ?? 0.0).toDouble(),
      rho: (json['rho'] ?? 0.0).toDouble(),
      impliedVolatility: (json['implied_volatility'] ?? 0.0).toDouble(),
      expiry: json['expiry'] ?? '',
      oi: json['oi'] ?? 0,
      oiDayHigh: json['oi_day_high'] ?? 0,
      oiDayLow: json['oi_day_low'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'zen_id': zenId,
      'underlying_zen_id': underlyingZenId,
      'trading_symbol': tradingSymbol,
      'strike': strike,
      'last_price': lastPrice,
      'moneyness': moneyness,
      'delta': delta,
      'vega': vega,
      'gamma': gamma,
      'theta': theta,
      'delta2': delta2,
      'charm': charm,
      'color': color,
      'rho': rho,
      'implied_volatility': impliedVolatility,
      'expiry': expiry,
      'oi': oi,
      'oi_day_high': oiDayHigh,
      'oi_day_low': oiDayLow,
    };
  }

  // Convert to DateTime for expiry
  DateTime get expiryDate {
    try {
      return DateTime.parse(expiry);
    } catch (e) {
      return DateTime.now();
    }
  }
}