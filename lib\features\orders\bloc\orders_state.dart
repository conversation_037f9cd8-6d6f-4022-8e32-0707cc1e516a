part of 'orders_bloc.dart';

@immutable
sealed class OrdersState {}

final class OrdersInitial extends OrdersState {
  
}

final class OrderLoading extends OrdersState{
  
}

final class PlaceOrder extends OrdersState {
  final OrderFormModel data;

  PlaceOrder({required this.data});
  
}

final class PlaceBulkOrder extends OrdersState {
  final List<OrderFormModel> data;

  PlaceBulkOrder({required this.data});
}

class BulkOrderProcessing extends OrdersState {
  final int total;
  final int processed;
  BulkOrderProcessing({required this.total, required this.processed});
}

class BulkOrderCompleted extends OrdersState {
  final String status;
  final String message;

  BulkOrderCompleted({
    required this.status,
    required this.message,
  });
}

class BulkOrderError extends OrdersState {
  final String error;
  BulkOrderError(this.error);
}


final class OrderPlaced extends OrdersState{
  final String status;
  final String message;
  final String tradingSymbol;
  final String methodType;

  OrderPlaced({ required this.tradingSymbol, required this.methodType, required this.status, required this.message});

}

final class OrderError extends OrdersState{
  final String message;
  OrderError(this.message);
}


