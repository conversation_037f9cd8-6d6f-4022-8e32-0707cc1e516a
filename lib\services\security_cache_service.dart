import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';

/// Service to manage cached security list data
class SecurityCacheService {
  static const Duration _cacheValidityDuration = Duration(hours: 24);
  static DateTime? _lastFetchTime;

  /// Check if the security list cache is valid
  static bool isCacheValid() {
    if (_lastFetchTime == null) return false;
    return DateTime.now().difference(_lastFetchTime!).abs() < _cacheValidityDuration;
  }

  /// Mark the cache as updated
  static void markCacheUpdated() {
    _lastFetchTime = DateTime.now();
  }

  /// Get the current security list state from the global bloc
  static SecurityListState getSecurityListState(BuildContext context) {
    return context.read<SecurityListBloc>().state;
  }

  /// Ensure security list is loaded and available
  static Future<void> ensureSecurityListLoaded(BuildContext context) async {
    final securityListBloc = context.read<SecurityListBloc>();
    final currentState = securityListBloc.state;

    // If the state is already loaded (regardless of cache validity), use it
    // This ensures we don't refetch data that's already available
    if (currentState is SecurityListLoaded) {
      debugPrint('Security list is already loaded, using cached data');
      markCacheUpdated(); // Update cache timestamp since we're using the data
      return;
    }

    // Only fetch if not loaded or in error state
    if (currentState is SecurityListInitial || 
        currentState is SecurityListError ||
        currentState is SecurityListLoading) {
      debugPrint('Security list not loaded, fetching data');
      
      // If not already loading, trigger fetch
      if (currentState is! SecurityListLoading) {
        securityListBloc.add(FetchSecurityListEvent());
      }
      
      // Wait for the data to be loaded
      await securityListBloc.stream.firstWhere((state) => 
        state is SecurityListLoaded || state is SecurityListError
      );
      
      if (securityListBloc.state is SecurityListLoaded) {
        markCacheUpdated();
      }
    }
  }

  /// Force refresh the security list
  static void refreshSecurityList(BuildContext context) {
    debugPrint('Forcing security list refresh');
    context.read<SecurityListBloc>().add(FetchSecurityListEvent());
    _lastFetchTime = null; // Invalidate cache
  }

  /// Get cached securities by type
  static List<SecurityModel>? getCachedSecurities(BuildContext context, String type) {
    final state = getSecurityListState(context);
    if (state is SecurityListLoaded) {
      return type == 'equity' ? state.equityList : state.featuresList;
    }
    return null;
  }

  /// Check if security list is currently loading
  static bool isLoading(BuildContext context) {
    final state = getSecurityListState(context);
    return state is SecurityListLoading;
  }

  /// Check if there's an error in security list
  static String? getError(BuildContext context) {
    final state = getSecurityListState(context);
    if (state is SecurityListError) {
      return state.error;
    }
    return null;
  }

  /// Get cached securities immediately without waiting
  /// Returns null if not loaded yet
  static List<SecurityModel>? getCachedSecuritiesSync(BuildContext context, String type) {
    final state = getSecurityListState(context);
    if (state is SecurityListLoaded) {
      debugPrint('📋 Using cached securities for type: $type');
      return type == 'equity' ? state.equityList : state.featuresList;
    }
    debugPrint('📋 No cached securities available for type: $type');
    return null;
  }

  /// Check if security list is loaded and ready to use
  static bool isSecurityListReady(BuildContext context) {
    final state = getSecurityListState(context);
    return state is SecurityListLoaded;
  }
}