import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';

///This is the side menu Drawer we use to
/// Logout
/// Accounts section
/// Settings
///
class SideDrawer extends StatelessWidget {
  const SideDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return BlocListener<AuthBloc, AuthState>(
            listener: (context, state) {
              if (state is AuthUnauthenticated) {
                Navigator.popAndPushNamed(context, '/login');
              }
            },
            child: Container(
              width: MediaQuery.of(context).size.width * 0.5,
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor(themeState.isDarkMode),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(10),
                  bottomRight: Radius.circular(10),
                ),
                boxShadow: [
                  BoxShadow(
                    blurRadius: 10,
                    color: themeState.isDarkMode
                        ? const Color(0xff383838)
                        : Colors.grey.withOpacity(0.3),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(10),
                  bottomRight: Radius.circular(10),
                ),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
                  child: Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(16),
                        height: 120,
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Menu',
                            style: TextStyle(
                              fontFamily: 'Roboto',
                              color:
                                  AppTheme.textPrimary(themeState.isDarkMode),
                              fontSize: 24,
                              fontWeight: FontWeight.w900,
                            ),
                          ),
                        ),
                      ),
                      _buildMenuItem(
                        icon: Icons.person_outline,
                        title: 'Profile',
                        onTap: () {
                          Navigator.popAndPushNamed(context, '/account');
                        },
                        themeState: themeState,
                      ),
                      SideDrawerMenuItemDivider(themeState: themeState),
                      _buildMenuItem(
                        icon: Icons.settings_outlined,
                        title: 'Settings',
                        onTap: () {
                          Navigator.popAndPushNamed(context, '/settings');
                        },
                        themeState: themeState,
                      ),
                      SideDrawerMenuItemDivider(themeState: themeState),
                      _buildMenuItem(
                        icon: Icons.trending_up_outlined,
                        title: 'Trades',
                        onTap: () {
                          Navigator.popAndPushNamed(context, '/trades');
                        },
                        themeState: themeState,
                      ),
                      SideDrawerMenuItemDivider(themeState: themeState),
                      _buildMenuItem(
                        icon: Icons.analytics_outlined,
                        title: 'Option Chain',
                        onTap: () {
                          Navigator.popAndPushNamed(context, '/option-chain');
                        },
                        themeState: themeState,
                      ),
                      SideDrawerMenuItemDivider(themeState: themeState),
                      const Spacer(),
                      _buildLogoutItem(
                        icon: Icons.logout,
                        title: 'Logout',
                        onTap: () {
                          debugPrint("Logout");
                          context.read<AuthBloc>().add(AuthLogoutEvent());
                        },
                        context: context,
                        themeState: themeState,
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ));
      },
    );
  }

  Widget _buildLogoutItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    required BuildContext context,
    required ThemeState themeState,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: () {
            //Show a confirmation dialog
            showDialog(
              context: context, // Make sure you have access to the context
              builder: (BuildContext context) {
                return AlertDialog(
                  title: Text("Confirm Logout"),
                  content: Text("Are you sure you want to log out?"),
                  actions: <Widget>[
                    TextButton(
                      child: Text("Cancel"),
                      onPressed: () {
                        Navigator.of(context).pop(); // Close the dialog
                      },
                    ),
                    TextButton(
                      child: Text("Logout"),
                      onPressed: () {
                        onTap(); // Call the logout function
                      },
                    ),
                  ],
                );
              },
            );
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            child: Row(
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Roboto',
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 24,
                    fontWeight: FontWeight.w900,
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                ImageIcon(
                  AssetImage("images/logout_icon.png"),
                  size: 24,
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    required ThemeState themeState,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Roboto',
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // const Spacer(),
                Icon(
                  Icons.arrow_forward,
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class SideDrawerMenuItemDivider extends StatelessWidget {
  final ThemeState themeState;

  const SideDrawerMenuItemDivider({super.key, required this.themeState});

  @override
  Widget build(BuildContext context) {
    return Divider(
      color: AppTheme.borderColor(themeState.isDarkMode),
      indent: 8,
      endIndent: 8,
      height: 1,
    );
  }
}
