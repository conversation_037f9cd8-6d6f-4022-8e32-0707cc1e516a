import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:phoenix/features/portfolio_data/data/repository/portfolio_repository.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';

part 'portfolio_event.dart';
part 'portfolio_state.dart';

class PortfolioBloc extends Bloc<PortfolioEvent, PortfolioState> {
  final PortfolioRepository _portfolioRepository;

  PortfolioBloc(this._portfolioRepository) : super(PortfolioInitial()) {
    on<FetchPortfolio>(_onFetchPortfolio);
  }

  void _onFetchPortfolio(
    FetchPortfolio event,
    Emitter<PortfolioState> emit,
  ) async {
    debugPrint("reach _onFetchPortfolio");
    emit(PortfolioLoading());
    try {
      final positions = await _portfolioRepository.getPortfolioData(event.clientId);
      emit(PortfolioLoaded(positions["open_positions"]!, positions["closed_positions"]!));
    } catch (e) {
      emit(PortfolioError(e.toString()));
    }
  }

}
