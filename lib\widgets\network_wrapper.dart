import 'dart:async';
import 'package:flutter/material.dart';
import 'package:phoenix/services/network_service.dart';
import 'package:phoenix/widgets/no_internet_page.dart';

class NetworkWrapper extends StatefulWidget {
  final Widget child;
  final VoidCallback? onRetry;
  
  const NetworkWrapper({
    super.key,
    required this.child,
    this.onRetry,
  });

  @override
  State<NetworkWrapper> createState() => _NetworkWrapperState();
}

class _NetworkWrapperState extends State<NetworkWrapper> {
  late StreamSubscription<bool> _networkSubscription;
  bool _isConnected = true;
  bool _hasShownNoInternet = false;

  @override
  void initState() {
    super.initState();
    _initializeNetworkListener();
  }

  void _initializeNetworkListener() {
    // Get initial network status
    _isConnected = NetworkService().isConnected;
    
    // Listen to network status changes
    _networkSubscription = NetworkService().networkStatusStream.listen(
      (bool isConnected) {
        if (mounted) {
          setState(() {
            _isConnected = isConnected;
            
            // Reset the flag when connection is restored
            if (isConnected) {
              _hasShownNoInternet = false;
            }
          });
        }
      },
    );
  }

  @override
  void dispose() {
    _networkSubscription.cancel();
    super.dispose();
  }

  void _handleRetry() {
    // Call the provided retry callback if available
    widget.onRetry?.call();
    
    // Reset the flag to allow showing no internet page again if needed
    _hasShownNoInternet = false;
  }

  @override
  Widget build(BuildContext context) {
    // Show no internet page if not connected
    if (!_isConnected) {
      _hasShownNoInternet = true;
      return NoInternetPage(
        onRetry: _handleRetry,
      );
    }
    
    // Show the actual content when connected
    return widget.child;
  }
}