import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:phoenix/features/latest_zen_order_state/model/latest_order.dart';
import 'package:phoenix/managers/api/api_response_manager.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/app_exception.dart';
import 'package:phoenix/utils/http_service.dart';

class LatestZenOrderStateProvider {
  Future<LatestOrder?> fetchLatestOrderState(int zenOrderId, int clientId) async {
    final HttpService httpService = HttpService();
    try {
      final uri = ApiPath.getLatestOrder(zenOrderId,clientId);
      final response = await httpService.get(Uri.parse(uri));
      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if(apiResponse.code != 200 || apiResponse.status != 'SUCCESS' || response.statusCode != 200 ){
        throw AppException(apiResponse.message);
      }else{

        final jsonData = apiResponse.payload;

        if (jsonData == null || jsonData.isEmpty) {
          return null; // Return null if response is empty
        }

        return LatestOrder.fromJson(jsonData);

      }
    } catch (e) {
      debugPrint("Error fetching latest order state: $e");
      return null; // Return null on error
    }
  }
}
