import 'package:phoenix/features/common/position_comp_key.dart';

class ZenOrderStateModel {
  final PositionCompKey positionCompKey;
  final int zenOrderId;
  final String brokerTimestamp;
  final String status;
  final String message;
  final String tradingSymbol;

  ZenOrderStateModel({
    required this.positionCompKey,
    required this.zenOrderId,
    required this.brokerTimestamp,
    required this.status,
    required this.message,
    required this.tradingSymbol,
  });

  factory ZenOrderStateModel.fromJson(Map<String, dynamic> json) {
    return ZenOrderStateModel(
      positionCompKey: PositionCompKey.fromJson(json['positionCompKey']),
      zenOrderId: json['zenOrderId'],
      brokerTimestamp: json['brokerTimestamp'],
      status: json['status'],
      message: json['message'] ?? '',
      tradingSymbol: json['tradingSymbol'],
    );
  }

  static List<ZenOrderStateModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => ZenOrderStateModel.fromJson(json)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'positionCompKey': positionCompKey.toJson(),
      'zenOrderId': zenOrderId,
      'brokerTimestamp': brokerTimestamp,
      'status': status,
      'message': message,
      'tradingSymbol': tradingSymbol,
    };
  }
}