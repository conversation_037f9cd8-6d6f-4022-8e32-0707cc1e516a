import 'package:flutter/material.dart';
import 'package:phoenix/phoenix_app.dart';
import 'package:phoenix/services/logger_service.dart';
import 'package:phoenix/services/network_service.dart';
import 'package:phoenix/services/shared_prefrences_service.dart';
import 'package:phoenix/utils/error_handler.dart';
import 'package:sentry_flutter/sentry_flutter.dart';


void main() async {
  //To ensure all the bundel binngs are done
  WidgetsFlutterBinding.ensureInitialized();
  
  //To init a single instace of service which will be used to access the prefs
  await SharedPreferencesService.initialize();
  
  //Initialize the logger service
  await LoggerService().initialize();
  
  //Initialize network service
  await NetworkService().initialize();
  
  //Initialize global error handler
  GlobalErrorHandler.initialize();
  
  //Log app start
  LoggerService().info('Application started');

  //This is the main entry point
  // await SentryFlutter.init(
  //   (options) {
  //     options.dsn = 'https://<EMAIL>/4509446173097984';
  //     // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
  //     // We recommend adjusting this value in production.
  //     options.tracesSampleRate = 1.0;
  //     // The sampling rate for profiling is relative to tracesSampleRate
  //     // Setting to 1.0 will profile 100% of sampled transactions:
  //     options.profilesSampleRate = 1.0;
  //   },
  //   appRunner: () => runApp(SentryWidget(child: PhoenixApp())),
    
  // );
  runApp(PhoenixApp());
}


