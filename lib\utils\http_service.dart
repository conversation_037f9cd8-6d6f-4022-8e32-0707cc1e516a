import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:http/http.dart' as http;
import 'package:phoenix/services/shared_prefrences_service.dart';
import 'package:phoenix/features/authentication/data/data_provider/auth_service.dart';

class HttpService extends http.BaseClient {
  final http.Client _inner = http.Client();
  String? _accessToken;

  HttpService() {
    _accessToken = SharedPreferencesService.instance.accessToken;
  }

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    debugPrint(_accessToken);
    if (_accessToken != null) {
      request.headers['Authorization'] = 'Bearer $_accessToken';
    }

    http.StreamedResponse response = await _inner.send(request);

    if (response.statusCode == 401 || response.statusCode == 403) {
      // Token might be expired, try refreshing it
      debugPrint("🔁 Received 401, trying to refresh access token");

      final authService = AuthService();
      final newToken = await authService.refreshAccessToken(_accessToken ?? '');

      if (newToken != _accessToken) {
        // Token updated, retry the request with the new token
        _accessToken = newToken;
        //this will store the new access token to our shared prefrences
        //we are using set method of shared prefrence service
        SharedPreferencesService.instance.accessToken = newToken;

        // Clone the original request
        final newRequest = _cloneRequest(request, newToken);
        response = await _inner.send(newRequest);
      }
    }

    return response;
  }

  http.BaseRequest _cloneRequest(http.BaseRequest request, String newToken) {
    final clonedRequest = http.Request(request.method, request.url)
      ..headers.addAll(request.headers)
      ..headers['Authorization'] = 'Bearer $newToken';

    if (request is http.Request) {
      clonedRequest.bodyBytes = request.bodyBytes;
    }

    return clonedRequest;
  }
}
