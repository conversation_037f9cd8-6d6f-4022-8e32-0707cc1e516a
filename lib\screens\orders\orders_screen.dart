import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/orders_state/bloc/orders_state_bloc.dart';
import 'package:phoenix/features/orders_state/model/unified_order_data.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/screens/orders/orders_list_builder.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/search_refresh_sort_bar/search_refresh_sort_bar.dart';
import 'package:phoenix/widgets/tab_bar/app_tab_bar2.dart';
import 'package:phoenix/widgets/toast/custom_toast.dart';

import '../../widgets/search/custom_search_bar.dart';
import 'package:phoenix/services/sort_service.dart';

/// Orders Screen
/// This screen is used to display the orders list and the order form
/// We also subscribe to the websocket to get the price updates for the current zenIds in the orders list
class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  _OrdersScreenState createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen>
    with TickerProviderStateMixin {
  final SortService _sortService = SortService.instance;

  ///order form bottom sheet animation
  late final AnimationController _formSheetAnimeController;

  bool _showSearch = false;
  final TextEditingController _searchController = TextEditingController();

  SortOption? _currentSortOption;
  bool _isAscending = true;

  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      if (!_showSearch) {
        _searchController.clear();
      }
    });
  }

  void _showSortOptions() {
    _sortService.showSortOptions(
      context: context,
      currentSortOption: _currentSortOption,
      isAscending: _isAscending,
      onSortChanged: (option, ascending) {
        setState(() {
          _currentSortOption = option;
          _isAscending = ascending;
        });
      },
      availableOptions: [
        SortOption.alphabetical,
        SortOption.invested,
        SortOption.date,
        SortOption.lastTradedPrice
      ],
    );
  }

  List<UnifiedOrderData> _sortOrders(List<UnifiedOrderData> orders) {
    return _sortService.sortList(
      items: orders,
      sortOption: _currentSortOption,
      isAscending: _isAscending,
      sortFunctions: {
        SortOption.alphabetical: (order) =>
            order.orderFillState.last.tradingSymbol,
        SortOption.invested: (order) => order.orderFillState.last.averagePrice,
        SortOption.date: (order) =>
            order.orderFillState.last.exchangeTimestamp!,
        SortOption.lastTradedPrice: (order) =>
            order.orderFillState.last.averagePrice,
      },
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    debugPrint("🪄 Orders Screen init");
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthUnauthenticated) {
      Navigator.pushReplacementNamed(context, '/login');
    }

    ///Order form pop up Animation
    _formSheetAnimeController = BottomSheet.createAnimationController(this);
    _formSheetAnimeController.duration = Duration(milliseconds: 850);

    if (authState is AuthAuthenticated) {
      context.read<OrdersStateBloc>().add(FetchOrdersState(
            clientId: authState.credentialsModel.clientId,
          ));
    } else {
      // Handle unauthenticated case (e.g., show login dialog)
      ZenSnackbar.show(
        context,
        "You need to log in first",
      );
    }
  }

  bool _matchesSearchQuery(UnifiedOrderData order, String query) {
    return order.orderFillState.last.tradingSymbol
        .toLowerCase()
        .contains(query.toLowerCase());
  }

  void _updateWebSocketSubscriptions(List<UnifiedOrderData> openOrders) {
    Set<int> stockIds =
        openOrders.map((e) => e.positionCompKey.zenSecId).toSet();
    context
        .read<WebSocketBloc>()
        .add(WebSocketSelectMultipleStocks(stockIds.toList()));

    debugPrint(
      "🔄 Updating WebSocket subscriptions for open orders - ${stockIds.length} length",
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return DefaultTabController(
          length: 2,
          child: Scaffold(
            backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
        resizeToAvoidBottomInset: true,
        body: Column(
          children: [
            BlocBuilder<OrdersStateBloc, OrdersStateState>(
              builder: (context, ordersState) {
                int openCount = 0;
                int executedCount = 0;

                if (ordersState is OrdersStateLoaded) {
                  openCount = ordersState.data
                      .where((order) => [
                            "update",
                            "pending",
                            "trigger_pending",
                            "in_progress",
                            "open"
                          ].contains(order.status.toLowerCase()))
                      .length;

                  executedCount = ordersState.data
                      .where((order) => ![
                            "update",
                            "pending",
                            "trigger_pending",
                            "in_progress",
                            "open"
                          ].contains(order.status.toLowerCase()))
                      .length;
                }

                return AppTabBar2(
                  count1: openCount,
                  count2: executedCount,
                  title1: "Open",
                  title2: "Executed",
                );
              },
            ),
            // Refresh, Search, and Sort Buttons
            SearchRefreshSortBar(
              refresh: () {
                final authState = context.read<AuthBloc>().state;
                if (authState is AuthAuthenticated) {
                  context.read<OrdersStateBloc>().add(FetchOrdersState(
                        clientId: authState.credentialsModel.clientId,
                      ));
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text("You need to log in first"),
                    ),
                  );
                }
              },
              toggleSearch: _toggleSearch,
              showSortOptions: _showSortOptions,
              isAscending: _isAscending,
              currentSortOption: _currentSortOption,
            ),
            if (_showSearch)
              CustomSearchBar(
                controller: _searchController,
                hintText: 'Search...',
                autofocus: true,
                onSearch: (query) {
                  // Handle search here
                  setState(() {
                    // Update your filtered data here
                  });
                },
                onClose: _toggleSearch,
              ),
            Expanded(
              child: BlocListener<OrdersStateBloc, OrdersStateState>(
                listener: (context, state) {
                  if (state is OrdersStateError) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error ${state.error}'),
                        backgroundColor: Colors.red,
                        duration: Duration(seconds: 3),
                      ),
                    );
                  }
                },
                child: BlocBuilder<OrdersStateBloc, OrdersStateState>(
                  builder: (context, state) {
                    if (state is OrdersStateLoading) {
                      return const Center(
                        child: CircularLoader(),
                      );
                    } else if (state is OrdersStateLoaded) {
                      final openData = state.data
                          .where((order) => [
                                "update",
                                "pending",
                                "trigger_pending",
                                "open",
                                "in_progress",
                                "stop_los_hit",
                                "trigger_pending_with_error",
                              ].contains(order.status.toLowerCase()))
                          .toList();

                      _updateWebSocketSubscriptions(openData);

                      final executedData = state.data
                          .where((order) => ![
                                "update",
                                "pending",
                                "trigger_pending",
                                "open",
                                "in_progress",
                                "stop_los_hit",
                                "trigger_pending_with_error",
                              ].contains(order.status.toLowerCase()))
                          .toList();

                      return TabBarView(
                        children: [
                          OrdersListBuilder(
                            data: _sortOrders(_searchController.text.isEmpty
                                ? openData
                                : openData
                                    .where((order) => _matchesSearchQuery(
                                        order, _searchController.text))
                                    .toList()),
                            emptyMessage: "No Orders Placed",
                            formSheetAnimeController: _formSheetAnimeController,
                            isOrderOpen: true,
                          ),
                          OrdersListBuilder(
                            data: _sortOrders(_searchController.text.isEmpty
                                ? executedData
                                : executedData
                                    .where((order) => _matchesSearchQuery(
                                        order, _searchController.text))
                                    .toList()),
                            emptyMessage: "No Orders Executed",
                            formSheetAnimeController: _formSheetAnimeController,
                          ),
                        ],
                      );
                    } else if (state is OrdersStateError) {
                      return Center(
                        child: Text(
                          'Something went wrong. ${state.error}',
                          style: TextStyle(color: AppTheme.textPrimary(themeState.isDarkMode)),
                        ),
                      );
                    } else {
                      return Center(
                        child: Text(
                          'Something went wrong.',
                          style: TextStyle(color: AppTheme.textPrimary(themeState.isDarkMode)),
                        ),
                      );
                    }
                  },
                ),
              ),
            ),
          ],
        ),
        )
        );
      },
    );
  }
}
