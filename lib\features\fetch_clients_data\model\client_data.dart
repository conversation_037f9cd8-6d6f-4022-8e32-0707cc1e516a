class ClientData {
  final int clientId;
  final String clientName;
  final List<String> permissions;

  ClientData({
    required this.clientId,
    required this.clientName,
    required this.permissions,
  });

  factory ClientData.fromJson(Map<String, dynamic> json) {
    return ClientData(
      clientId: json['client_id'],
      clientName: json['client_name'],
      permissions: List<String>.from(json['permissions']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'client_id': clientId,
      'client_name': clientName,
      'permissions': permissions,
    };
  }
}