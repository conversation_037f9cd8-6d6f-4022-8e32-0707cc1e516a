import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';

class BrokerDropDown extends StatelessWidget {
  final String? selectedValue;
  final void Function(String) action;
  final List<String> list;

  const BrokerDropDown({
    super.key,
    required this.selectedValue,
    required this.action,
    required this.list,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(2),
      alignment: Alignment.center,
      height: 40,
      width: 170,
      decoration: BoxDecoration(
        color: Color(0xff353535),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: DropdownButtonHideUnderline(
          child: DropdownButton2<String>(
            hint: Text(
              "Broker",
              style: TextStyle(
                fontSize: 16,
                color: Color(0xffADADAD),
                fontWeight: FontWeight.w400,
              ),
            ),
            isExpanded: true,
            items: list
                .map(
                  (item) => DropdownMenuItem(
                    
                    value: item,
                    child: Container(
                      
                      
                      // decoration: BoxDecoration(
                      //   border: Border(bottom: BorderSide(
                      //      color: Color(0xff474747),
                      //      width: 2
                      //   ))
                      // ),
                      child: Text(
                        textAlign: TextAlign.center,
                        item,
                        style: TextStyle(
                          
                          fontSize: 15,
                          color: Color(0xffADADAD),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                )
                .toList(),
            value: selectedValue,
            onChanged: (value) {
              action(value!);
            },
            buttonStyleData: const ButtonStyleData(
              padding: EdgeInsets.symmetric(horizontal: 16),
              height: 100,
              width: 400,
            ),
            dropdownStyleData: DropdownStyleData(
              decoration: BoxDecoration(
                color: Color(0xff353535),
                //backgroundBlendMode: BlendMode.darken
                borderRadius: BorderRadius.all(Radius.circular(10)),
                shape: BoxShape.rectangle,
              ),
              //scrollbarTheme: ScrollbarThemeData(thumbColor: WidgetStateProperty.all(ThemeConstants.floatingActionButtonColor)),
              maxHeight: 500,
              //This is to adjust the pop up to center
            ),
            menuItemStyleData: const MenuItemStyleData(
              height: 40,
            ),
          ),
        ),
      ),
    );
  }
}
