import 'package:phoenix/features/authentication/model/credentials_model.dart';

class BrokerDataMapperService {
  final List<BrokerInfo> brokers;

  late final Map<int, String> accountIdToNameMap;
  late final Map<int, String> strategyIdToNameMap;
  late final Map<String, String> brokerNameToLabelMap; // brokerName is String, not ID

  BrokerDataMapperService({required this.brokers}) {
    _generateMaps();
  }

  void _generateMaps() {
    accountIdToNameMap = {};
    strategyIdToNameMap = {};
    brokerNameToLabelMap = {};

    for (final broker in brokers) {
      brokerNameToLabelMap[broker.brokerName] = broker.brokerName;

      for (final account in broker.accounts) {
        accountIdToNameMap[account.accountId] = account.accountName;

        for (final strategy in account.strategies) {
          strategyIdToNameMap[strategy.strategyId] = strategy.strategyName;
        }
      }
    }
  }
}
