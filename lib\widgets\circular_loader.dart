import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';

class CircularLoader extends StatelessWidget {
  const CircularLoader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return CircularProgressIndicator(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          valueColor: AlwaysStoppedAnimation(AppTheme.primaryColor(themeState.isDarkMode)),
          strokeWidth: 3,
          strokeCap: StrokeCap.square,
        );
      },
    );
  }
}
