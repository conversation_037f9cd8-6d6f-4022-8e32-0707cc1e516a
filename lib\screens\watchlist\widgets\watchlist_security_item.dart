import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:intl/intl.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/screens/orders/order_form_bottom_sheet.dart';
import 'package:phoenix/screens/orders/order_type.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/custom/dismissible/zen_dismissible.dart';
import 'package:phoenix/widgets/order_form/security_list_dropdown/security_list_text_formatter.dart';
import '../../../features/orders/model/spider_order_data.dart';
import '../../../features/theme/bloc/theme_bloc.dart';
import '../../../features/theme/bloc/theme_state.dart';
import 'slide_buy_background.dart';
import 'slide_sell_background.dart';

class WatchlistSecurityItem extends StatefulWidget {
  final SecurityModel security;
  final double? price;
  final double sodPrice;
  final PositionsModel position;
  final bool isReorderable;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onBuy;
  final VoidCallback? onSell;

  const WatchlistSecurityItem({
    super.key,
    required this.security,
    this.price,
    required this.sodPrice,
    required this.position,
    this.isReorderable = true,
    this.isSelected = false,
    this.onTap,
    this.onBuy,
    this.onSell,
  });

  @override
  State<WatchlistSecurityItem> createState() => _WatchlistSecurityItemState();
}

class _WatchlistSecurityItemState extends State<WatchlistSecurityItem> {
  late AnimationController _formSheetAnimeController;

  @override
  void initState() {
    super.initState();
    _formSheetAnimeController =
        BottomSheet.createAnimationController(Navigator.of(context));
    _formSheetAnimeController.duration = const Duration(milliseconds: 850);
  }

  @override
  void dispose() {
    _formSheetAnimeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        final currencyFormat =
            NumberFormat.currency(locale: 'en_IN', symbol: '₹');
        final change = widget.price != null && widget.sodPrice != 0
            ? widget.price! - widget.sodPrice
            : 0.0;
        final percentageChange = widget.price != null && widget.sodPrice != 0
            ? ((widget.price! - widget.sodPrice) / widget.sodPrice) * 100
            : 0.0;
        final isGain = change >= 0;
        final qty = widget.position.position;

        Widget listTile = AnimatedContainer(
          duration: const Duration(milliseconds: 350),
          curve: Curves.easeInOutCubic,
          margin: EdgeInsets.symmetric(
            horizontal: widget.isSelected ? 2 : 0,
            vertical: widget.isSelected ? 2 : 0,
          ),
          transform: Matrix4.identity()..scale(widget.isSelected ? 0.98 : 1.0),
          decoration: BoxDecoration(
            color: widget.isSelected
                ? AppTheme.surfaceColor(themeState.isDarkMode)
                : AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(16),
            border: widget.isSelected
                ? Border.all(
                    color: AppTheme.primaryColor(themeState.isDarkMode),
                    width: 1.5,
                  )
                : Border.all(
                    color: Colors.transparent,
                    width: 1.5,
                  ),
            boxShadow: widget.isSelected
                ? [
                    BoxShadow(
                      color: AppTheme.primaryColor(themeState.isDarkMode)
                          .withValues(alpha: 0.15),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : ThemeConstants.getNeomorpicShadow(themeState.isDarkMode),
          ),
          child: ListTile(
            onTap: widget.onTap,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
            leading: widget.isSelected ?AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (child, animation) {
                return ScaleTransition(
                  scale: animation,
                  child: FadeTransition(
                    opacity: animation,
                    child: child,
                  ),
                );
              },
              child:  Container(
                      key: const ValueKey('selected'),
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor(themeState.isDarkMode),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppTheme.primaryColor(themeState.isDarkMode),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.primaryColor(themeState.isDarkMode)
                                .withValues(alpha: 0.3),
                            blurRadius: 4,
                            spreadRadius: 0,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 14,
                      ),
                    )
            ): null,
            title: AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 350),
              curve: Curves.easeInOutCubic,
              style: TextStyle(
                fontWeight:
                    widget.isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: 15,
                color: widget.isSelected
                    ? AppTheme.primaryColor(themeState.isDarkMode)
                    : AppTheme.textPrimary(themeState.isDarkMode),
              ),
              child: TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 350),
                curve: Curves.easeInOutCubic,
                tween: Tween(begin: 0.0, end: widget.isSelected ? 1.0 : 0.0),
                builder: (context, value, child) {
                  return Transform.translate(
                    offset: Offset(value * 2, 0),
                    child: child,
                  );
                },
                child: Text(
                  SecurityListTextFormatter.format(
                      widget.security, widget.security.instrumentType),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            subtitle: AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 350),
              curve: Curves.easeInOutCubic,
              style: TextStyle(
                color: widget.isSelected
                    ? AppTheme.primaryColor(themeState.isDarkMode)
                        .withValues(alpha: 0.7)
                    : AppTheme.textSecondary(themeState.isDarkMode),
                fontSize: 13,
                fontWeight: FontWeight.w400,
              ),
              child: TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 350),
                curve: Curves.easeInOutCubic,
                tween: Tween(begin: 0.0, end: widget.isSelected ? 1.0 : 0.0),
                builder: (context, value, child) {
                  return Transform.translate(
                    offset: Offset(value * 2, 0),
                    child: child,
                  );
                },
                child: Text(widget.security.name),
              ),
            ),
            trailing: _buildTrailingWidget(
              qty: qty,
              price: widget.price,
              change: change,
              percentageChange: percentageChange,
              isGain: isGain,
              currencyFormat: currencyFormat,
              isReorderable: widget.isReorderable,
              isSelected: widget.isSelected,
              themeState: themeState,
            ),
          ),
        );

        // Only enable swipe for buy/sell when not in selection mode
        if (!widget.isSelected && widget.isReorderable) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: _buildSwipeableItem(listTile),
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          child: listTile,
        );
      },
    );
  }

  Widget _buildSwipeableItem(Widget child) {
    return ZenDismissible(
      key: Key('${widget.security.tradingSymbol}_swipeable'),
      background: const SlideSellBackground(),
      secondaryBackground: const SlideBuyBackground(),
      dismissThresholds: const {
        ZenDismissDirection.endToStart: 0.20, // Buy threshold
        ZenDismissDirection.startToEnd: 0.20, // Sell threshold
      },
      direction: DismissDirection.horizontal,
      movementDuration: const Duration(milliseconds: 200),
      confirmDismiss: _handleSwipeAction,
      child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(20)),
          child: child),
    );
  }

  Future<bool?> _handleSwipeAction(DismissDirection direction) async {
    final action = direction == DismissDirection.startToEnd ? 'SELL' : 'BUY';

    debugPrint(
        'Swipe handler triggered for action: $action on ${widget.security.tradingSymbol}');

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      transitionAnimationController: _formSheetAnimeController,
      builder: (modalContext) {
        return SingleChildScrollView(
          reverse: true,
          child: OrderFormSheet(
            openOrderType: action == 'BUY'
                ? FormOpenOrderType.buy
                : FormOpenOrderType.sell,
            spiderMetaData: SpiderOrderData(
              zenId: widget.security.zenId,
              tradingSymbol: widget.security.tradingSymbol,
              transactionType: action,
            ),
          ),
        );
      },
    );

    // Return false to prevent actual dismissal
    return false;
  }

  Widget _buildTrailingWidget({
    required int qty,
    required double? price,
    required double change,
    required double percentageChange,
    required bool isGain,
    required NumberFormat currencyFormat,
    required bool isReorderable,
    required bool isSelected,
    required ThemeState themeState,
  }) {
    return SizedBox(
      height: 70,
      width: 110,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (qty > 0)
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedDefaultTextStyle(
                  duration: const Duration(milliseconds: 350),
                  curve: Curves.easeInOutCubic,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: isSelected
                        ? AppTheme.primaryColor(themeState.isDarkMode)
                        : AppTheme.textSecondary(themeState.isDarkMode),
                  ),
                  child: Text('Qty: $qty'),
                ),
                const SizedBox(width: 5),
                const ImageIcon(
                  AssetImage("images/tile-generic/qty_icon.png"),
                  color: ThemeConstants.blue,
                  size: 10,
                ),
              ],
            ),
          const SizedBox(height: 2),
          AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 350),
            curve: Curves.easeInOutCubic,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: isSelected ? 15 : 14,
              color: isSelected
                  ? AppTheme.primaryColor(themeState.isDarkMode)
                  : AppTheme.textPrimary(themeState.isDarkMode),
            ),
            child: Text(
              price != null
                  ? currencyFormat.format(price)
                  : (isReorderable ? "---" : "N/A"),
            ),
          ),
          const SizedBox(height: 4),
          AnimatedContainer(
            duration: const Duration(milliseconds: 350),
            curve: Curves.easeInOutCubic,
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: isGain
                  ? ThemeConstants.tileGreenColor.withValues(alpha: 0.1)
                  : ThemeConstants.titleRedColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: isSelected
                  ? Border.all(
                      color: isGain
                          ? ThemeConstants.tileGreenColor.withValues(alpha: 0.3)
                          : ThemeConstants.titleRedColor.withValues(alpha: 0.3),
                      width: 1,
                    )
                  : null,
            ),
            child: AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 350),
              curve: Curves.easeInOutCubic,
              style: TextStyle(
                color: isGain
                    ? ThemeConstants.tileGreenColor
                    : ThemeConstants.titleRedColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: 11,
              ),
              child: Text(
                '${change.toStringAsFixed(2)} (${percentageChange.toStringAsFixed(2)}%)',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
