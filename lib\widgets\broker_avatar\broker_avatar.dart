import 'package:flutter/material.dart';

class BrokerAvatar extends StatelessWidget {
  const BrokerAvatar({super.key, required this.brokerName});

  final String brokerName;

  // Map of known brokers to their asset paths
  static const Map<String, String> _brokerLogos = {
    "ZEN_BROKER": "zen_broker.png",
    "ZERODHA": "zerodha.png",
    "KOTAK": "kotak.png",
  };

  bool get _hasLogo => _brokerLogos.containsKey(brokerName);

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: brokerName,
      child: _hasLogo
          ? CircleAvatar(
              radius: 8,
              backgroundImage: AssetImage("images/brokers/${_brokerLogos[brokerName]}"),
              backgroundColor: Colors.transparent,
            )
          : CircleAvatar(
              radius: 8,
              backgroundColor: Colors.grey.shade300,
              child: Text(
                brokerName.isNotEmpty ? brokerName[0].toUpperCase() : '?',
                style: const TextStyle(
                  fontSize: 8,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ),
    );
  }
}
