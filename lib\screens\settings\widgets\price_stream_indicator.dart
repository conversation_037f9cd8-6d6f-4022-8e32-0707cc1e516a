import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class PriceStreamIndicator extends StatelessWidget {
 
  final String label;

  const PriceStreamIndicator({
    Key? key,
   
    this.label = 'Price Stream',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return BlocBuilder<WebSocketBloc, WebSocketState>(
          builder: (context, state) {
        debugPrint(state.toString());

        bool isLive = false;
        if(state is WebSocketDisconnected || state is WebSocketError || state is WebSocketInitial || state is WebSocketConnecting){
          isLive = false;
        } else{
          if(state is WebSocketDataReceived){
            debugPrint(state.tickList!.length.toString());
            if(state.tickList!.isNotEmpty){
              isLive = true;
            }
          } else if (state is WebSocketMultipleStockPricesUpdated){
            debugPrint(state.stockPrices.length.toString());
            if(state.stockPrices.isNotEmpty){
              isLive = true;
            }
          }
        }

            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.cardColor(themeState.isDarkMode),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.borderColor(themeState.isDarkMode),
                  width: 1,
                ),
              ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
                Text(
                  label,
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              const Spacer(),
              Row(
                children: [
                  if (isLive)
                    Text(
                      'Live',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  const SizedBox(width: 4),
                  Icon(
                    isLive ? Icons.wifi : Icons.wifi_off,
                    size: 24,
                    color: isLive ? Colors.green : Colors.red,
                  ),
                ],
              ),
              ],
            ),
          );
          },
        );
      },
    );
  }
}
