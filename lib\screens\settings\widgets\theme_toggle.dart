import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_event.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class ThemeToggle extends StatelessWidget {
  const ThemeToggle({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor(state.isDarkMode),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppTheme.borderColor(state.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: state.isDarkMode 
                    ? Colors.black.withOpacity(0.3)
                    : Colors.grey.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  // Theme icon with background
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor(state.isDarkMode).withOpacity(0.15),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      state.isDarkMode 
                          ? CupertinoIcons.moon_stars_fill 
                          : CupertinoIcons.sun_max_fill,
                      color: AppTheme.primaryColor(state.isDarkMode),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 14),
                  // Title and subtitle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Theme',
                          style: TextStyle(
                            color: AppTheme.textPrimary(state.isDarkMode),
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.2,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          state.isDarkMode ? 'Dark mode' : 'Light mode',
                          style: TextStyle(
                            color: AppTheme.textSecondary(state.isDarkMode),
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Modern toggle switch
                  _buildModernToggle(context, state),
                ],
              ),
              const SizedBox(height: 16),
              // Theme preview cards
              Row(
                children: [
                  Expanded(
                    child: _buildThemePreviewCard(
                      context: context,
                      isDarkMode: true,
                      isSelected: state.isDarkMode,
                      onTap: () => context.read<ThemeBloc>().add(SetDarkTheme()),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildThemePreviewCard(
                      context: context,
                      isDarkMode: false,
                      isSelected: !state.isDarkMode,
                      onTap: () => context.read<ThemeBloc>().add(SetLightTheme()),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildModernToggle(BuildContext context, ThemeState state) {
    return GestureDetector(
      onTap: () => context.read<ThemeBloc>().add(ToggleTheme()),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        width: 56,
        height: 32,
        padding: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: state.isDarkMode
              ? AppTheme.primaryColor(state.isDarkMode).withOpacity(0.8)
              : AppTheme.borderColor(state.isDarkMode),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: state.isDarkMode
                  ? AppTheme.primaryColor(state.isDarkMode).withOpacity(0.3)
                  : Colors.grey.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: AnimatedAlign(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          alignment: state.isDarkMode ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Icon(
              state.isDarkMode 
                  ? CupertinoIcons.moon_fill
                  : CupertinoIcons.sun_max_fill,
              color: state.isDarkMode
                  ? AppTheme.primaryColor(state.isDarkMode)
                  : Colors.orange,
              size: 16,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildThemePreviewCard({
    required BuildContext context,
    required bool isDarkMode,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final previewBg = isDarkMode ? const Color(0xff24272C) : const Color(0xffF8F9FA);
    final previewText = isDarkMode ? const Color(0xffCDCDCD) : const Color(0xff2C3E50);
    final previewCard = isDarkMode ? const Color(0xff353535) : Colors.white;

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: previewBg,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? AppTheme.blue
                : Colors.transparent,
            width: 2,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppTheme.blue.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
        ),
        child: Column(
          children: [
            // Preview header
            Container(
              width: double.infinity,
              height: 16,
              decoration: BoxDecoration(
                color: previewCard,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  const SizedBox(width: 6),
                  Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: previewText.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 2),
                  Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: previewText.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 2),
                  Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: previewText.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 6),
            // Preview content
            Container(
              width: double.infinity,
              height: 8,
              decoration: BoxDecoration(
                color: previewText.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 4),
            Container(
              width: double.infinity,
              height: 6,
              decoration: BoxDecoration(
                color: previewText.withOpacity(0.1),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 8),
            // Theme label
            Text(
              isDarkMode ? 'Dark' : 'Light',
              style: TextStyle(
                color: previewText,
                fontSize: 11,
                fontWeight: FontWeight.w600,
              ),
            ),
            // Selected indicator
            if (isSelected)
              Container(
                margin: const EdgeInsets.only(top: 4),
                width: 6,
                height: 6,
                decoration: BoxDecoration(
                  color: AppTheme.blue,
                  shape: BoxShape.circle,
                ),
              ),
          ],
        ),
      ),
    );
  }
}