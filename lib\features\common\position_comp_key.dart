class PositionCompKey {
  final int zenSecId;
  final int clientId;
  final String broker;
  final int accountId;
  final int strategyId;

  PositionCompKey({
    required this.zenSecId,
    required this.clientId,
    required this.broker,
    required this.accountId,
    required this.strategyId,
  });

  factory PositionCompKey.fromJson(Map<String, dynamic> json) {
    return PositionCompKey(
      zenSecId: json['zen_id'],
      clientId: json['client_id'],
      broker: json['broker'],
      accountId: json['account_id'],
      strategyId: json['strategy_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'zen_id': zenSecId,
      'client_id': clientId,
      'broker': broker,
      'account_id': accountId,
      'strategy_id': strategyId,
    };
  }
}