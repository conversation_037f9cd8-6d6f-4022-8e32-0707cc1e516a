//
//  Generated code. Do not modify.
//  source: zen_tick.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use zenTickDescriptor instead')
const ZenTick$json = {
  '1': 'ZenTick',
  '2': [
    {'1': 'tradable', '3': 1, '4': 1, '5': 11, '6': '.google.protobuf.BoolValue', '10': 'tradable'},
    {'1': 'mode', '3': 2, '4': 1, '5': 11, '6': '.google.protobuf.StringValue', '10': 'mode'},
    {'1': 'instrument_token', '3': 3, '4': 1, '5': 11, '6': '.google.protobuf.Int64Value', '10': 'instrumentToken'},
    {'1': 'last_price', '3': 4, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'lastPrice'},
    {'1': 'last_traded_quantity', '3': 5, '4': 1, '5': 11, '6': '.google.protobuf.Int32Value', '10': 'lastTradedQuantity'},
    {'1': 'average_traded_price', '3': 6, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'averageTradedPrice'},
    {'1': 'volume_traded', '3': 7, '4': 1, '5': 11, '6': '.google.protobuf.Int32Value', '10': 'volumeTraded'},
    {'1': 'total_buy_quantity', '3': 8, '4': 1, '5': 11, '6': '.google.protobuf.Int32Value', '10': 'totalBuyQuantity'},
    {'1': 'total_sell_quantity', '3': 9, '4': 1, '5': 11, '6': '.google.protobuf.Int32Value', '10': 'totalSellQuantity'},
    {'1': 'ohlc', '3': 10, '4': 1, '5': 11, '6': '.websocket.OHLC', '10': 'ohlc'},
    {'1': 'change', '3': 11, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'change'},
    {'1': 'last_trade_time', '3': 12, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'lastTradeTime'},
    {'1': 'oi', '3': 13, '4': 1, '5': 11, '6': '.google.protobuf.Int32Value', '10': 'oi'},
    {'1': 'oi_day_high', '3': 14, '4': 1, '5': 11, '6': '.google.protobuf.Int32Value', '10': 'oiDayHigh'},
    {'1': 'oi_day_low', '3': 15, '4': 1, '5': 11, '6': '.google.protobuf.Int32Value', '10': 'oiDayLow'},
    {'1': 'exchange_timestamp', '3': 16, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'exchangeTimestamp'},
    {'1': 'depth', '3': 17, '4': 1, '5': 11, '6': '.websocket.Depth', '10': 'depth'},
    {'1': 'phoenix_timestamp', '3': 18, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'phoenixTimestamp'},
    {'1': 'zen_id', '3': 19, '4': 1, '5': 11, '6': '.google.protobuf.Int64Value', '10': 'zenId'},
  ],
};

/// Descriptor for `ZenTick`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List zenTickDescriptor = $convert.base64Decode(
    'CgdaZW5UaWNrEjYKCHRyYWRhYmxlGAEgASgLMhouZ29vZ2xlLnByb3RvYnVmLkJvb2xWYWx1ZV'
    'IIdHJhZGFibGUSMAoEbW9kZRgCIAEoCzIcLmdvb2dsZS5wcm90b2J1Zi5TdHJpbmdWYWx1ZVIE'
    'bW9kZRJGChBpbnN0cnVtZW50X3Rva2VuGAMgASgLMhsuZ29vZ2xlLnByb3RvYnVmLkludDY0Vm'
    'FsdWVSD2luc3RydW1lbnRUb2tlbhI7CgpsYXN0X3ByaWNlGAQgASgLMhwuZ29vZ2xlLnByb3Rv'
    'YnVmLkRvdWJsZVZhbHVlUglsYXN0UHJpY2USTQoUbGFzdF90cmFkZWRfcXVhbnRpdHkYBSABKA'
    'syGy5nb29nbGUucHJvdG9idWYuSW50MzJWYWx1ZVISbGFzdFRyYWRlZFF1YW50aXR5Ek4KFGF2'
    'ZXJhZ2VfdHJhZGVkX3ByaWNlGAYgASgLMhwuZ29vZ2xlLnByb3RvYnVmLkRvdWJsZVZhbHVlUh'
    'JhdmVyYWdlVHJhZGVkUHJpY2USQAoNdm9sdW1lX3RyYWRlZBgHIAEoCzIbLmdvb2dsZS5wcm90'
    'b2J1Zi5JbnQzMlZhbHVlUgx2b2x1bWVUcmFkZWQSSQoSdG90YWxfYnV5X3F1YW50aXR5GAggAS'
    'gLMhsuZ29vZ2xlLnByb3RvYnVmLkludDMyVmFsdWVSEHRvdGFsQnV5UXVhbnRpdHkSSwoTdG90'
    'YWxfc2VsbF9xdWFudGl0eRgJIAEoCzIbLmdvb2dsZS5wcm90b2J1Zi5JbnQzMlZhbHVlUhF0b3'
    'RhbFNlbGxRdWFudGl0eRIjCgRvaGxjGAogASgLMg8ud2Vic29ja2V0Lk9ITENSBG9obGMSNAoG'
    'Y2hhbmdlGAsgASgLMhwuZ29vZ2xlLnByb3RvYnVmLkRvdWJsZVZhbHVlUgZjaGFuZ2USQgoPbG'
    'FzdF90cmFkZV90aW1lGAwgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcFINbGFzdFRy'
    'YWRlVGltZRIrCgJvaRgNIAEoCzIbLmdvb2dsZS5wcm90b2J1Zi5JbnQzMlZhbHVlUgJvaRI7Cg'
    'tvaV9kYXlfaGlnaBgOIAEoCzIbLmdvb2dsZS5wcm90b2J1Zi5JbnQzMlZhbHVlUglvaURheUhp'
    'Z2gSOQoKb2lfZGF5X2xvdxgPIAEoCzIbLmdvb2dsZS5wcm90b2J1Zi5JbnQzMlZhbHVlUghvaU'
    'RheUxvdxJJChJleGNoYW5nZV90aW1lc3RhbXAYECABKAsyGi5nb29nbGUucHJvdG9idWYuVGlt'
    'ZXN0YW1wUhFleGNoYW5nZVRpbWVzdGFtcBImCgVkZXB0aBgRIAEoCzIQLndlYnNvY2tldC5EZX'
    'B0aFIFZGVwdGgSRwoRcGhvZW5peF90aW1lc3RhbXAYEiABKAsyGi5nb29nbGUucHJvdG9idWYu'
    'VGltZXN0YW1wUhBwaG9lbml4VGltZXN0YW1wEjIKBnplbl9pZBgTIAEoCzIbLmdvb2dsZS5wcm'
    '90b2J1Zi5JbnQ2NFZhbHVlUgV6ZW5JZA==');

@$core.Deprecated('Use zenTicksDescriptor instead')
const ZenTicks$json = {
  '1': 'ZenTicks',
  '2': [
    {'1': 'zen_ticks', '3': 1, '4': 3, '5': 11, '6': '.websocket.ZenTick', '10': 'zenTicks'},
  ],
};

/// Descriptor for `ZenTicks`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List zenTicksDescriptor = $convert.base64Decode(
    'CghaZW5UaWNrcxIvCgl6ZW5fdGlja3MYASADKAsyEi53ZWJzb2NrZXQuWmVuVGlja1IIemVuVG'
    'lja3M=');

