import 'package:flutter/material.dart';
import 'dart:async';

class AnimatedTypingDots extends StatefulWidget {
  const AnimatedTypingDots({Key? key}) : super(key: key);

  @override
  State<AnimatedTypingDots> createState() => _AnimatedTypingDotsState();
}

class _AnimatedTypingDotsState extends State<AnimatedTypingDots> with SingleTickerProviderStateMixin {
  int _dotCount = 1;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      setState(() {
        _dotCount = _dotCount % 3 + 1;
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Text('.' * _dotCount, style: TextStyle(fontWeight: FontWeight.bold));
  }
}
