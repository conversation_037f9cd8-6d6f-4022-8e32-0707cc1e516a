import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/websocket_streaming/data/repository/tick_repository.dart';
import 'package:phoenix/features/websocket_streaming/model/tick_data_model.dart';

part 'tick_event.dart';
part 'tick_state.dart';

class TickBloc extends Bloc<TickEvent, TickState> {
  final TickRepository _repository;

  StreamSubscription? _subscription;


  TickBloc(this._repository) : super(TickInitial()) {
    on<ConnectWebSocketEvent>(_onConnectWebsocket);
    on<DisconnectWebSocketEvent>(_onDisconnectWebSocket);
    on<SendPingEvent>(_onSendPing);
    on<TickReceived>(_onTickRecived);
    on<TickErrorOccurred>(_onTickErrorOccured);
  }

  void _onConnectWebsocket(ConnectWebSocketEvent event, Emitter<TickState> emit) async {
    emit(TickLoading());
    try {
      _subscription = _repository.connect(event.url,headers: event.headers).listen(
        (data) => add(TickReceived(data)),
        onError: (error) => add(TickErrorOccurred(error.toString())),
      );
    } catch (e){
      emit(TickError("COnnection failed : $e"));
    }
  }

  void _onDisconnectWebSocket (DisconnectWebSocketEvent event, Emitter<TickState> emit){
    _subscription?.cancel();
    _repository.disconnect();
    emit(TickInitial());
  }

  void _onSendPing (SendPingEvent event, Emitter <TickState> emit){
    _repository.sendPing();
  }

  void _onTickRecived (TickReceived event, Emitter<TickState> emit){
    emit(TickLoaded(event.tickData));
  }

  void _onTickErrorOccured (TickErrorOccurred event, Emitter<TickState> emit){
    emit(TickError(event.error));
  }

  @override
  Future<void> close() {
    _subscription?.cancel();
    _repository.disconnect();
    return super.close();
  }
}
