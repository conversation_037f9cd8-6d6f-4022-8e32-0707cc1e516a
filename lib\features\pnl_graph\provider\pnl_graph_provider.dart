import 'dart:convert';

import 'package:phoenix/features/common/api_response_model.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';
import 'package:phoenix/services/request_body_filter_service.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/app_exception.dart';
import 'package:phoenix/utils/http_service.dart';

class PnlGraphProvider {
  /// New method to fetch PnL data filtered by timestamp range and other filters
  Future<List<PositionPnL>> fetchPnlDataByTimestamp({
    required int clientId,
    required String startTimestamp,
    required String endTimestamp,
    //required List<String> brokers,
    //required List<int> accountIds,
    required List<int> zenSecIds,
    required List<int> strategyId,
  }) async {
    final customHttpService = HttpService();
    try {
      final positionCompKeyFilter =
          await RequestBodyFilterService.buildPositionCompKeyFilter(clientId);

      final requestBody = jsonEncode({
        "start_timestamp": startTimestamp,
        "end_timestamp": endTimestamp,
        ...positionCompKeyFilter,
        "zen_ids": zenSecIds,
        "strategy_ids": strategyId,
      });

      final response = await customHttpService.post(
        Uri.parse(ApiPath.getPnL(clientId)),
        body: requestBody,
        headers: {'Content-Type': 'application/json'},
      );

      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if (apiResponse.code == 200 &&
          apiResponse.status == 'SUCCESS' &&
          response.statusCode == 200) {
        final List<PositionPnL> pnlData = (apiResponse.payload as List<dynamic>)
            .map((data) => PositionPnL.fromJson(data))
            .toList();

        return pnlData;
      } else {
        throw AppException(apiResponse.message);
      }
    } catch (e) {
      throw e is AppException
          ? e
          : AppException(
              'Error fetching timestamp-filtered PnL data: ${e.toString()}');
    }
  }
}
