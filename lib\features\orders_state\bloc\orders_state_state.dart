part of 'orders_state_bloc.dart';

@immutable
sealed class OrdersStateState {}

final class OrdersStateInitial extends OrdersStateState {}

class OrdersStateLoading extends OrdersStateState{}

class OrdersStateLoaded extends OrdersStateState {
  //final List<SentinelOrderState> data;
  final List<UnifiedOrderData> data;
  OrdersStateLoaded( this.data );
}

class OrdersStateError extends OrdersStateState {
  final String error;
  OrdersStateError(this.error);
}
