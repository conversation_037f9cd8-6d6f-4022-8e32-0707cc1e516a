import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/orders/model/position_order_data.dart';
import 'package:phoenix/features/pnl/bloc/pnl_bloc.dart';
import 'package:phoenix/features/portfolio_data/bloc/portfolio_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';

import 'package:phoenix/screens/orders/order_form_bottom_sheet.dart';
import 'package:phoenix/screens/orders/order_type.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/widgets/broker_avatar/broker_avatar.dart';
import 'package:phoenix/widgets/custom/dismissible/zen_dismissible.dart';

import 'package:phoenix/utils/util_functions.dart';

class PositionsChild extends StatefulWidget {
  const PositionsChild(
      {super.key,
      required this.data,
      this.prices,
      required this.brokerAccountStrategyMapData});

  final PositionsModel data;
  final double? prices;
  final BrokerAccountStrategyData? brokerAccountStrategyMapData;

  @override
  State<PositionsChild> createState() => _PositionsChildState();
}

class _PositionsChildState extends State<PositionsChild> {
  double? traker;
  late final AnimationController _formSheetAnimeController;
  late bool isRight = true;

  @override
  void initState() {
    super.initState();
    _formSheetAnimeController =
        BottomSheet.createAnimationController(Navigator.of(context));
    _formSheetAnimeController.duration = const Duration(milliseconds: 850);
  }

  @override
  Widget build(BuildContext context) {
    double daysChange = ((widget.data.latestPrice - widget.data.sodPrice) /
            widget.data.sodPrice) *
        100;

    double ltp = widget.data.latestPrice;
    double unrealized = widget.data.unrealizedPnl;
    double unrealizedPercentage = widget.data.unrealizedPnlPercentageChange;

    //calculations for live prices
    if (widget.prices != null && widget.prices! > 0) {
      ltp = widget.prices as double;
      unrealized = (ltp * widget.data.position) - widget.data.openCost;
      unrealizedPercentage = (unrealized / widget.data.openCost) * 100;
      daysChange = ((ltp - widget.data.sodPrice) / widget.data.sodPrice) * 100;
    }

    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return ZenDismissible(
          key: Key(
            widget.data.positionCompositeKey.zenSecId.toString(),
          ),
          background: slideSellBackground(),
          secondaryBackground: slideBuyBackground(),
          dismissThresholds: const {
            ZenDismissDirection.endToStart: 0.20,
            ZenDismissDirection.startToEnd: 0.20,
          },
          direction: DismissDirection.horizontal,
          movementDuration: const Duration(milliseconds: 200),
          confirmDismiss: swipeHandler,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: AppTheme.surfaceColor(themeState.isDarkMode),
              boxShadow: ThemeConstants.getNeomorpicShadow(themeState.isDarkMode)
            ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Left Column
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      widget.data.tradingSymbol,
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 15,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(width: 4),
                    if (widget.data.positionCompositeKey.broker.isNotEmpty)
                      BrokerAvatar(
                          brokerName:
                              widget.brokerAccountStrategyMapData?.brokerName ??
                                  "N/A"),
                  ],
                ),
                // // Strategy
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const ImageIcon(
                      AssetImage("images/tile-generic/strategy.png"),
                      color: Colors.blue,
                      size: 15,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      widget.brokerAccountStrategyMapData?.strategyName ??
                          "N/A",
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 19),

                // Invested Value
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.currency_exchange_rounded,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                      size: 12,
                    ),
                    Icon(
                      Icons.currency_rupee,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                      size: 12,
                    ),
                    Text(
                      UtilFunctions.formatIndianCurrency(
                        widget.data.openCost,
                      ),
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),

                // LTP and Change Percentage
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'LTP ',
                        style: TextStyle(
                          color: AppTheme.textPrimary(themeState.isDarkMode),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      TextSpan(
                        text: UtilFunctions.formatIndianCurrency(ltp),
                        style: TextStyle(
                          color: AppTheme.textSecondary(themeState.isDarkMode),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      daysPnlFormatter(daysChange),
                    ],
                  ),
                ),
              ],
            ),

            // Right Column
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Text(
                      'Qty. ',
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      widget.data.position.toString(),
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const ImageIcon(
                      AssetImage("images/tile-generic/qty_icon.png"),
                      color: Colors.blue,
                      size: 12,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                PnlFormatter(
                    value: widget.data.isOpen
                        ? unrealized
                        : widget.data.realizedPnl),
                PercentageValidator(
                  unrealizedPercentage: unrealizedPercentage,
                ),
                const SizedBox(height: 4),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Avg. ',
                        style: TextStyle(
                          color: AppTheme.textPrimary(themeState.isDarkMode),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      TextSpan(
                        text: UtilFunctions.formatIndianCurrency(
                            widget.data.averageCostPerShare),
                        style: TextStyle(
                          color: AppTheme.textSecondary(themeState.isDarkMode),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
        );
      },
    );
  }

  //this opens the form
  //after interaction closes it and resubscribes to the data
  Future<bool?> swipeHandler(DismissDirection direction) async {
    final action = direction == DismissDirection.startToEnd ? 'SELL' : 'BUY';

    debugPrint("Swipe handler triggered for action: $action");

    // Store references before async gap
    final portfolioBloc = context.read<PortfolioBloc>();
    final webSocketBloc = context.read<WebSocketBloc>();
    final pnlBloc = context.read<PnlBloc>();

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      transitionAnimationController: _formSheetAnimeController,
      builder: (modalContext) {
        return SingleChildScrollView(
          reverse: true,
          child: OrderFormSheet(
            openOrderType: FormOpenOrderType.fromPosition,
            positionMetaData: PositionOrderData(
              positionData: widget.data,
              zenId: widget.data.positionCompositeKey.zenSecId,
              tradingSymbol: widget.data.tradingSymbol,
              transactionType: action,
            ),
          ),
        );
      },
    ).whenComplete(() {
      debugPrint(
        "📌 $action form from positions screen closed subsribing to the data list",
      );
      
      // Clearing signel stock streaming
      BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Use stored bloc references instead of context.read
      final portfolioState = portfolioBloc.state;

      final Set<int> zenSecIds = {};

      ///
      /// reading zenIds from pnl and positions bloc and adding to websocket
      ///

      if (portfolioState is PortfolioLoaded) {
        zenSecIds.addAll(
          portfolioState.openPositions
              .map((holding) => holding.positionCompositeKey.zenSecId),
        );

        zenSecIds.addAll(
          portfolioState.closedPositions
              .map((holding) => holding.positionCompositeKey.zenSecId),
        );

        debugPrint(
          "🔄 Updating WebSocket subscriptions after $action - ${zenSecIds.length} unique stocks from Portfolio",
        );
      }

      final pnlState = pnlBloc.state;
      if (pnlState is PnlLoaded) {
        zenSecIds.addAll(
          pnlState.pnlData
              .map((pnlData) => pnlData.positionCompositeKey.zenSecId),
        );

        debugPrint(
          "🔄 Adding ${pnlState.pnlData.length} stocks from PnL to WebSocket subscription",
        );
      }
      webSocketBloc.add(
        WebSocketSelectMultipleStocks(zenSecIds.toList()),
      );
    });

    // Return false to prevent actual dismissal
    return false;
  }

  TextSpan daysPnlFormatter(double daysChange) {
    Color textColor;
    String displayText;
    if (daysChange.isInfinite || daysChange.isNaN) {
      displayText = " ";
      textColor = Colors.grey;
    } else if (daysChange == 0) {
      displayText = " (0.00%)";
      textColor = Colors.grey;
    } else if (daysChange > 0) {
      displayText = " (+${daysChange.toStringAsFixed(2)}%)";
      textColor = const Color(0xFF00FF29);
    } else {
      displayText = " (${daysChange.toStringAsFixed(2)}%)";
      textColor = ThemeConstants.titleRedColor;
    }

    return TextSpan(
      text: displayText,
      style: TextStyle(
        color: textColor,
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  Widget slideSellBackground() {
    return Container(
      decoration: BoxDecoration(
        color: ThemeConstants.sellSlideOptionColor,
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(20),
          right: Radius.circular(20),
        ),
        boxShadow: [
          // Light inner glow near edges
          BoxShadow(
            //color: Colors.lightGreenAccent.withOpacity(0.9),
            color: Colors.red.shade200,
            offset: Offset(-10, -10),
            blurRadius: 20,
            //spreadRadius: 20,
            inset: true,
          ),
          // Dark inner shadow in center-ish
          BoxShadow(
            color: Colors.black,
            offset: Offset(10, 10),
            blurRadius: 20,
            inset: true,
          ),
        ],
      ),
      child: Align(
        alignment: Alignment.centerRight,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            SizedBox(
              width: 20,
            ),
            Text(
              " Sell",
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w800,
                fontSize: 15,
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ),
    );
  }

  Widget slideBuyBackground() {
    return Container(
      decoration: BoxDecoration(
        color: ThemeConstants.buySlideOptionColor,
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(20),
          right: Radius.circular(20),
        ),
        boxShadow: [
          // Light inner glow near edges

          BoxShadow(
            //color: Colors.lightGreenAccent.withOpacity(0.9),
            color: Colors.lightGreenAccent.withOpacity(0.9),

            offset: Offset(-10, -10),
            blurRadius: 20,
            //spreadRadius: 20,
            inset: true,
          ),
          // Dark inner shadow in center-ish
          BoxShadow(
            color: Colors.black,
            offset: Offset(10, 10),
            blurRadius: 20,
            inset: true,
          ),
        ],
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            Text(
              " Buy",
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w800,
                fontSize: 15,
              ),
              textAlign: TextAlign.right,
            ),
            SizedBox(
              width: 20,
            ),
          ],
        ),
      ),
    );
  }
}

class PnlFormatter extends StatelessWidget {
  const PnlFormatter({
    super.key,
    required this.value,
  });

  final double value;

  @override
  Widget build(BuildContext context) {
    Color textColor;
    String displayText;

    if (value.isInfinite || value.isNaN) {
      displayText = "--";
      textColor = Colors.grey;
    } else if (value == 0) {
      displayText = "0.00";
      textColor = Colors.grey;
    } else if (value > 0) {
      displayText = "+${UtilFunctions.formatIndianCurrency(value)}";
      textColor = ThemeConstants.tileGreenColor;
    } else {
      displayText = UtilFunctions.formatIndianCurrency(value);
      textColor = ThemeConstants.titleRedColor;
    }

    return Text(
      displayText,
      style: TextStyle(
        color: textColor,
        fontSize: 15,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}

class PercentageValidator extends StatelessWidget {
  const PercentageValidator({
    super.key,
    required this.unrealizedPercentage,
  });

  final double unrealizedPercentage;

  @override
  Widget build(BuildContext context) {
    Color textColor;
    String displayText;

    if (unrealizedPercentage.isInfinite || unrealizedPercentage.isNaN) {
      displayText = "--";
      textColor = Colors.grey;
    } else if (unrealizedPercentage == 0) {
      displayText = "0.00%";
      textColor = Colors.grey;
    } else if (unrealizedPercentage > 0) {
      displayText = "+${unrealizedPercentage.toStringAsFixed(2)}%";
      textColor = ThemeConstants.tileGreenColor;
    } else {
      displayText = "${unrealizedPercentage.toStringAsFixed(2)}%";
      textColor = ThemeConstants.titleRedColor;
    }

    return Text(
      displayText,
      style: TextStyle(
        color: textColor,
        fontSize: 13,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}
