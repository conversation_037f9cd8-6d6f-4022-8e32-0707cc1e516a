import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';
import 'package:phoenix/features/margin_state/bloc/margin_state_bloc.dart';
import 'package:phoenix/features/margin_state/model/margin_data.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/screens/account/widgets/client_selection_widget.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/circular_loader.dart';

class FundProfilePage extends StatefulWidget {
  const FundProfilePage({super.key});

  @override
  State<FundProfilePage> createState() => _FundProfilePageState();
}

class _FundProfilePageState extends State<FundProfilePage> {
  bool _isAccountsExpanded = false;
  bool _isStrategiesExpanded = false;

  @override
  void initState() {
    super.initState();
    // Fetch margin data when the page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchMarginData();
    });
  }

  void _fetchMarginData() {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      final user = authState.credentialsModel;
      List<int> accountIds = [];

      // Collect all account IDs from all brokers
      for (var broker in user.brokers) {
        for (var account in broker.accounts) {
          accountIds.add(account.accountId);
        }
      }

      if (accountIds.isNotEmpty) {
        context.read<MarginBloc>().add(
          MarginFetchMultipleEvent(user.clientId, accountIds),
        );
      }
    }
  }

  void _fetchMarginDataForClient(int clientId) {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      final user = authState.credentialsModel;

      // If the clientId matches the current user's clientId, use their account IDs
      if (clientId == user.clientId) {
        List<int> accountIds = [];

        // Collect all account IDs from all brokers for the current user
        for (var broker in user.brokers) {
          for (var account in broker.accounts) {
            accountIds.add(account.accountId);
          }
        }

        if (accountIds.isNotEmpty) {
          context.read<MarginBloc>().add(
            MarginFetchMultipleEvent(clientId, accountIds),
          );
        }
      } else {
        // For different clients, we need to trigger a client change first
        // which will update the credentials with the new client's broker/account info
        context.read<AuthBloc>().add(
          ClientChangeEvent(clientId: clientId),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthUnauthenticated) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            '/',
            (Route<dynamic> route) => false,
          ); // remove all previous routes);
        } else if (state is AuthAuthenticated) {
          // Refresh margin data when client changes - use the regular fetch method
          // since the credentials are now updated with the new client's data
          _fetchMarginData();
        }
      },
      builder: (context, state) {
        if (state is! AuthAuthenticated) {
          return BlocBuilder<ThemeBloc, ThemeState>(
            builder: (context, themeState) {
              return Scaffold(
                backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
                body: const Center(child: CircularLoader()),
              );
            },
          );
        }
        final user = state.credentialsModel;

        return BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, themeState) {
            return Scaffold(
              backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
              appBar: AppBar(
                backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
                elevation: 0,
                title: Row(
                  children: [
                    Text(
                      "Fund Profile",
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                iconTheme: IconThemeData(color: AppTheme.textPrimary(themeState.isDarkMode)),
                actions: [
                  BlocBuilder<MarginBloc, MarginState>(
                    builder: (context, marginState) {
                      return IconButton(
                        onPressed: marginState is MarginLoading ? null : _fetchMarginData,
                        icon: marginState is MarginLoading
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppTheme.textSecondary(themeState.isDarkMode),
                                  ),
                                ),
                              )
                            : Icon(
                                Icons.refresh,
                                color: AppTheme.textPrimary(themeState.isDarkMode),
                              ),
                        tooltip: 'Refresh margin data',
                      );
                    },
                  ),
                  const SizedBox(width: 8),
                ],
              ),
          body: SafeArea(
            child: RefreshIndicator(
              onRefresh: () async {
                _fetchMarginData();
                // Add a small delay to show the refresh indicator
                await Future.delayed(const Duration(milliseconds: 500));
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                  // Profile Header Card with Margin Summary
                  BlocBuilder<MarginBloc, MarginState>(
                    builder: (context, marginState) {
                      return _buildProfileCard(user, themeState, marginState);
                    },
                  ),
                  const SizedBox(height: 16),

                  // Overview Cards Row
                  _buildOverviewCards(user, themeState),
                  const SizedBox(height: 16),

                  // Accounts Section with Margin Data
                  BlocBuilder<MarginBloc, MarginState>(
                    builder: (context, marginState) {
                      return _buildAccountsSection(user, themeState, marginState);
                    },
                  ),
                  const SizedBox(height: 16),

                  // Strategies Section
                  _buildStrategiesSection(user, themeState),
                ],
              ),
            ),
          ),),);
          },
        );
      
  },);}

  Widget _buildProfileCard(CredentialsModel user, ThemeState themeState, MarginState marginState) {
    // Calculate total available cash and account count
    double totalCash = 0.0;
    int totalAccounts = 0;

    if (marginState is MarginMultipleLoaded) {
      for (var marginData in marginState.margins.values) {
        totalCash += marginData.availableCash;
      }
      totalAccounts = marginState.margins.length;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: themeState.isDarkMode
              ? [Colors.grey[850]!, Colors.grey[800]!]
              : [Colors.white, Colors.grey[50]!],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode
                ? Colors.black.withOpacity(0.3)
                : Colors.grey.withOpacity(0.12),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with user info and total cash
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 1.5,
                  ),
                ),
                child: CircleAvatar(
                  radius: 28,
                  backgroundImage: NetworkImage(user.pictureUrl),
                  backgroundColor: Colors.grey[300],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back,',
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      user.name.split("@").first.toCapitalized,
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 20,
                        overflow: TextOverflow.ellipsis,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      user.email,
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Client Selection integrated into profile card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: themeState.isDarkMode
                  ? Colors.grey[800]?.withOpacity(0.6)
                  : Colors.grey[50]?.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: themeState.isDarkMode
                    ? Colors.grey[700]!.withOpacity(0.5)
                    : Colors.grey[200]!.withOpacity(0.5),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person_outline,
                      color: Colors.blue,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Client Selection',
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                ClientSelectionWidget(
                  userClientId: user.clientId,
                  onClientChanged: _fetchMarginDataForClient,
                  isCompact: true,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Total Portfolio Value Section
          if (marginState is MarginMultipleLoaded && totalCash > 0)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.green.withOpacity(0.15),
                    Colors.green.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.account_balance_wallet,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Total Portfolio Value',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '₹${_formatCurrency(totalCash)}',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$totalAccounts accounts',
                    style: TextStyle(
                      color: Colors.green.shade600,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          else if (marginState is MarginLoading)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: themeState.isDarkMode ? Colors.grey[800] : Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.textSecondary(themeState.isDarkMode),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Loading portfolio data...',
                    style: TextStyle(
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }



  Widget _buildOverviewCards(CredentialsModel user, ThemeState themeState) {
    List<AccountInfo> allAccounts = [];
    for (var broker in user.brokers) {
      allAccounts.addAll(broker.accounts);
    }

    List<Strategy> allStrategies = [];
    for (var broker in user.brokers) {
      for (var account in broker.accounts) {
        allStrategies.addAll(account.strategies);
      }
    }

    return Row(
      children: [
        Expanded(
          child: _buildOverviewCard(
            'Brokers',
            user.brokers.length.toString(),
            Icons.account_balance,
            Colors.blue,
            themeState,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildOverviewCard(
            'Accounts',
            allAccounts.length.toString(),
            Icons.account_circle,
            Colors.orange,
            themeState,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildOverviewCard(
            'Strategies',
            allStrategies.length.toString(),
            Icons.trending_up,
            Colors.teal,
            themeState,
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewCard(String title, String count, IconData icon, Color color, ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeState.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: themeState.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode
                ? Colors.black.withOpacity(0.1)
                : Colors.grey.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            count,
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }







  Widget _buildAccountsSection(CredentialsModel user, ThemeState themeState, MarginState marginState) {
    List<AccountInfo> allAccounts = [];
    for (var broker in user.brokers) {
      allAccounts.addAll(broker.accounts);
    }

    return Container(
      decoration: BoxDecoration(
        color: themeState.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: themeState.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode
                ? Colors.black.withOpacity(0.1)
                : Colors.grey.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Accordion Header
          InkWell(
            onTap: () {
              setState(() {
                _isAccountsExpanded = !_isAccountsExpanded;
              });
            },
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: themeState.isDarkMode
                    ? Colors.grey[800]?.withOpacity(0.5)
                    : Colors.grey[50]?.withOpacity(0.8),
                borderRadius: _isAccountsExpanded
                    ? const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      )
                    : BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.account_circle,
                      color: Colors.orange,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Trading Accounts',
                          style: TextStyle(
                            color: AppTheme.textPrimary(themeState.isDarkMode),
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          '${allAccounts.length} accounts across ${user.brokers.length} brokers',
                          style: TextStyle(
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${allAccounts.length}',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  AnimatedRotation(
                    turns: _isAccountsExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Accordion Content
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: _isAccountsExpanded ? null : 0,
            child: _isAccountsExpanded
                ? Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        ...user.brokers.map((broker) =>
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (broker.accounts.isNotEmpty) ...[
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  margin: const EdgeInsets.only(bottom: 8),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.05),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.blue.withOpacity(0.2)),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.account_balance,
                                        color: Colors.blue,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        broker.brokerName,
                                        style: TextStyle(
                                          color: Colors.blue.shade700,
                                          fontSize: 13,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                ...broker.accounts.map((account) => _buildAccountCard(account, themeState, marginState)),
                                const SizedBox(height: 12),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountCard(AccountInfo account, ThemeState themeState, MarginState marginState) {
    // Get margin data for this account
    MarginData? marginData;
    if (marginState is MarginMultipleLoaded) {
      marginData = marginState.margins[account.accountId];
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: themeState.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: themeState.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode
                ? Colors.black.withOpacity(0.15)
                : Colors.grey.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Account Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: themeState.isDarkMode
                  ? Colors.grey[800]?.withOpacity(0.5)
                  : Colors.grey[50]?.withOpacity(0.8),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.orange.withOpacity(0.2),
                        Colors.orange.withOpacity(0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.account_circle,
                    color: Colors.orange,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        account.accountName,
                        style: TextStyle(
                          color: AppTheme.textPrimary(themeState.isDarkMode),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                // Status indicator
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: marginData != null
                        ? Colors.green.withOpacity(0.1)
                        : Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: marginData != null
                          ? Colors.green.withOpacity(0.3)
                          : Colors.orange.withOpacity(0.3),
                    ),
                  ),
                  child: Text(
                    marginData != null ? 'Active' : 'Loading',
                    style: TextStyle(
                      color: marginData != null ? Colors.green : Colors.orange,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Margin Data Section
          if (marginData != null) ...[
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Available Cash - Main metric
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.green.withOpacity(0.1),
                          Colors.green.withOpacity(0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.green.withOpacity(0.2)),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.account_balance_wallet,
                              color: Colors.green,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Available Cash',
                              style: TextStyle(
                                color: Colors.green.shade700,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '₹${_formatCurrency(marginData.availableCash)}',
                          style: TextStyle(
                            color: Colors.green,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Account Details Row
                  Row(
                    children: [
                      Expanded(
                        child: _buildMarginInfoCard(
                          'Broker',
                          marginData.brokerName.isNotEmpty
                              ? marginData.brokerName
                              : 'N/A',
                          Icons.account_balance,
                          Colors.blue,
                          themeState,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildMarginInfoCard(
                          'Strategies',
                          '${account.strategies.length}',
                          Icons.trending_up,
                          Colors.purple,
                          themeState,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ] else if (marginState is MarginLoading) ...[
            Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.textSecondary(themeState.isDarkMode),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Loading margin data...',
                    style: TextStyle(
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ] else if (marginState is MarginError) ...[
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  GestureDetector(
                    onTap: _fetchMarginData,
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.withOpacity(0.3)),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.refresh,
                            color: Colors.red,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Retry Loading',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMarginInfoCard(String title, String value, IconData icon, Color color, ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeState.isDarkMode
            ? Colors.grey[800]?.withOpacity(0.5)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: themeState.isDarkMode
              ? Colors.grey[700]!.withOpacity(0.5)
              : Colors.grey[200]!.withOpacity(0.5),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 16,
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStrategiesSection(CredentialsModel user, ThemeState themeState) {
    List<Strategy> allStrategies = [];
    for (var broker in user.brokers) {
      for (var account in broker.accounts) {
        allStrategies.addAll(account.strategies);
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: themeState.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: themeState.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode
                ? Colors.black.withOpacity(0.1)
                : Colors.grey.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Accordion Header
          InkWell(
            onTap: () {
              setState(() {
                _isStrategiesExpanded = !_isStrategiesExpanded;
              });
            },
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: themeState.isDarkMode
                    ? Colors.grey[800]?.withOpacity(0.5)
                    : Colors.grey[50]?.withOpacity(0.8),
                borderRadius: _isStrategiesExpanded
                    ? const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      )
                    : BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.teal.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.trending_up,
                      color: Colors.teal,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Active Strategies',
                          style: TextStyle(
                            color: AppTheme.textPrimary(themeState.isDarkMode),
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          allStrategies.isEmpty
                              ? 'No strategies available'
                              : '${allStrategies.length} strategies across accounts',
                          style: TextStyle(
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.teal.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${allStrategies.length}',
                      style: TextStyle(
                        color: Colors.teal,
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  AnimatedRotation(
                    turns: _isStrategiesExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Accordion Content
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: _isStrategiesExpanded ? null : 0,
            child: _isStrategiesExpanded
                ? Container(
                    padding: const EdgeInsets.all(16),
                    child: allStrategies.isEmpty
                        ? Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: themeState.isDarkMode ? Colors.grey[800] : Colors.grey[50],
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: themeState.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
                              ),
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: AppTheme.textSecondary(themeState.isDarkMode),
                                  size: 24,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'No strategies available',
                                  style: TextStyle(
                                    color: AppTheme.textSecondary(themeState.isDarkMode),
                                    fontSize: 13,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Column(
                            children: [
                              ...user.brokers.map((broker) =>
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ...broker.accounts.map((account) =>
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          if (account.strategies.isNotEmpty) ...[
                                            Container(
                                              width: double.infinity,
                                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                              margin: const EdgeInsets.only(bottom: 8),
                                              decoration: BoxDecoration(
                                                color: Colors.teal.withOpacity(0.05),
                                                borderRadius: BorderRadius.circular(6),
                                                border: Border.all(color: Colors.teal.withOpacity(0.2)),
                                              ),
                                              child: Text(
                                                '${broker.brokerName} - ${account.accountName}',
                                                style: TextStyle(
                                                  color: Colors.teal.shade700,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ),
                                            ...account.strategies.map((strategy) => _buildStrategyCard(strategy, themeState)),
                                            const SizedBox(height: 12),
                                          ],
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildStrategyCard(Strategy strategy, ThemeState themeState) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeState.isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: themeState.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode 
                ? Colors.black.withOpacity(0.1) 
                : Colors.grey.withOpacity(0.03),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.teal.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.trending_up,
              color: Colors.teal,
              size: 16,
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  strategy.strategyName,
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          // Icon(
          //   Icons.arrow_forward_ios,
          //   color: AppTheme.textSecondary(themeState.isDarkMode),
          //   size: 14,
          // ),
        ],
      ),
    );
  }

  String _formatCurrency(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(2)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(2)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(2)}K';
    } else {
      return amount.toStringAsFixed(2);
    }
  }
}
