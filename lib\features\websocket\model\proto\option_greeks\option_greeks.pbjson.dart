//
//  Generated code. Do not modify.
//  source: option_greeks.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use zenOptionGreeksDescriptor instead')
const ZenOptionGreeks$json = {
  '1': 'ZenOptionGreeks',
  '2': [
    {'1': 'zen_id', '3': 1, '4': 1, '5': 11, '6': '.google.protobuf.Int64Value', '10': 'zenId'},
    {'1': 'underlying_zen_id', '3': 2, '4': 1, '5': 11, '6': '.google.protobuf.Int64Value', '10': 'underlyingZenId'},
    {'1': 'implied_volatility', '3': 3, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'impliedVolatility'},
    {'1': 'delta', '3': 4, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'delta'},
    {'1': 'delta2', '3': 5, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'delta2'},
    {'1': 'theta', '3': 6, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'theta'},
    {'1': 'gamma', '3': 7, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'gamma'},
    {'1': 'vega', '3': 8, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'vega'},
    {'1': 'rho', '3': 9, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'rho'},
    {'1': 'color', '3': 10, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'color'},
    {'1': 'charm', '3': 11, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'charm'},
    {'1': 'moneyness', '3': 12, '4': 1, '5': 11, '6': '.google.protobuf.StringValue', '10': 'moneyness'},
    {'1': 'trading_symbol', '3': 13, '4': 1, '5': 11, '6': '.google.protobuf.StringValue', '10': 'tradingSymbol'},
    {'1': 'strike', '3': 14, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'strike'},
    {'1': 'expiry', '3': 15, '4': 1, '5': 11, '6': '.google.type.Date', '10': 'expiry'},
    {'1': 'last_price', '3': 16, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'lastPrice'},
  ],
};

/// Descriptor for `ZenOptionGreeks`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List zenOptionGreeksDescriptor = $convert.base64Decode(
    'Cg9aZW5PcHRpb25HcmVla3MSMgoGemVuX2lkGAEgASgLMhsuZ29vZ2xlLnByb3RvYnVmLkludD'
    'Y0VmFsdWVSBXplbklkEkcKEXVuZGVybHlpbmdfemVuX2lkGAIgASgLMhsuZ29vZ2xlLnByb3Rv'
    'YnVmLkludDY0VmFsdWVSD3VuZGVybHlpbmdaZW5JZBJLChJpbXBsaWVkX3ZvbGF0aWxpdHkYAy'
    'ABKAsyHC5nb29nbGUucHJvdG9idWYuRG91YmxlVmFsdWVSEWltcGxpZWRWb2xhdGlsaXR5EjIK'
    'BWRlbHRhGAQgASgLMhwuZ29vZ2xlLnByb3RvYnVmLkRvdWJsZVZhbHVlUgVkZWx0YRI0CgZkZW'
    'x0YTIYBSABKAsyHC5nb29nbGUucHJvdG9idWYuRG91YmxlVmFsdWVSBmRlbHRhMhIyCgV0aGV0'
    'YRgGIAEoCzIcLmdvb2dsZS5wcm90b2J1Zi5Eb3VibGVWYWx1ZVIFdGhldGESMgoFZ2FtbWEYBy'
    'ABKAsyHC5nb29nbGUucHJvdG9idWYuRG91YmxlVmFsdWVSBWdhbW1hEjAKBHZlZ2EYCCABKAsy'
    'HC5nb29nbGUucHJvdG9idWYuRG91YmxlVmFsdWVSBHZlZ2ESLgoDcmhvGAkgASgLMhwuZ29vZ2'
    'xlLnByb3RvYnVmLkRvdWJsZVZhbHVlUgNyaG8SMAoFY29sb3IYCiABKAsyHC5nb29nbGUucHJv'
    'dG9idWYuRG91YmxlVmFsdWVSBWNvbG9yEjAKBWNoYXJtGAsgASgLMhwuZ29vZ2xlLnByb3RvYn'
    'VmLkRvdWJsZVZhbHVlUgVjaGFybRI6Cgltb25leW5lc3MYDCABKAsyHC5nb29nbGUucHJvdG9i'
    'dWYuU3RyaW5nVmFsdWVSCW1vbmV5bmVzcxJDCg50cmFkaW5nX3N5bWJvbBgNIAEoCzIcLmdvb2'
    'dsZS5wcm90b2J1Zi5TdHJpbmdWYWx1ZVINdHJhZGluZ1N5bWJvbBIyCgZzdHJpa2UYDSABKASY'
    'HC5nb29nbGUucHJvdG9idWYuRG91YmxlVmFsdWVSBnN0cmlrZRIpCgZleHBpcnkYDyABKAsyES'
    '5nb29nbGUudHlwZS5EYXRlUgZleHBpcnkSOwoKbGFzdF9wcmljZRgQIAEoCzIcLmdvb2dsZS5w'
    'cm90b2J1Zi5Eb3VibGVWYWx1ZVIJbGFzdFByaWNl');

@$core.Deprecated('Use zenOptionGreeksListDescriptor instead')
const ZenOptionGreeksList$json = {
  '1': 'ZenOptionGreeksList',
  '2': [
    {'1': 'zen_option_greeks', '3': 1, '4': 3, '5': 11, '6': '.com.zentropy.phoenix.common.dataset.derivatives.ZenOptionGreeks', '10': 'zenOptionGreeks'},
  ],
};

/// Descriptor for `ZenOptionGreeksList`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List zenOptionGreeksListDescriptor = $convert.base64Decode(
    'ChNaZW5PcHRpb25HcmVla3NMaXN0EmgKEXplbl9vcHRpb25fZ3JlZWtzGAEgAygLMkAuY29tLn'
    'plbnRyb3B5LnBob2VuaXguY29tbW9uLmRhdGFzZXQuZGVyaXZhdGl2ZXMuWmVuT3B0aW9uR3Jl'
    'ZWtzUg96ZW5PcHRpb25HcmVla3M=');

@$core.Deprecated('Use optionGreeksEmptyRequestDescriptor instead')
const OptionGreeksEmptyRequest$json = {
  '1': 'OptionGreeksEmptyRequest',
};

/// Descriptor for `OptionGreeksEmptyRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List optionGreeksEmptyRequestDescriptor = $convert.base64Decode(
    'ChhPcHRpb25HcmVla3NFbXB0eVJlcXVlc3Q=');