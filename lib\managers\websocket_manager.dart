import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/features/websocket/model/proto/price_proto/price.pb.dart';

import '../services/shared_prefrences_service.dart';

class WebSocketManager {
  final WebSocketBloc _webSocketBloc;
  StreamSubscription? _subscription;
  bool _isConnected = false;
  bool _manuallyDisconnected = true;
  void Function(dynamic)? onDataReceived;

  WebSocketManager({
    required WebSocketBloc webSocketBloc,
    required this.onDataReceived,
  }) : _webSocketBloc = webSocketBloc {
    _setupWebSocketListener();
  }

  bool get isConnected => _isConnected;

  void _setupWebSocketListener() {
    _subscription?.cancel(); // Cancel existing subscription if any
    _subscription = _webSocketBloc.stream.listen(
      (state) {
        if (state is WebSocketDataReceived) {
          _handleWebSocketData(state.tickList);
          _isConnected = true;
        } else if (state is WebSocketError) {
          debugPrint('WebSocket error: ${state.error}');
          _isConnected = false;
        } else if (state is WebSocketInitial) {
          _isConnected = false;
        }
      },
      cancelOnError: false, // Don't cancel on error to allow reconnection
    );
  }

  void _handleWebSocketData(List<Price>? prices) {
    if (prices == null) return;

    for (var price in prices) {
      final priceMap = {
        'zen_id': int.parse(price.zenId.value.toString()), // Convert to int
        'price': price.price.value,
      };

      // debugPrint('WebSocketManager processing price: $priceMap');
      onDataReceived?.call(priceMap);
    }
  }

  void connect(String accessToken) {
    if (_isConnected || _manuallyDisconnected) return;

    debugPrint('Connecting to WebSocket via bloc');
    _webSocketBloc.add(WebSocketConnect(accessToken));
    _manuallyDisconnected = false;
  }

  void disconnect() {
    debugPrint('Manually disconnecting WebSocket');
    _manuallyDisconnected = true;
    _isConnected = false;
    // _webSocketBloc.add(WebSocketDisconnect());
  }

  void toggle() {
    if (_isConnected) {
      disconnect();
    } else {
      _manuallyDisconnected = false;
      _webSocketBloc.add(WebSocketConnect(
        SharedPreferencesService.instance.accessToken ?? '',
      ));
    }
  }

  void updateCallback(void Function(dynamic) newCallback) {
    onDataReceived = newCallback;
  }

  void dispose() {
    debugPrint('Disposing WebSocketManager');
    disconnect(); // Ensure connection is closed
    _subscription?.cancel();
    _subscription = null;
    onDataReceived = null;
  }
}
