plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.zentropytech.phoenix"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.zentropytech.phoenix"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        manifestPlaceholders += [auth0Domain: "phoenix-lab.us.auth0.com", auth0Scheme: "phoenixscheme", appAuthRedirectScheme:"com.zentropytech.phoenix" ] // 👈 New code
        minSdkVersion 30  // or whatever version you're targeting, but needs to be at least 33 for this feature
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation "androidx.fragment:fragment:1.5.7"
    implementation "androidx.fragment:fragment-ktx:1.5.7"
    implementation 'com.google.errorprone:error_prone_annotations:2.23.0'
    implementation 'com.google.api-client:google-api-client:2.2.0' // For com.google.api.client.http
    implementation 'com.google.errorprone:error_prone_annotations:2.23.0' // For com.google.errorprone.annotations
    implementation 'org.joda:joda-convert:2.2.3' // For org.joda.time.Instant
    implementation 'com.google.crypto.tink:tink-android:1.10.0' // Ensure Tink is included
}

flutter {
    source = "../.."
}
