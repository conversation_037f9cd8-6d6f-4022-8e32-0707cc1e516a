part of 'tick_bloc.dart';

@immutable
sealed class TickEvent {}

class ConnectWebSocketEvent extends TickEvent {
  final String url;
  final Map<String,String>? headers;
  ConnectWebSocketEvent(this.url, this.headers);
}

class DisconnectWebSocketEvent extends TickEvent{}

class SendPingEvent extends TickEvent {}

class TickReceived extends TickEvent {
  final TickDataModel tickData;

  TickReceived(this.tickData);
}

class TickErrorOccurred extends TickEvent {
  final String error;

  TickErrorOccurred(this.error);
}

