import 'package:flutter/material.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/watchlist/model/watchlist_model.dart';
import 'package:phoenix/widgets/empty_state/empty_container.dart';
import 'package:phoenix/utils/theme_constants.dart';

import '../../../features/common/position_comp_key.dart';
import 'watchlist_security_item.dart';

class WatchlistTabContent extends StatelessWidget {
  final Watchlist watchlist;
  final List<SecurityModel> filteredSecurities;
  final Map<int, double> stockPrices;
  final Map<int, double> sodPrices;
  final List<PositionsModel> positions;
  final bool showSearch;
  final Set<String> selectedSecurities;
  final VoidCallback onRefresh;
  final Future<void> Function(int oldIndex, int newIndex) onReorder;
  final Function(SecurityModel security) onSecurityTap;
  final Future<void> Function(List<SecurityModel> securities) onDeleteSecurities;

  const WatchlistTabContent({
    super.key,
    required this.watchlist,
    required this.filteredSecurities,
    required this.stockPrices,
    required this.sodPrices,
    required this.positions,
    required this.showSearch,
    required this.selectedSecurities,
    required this.onRefresh,
    required this.onReorder,
    required this.onSecurityTap,
    required this.onDeleteSecurities,
  });

  @override
  Widget build(BuildContext context) {
    if (filteredSecurities.isEmpty) {
      return const EmptyContainer(
        title: "Watchlist is Empty",
        message: "Add securities to this watchlist to see them here.",
        imagePath: "images/database-positions-no.png",
      );
    }

    return RefreshIndicator(
      onRefresh: () async => onRefresh(),
      child: showSearch
          ? _buildSearchableListView()
          : _buildReorderableListView(),
    );
  }

  Widget _buildReorderableListView() {
    return Theme(
      data: ThemeData().copyWith(
        canvasColor: ThemeConstants.blue.withValues(alpha: 0.5),
      ),
      child: ReorderableListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        itemCount: filteredSecurities.length,
        onReorder: onReorder,
        itemBuilder: (context, index) {
          final security = filteredSecurities[index];
          return _buildSecurityItem(
            security: security,
            index: index,
            isReorderable: true,
          );
        },
      ),
    );
  }

  Widget _buildSearchableListView() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      itemCount: filteredSecurities.length,
      itemBuilder: (context, index) {
        final security = filteredSecurities[index];
        return _buildSecurityItem(
          security: security,
          index: index,
          isReorderable: false,
        );
      },
    );
  }

  Widget _buildSecurityItem({
    required SecurityModel security,
    required int index,
    required bool isReorderable,
  }) {
    final price = stockPrices[security.zenId];
    final sodPrice = sodPrices[security.zenId] ?? 0.0;
    final position = _findPosition(security);
    final isSelected = selectedSecurities.contains(security.tradingSymbol);

    return WatchlistSecurityItem(
      key: Key('${security.tradingSymbol}_$index'),
      security: security,
      price: price,
      sodPrice: sodPrice,
      position: position,
      isReorderable: isReorderable,
      isSelected: isSelected,
      onTap: () => onSecurityTap(security),
    );
  }

  PositionsModel _findPosition(SecurityModel security) {
    return positions.firstWhere(
      (p) => p.tradingSymbol == security.tradingSymbol,
      orElse: () => PositionsModel(
        positionCompositeKey: PositionCompKey(
          zenSecId: 0,
          clientId: 0,
          broker: '',
          accountId: 0,
          strategyId: 0,
        ),
        tradingSymbol: '',
        position: 0,
        openCost: 0,
        averageCostPerShare: 0,
        latestPrice: 0,
        sodPrice: sodPrices[security.zenId] ?? 0.0,
        unrealizedPnl: 0,
        unrealizedPnlPercentageChange: 0,
        realizedPnl: 0,
        curMarketValue: 0,
        date: DateTime.now(),
        isOpen: false,
      ),
    );
  }
}