import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:phoenix/features/authentication/data/data_provider/login_failed_exception.dart';
import 'package:phoenix/features/authentication/data/repository/auth_repository.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';

import 'package:phoenix/services/shared_prefrences_service.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository authRepository;

  AuthBloc(this.authRepository) : super(AuthInitial()) {
    on<AuthInitializeEvent>(_onInitialize);
    on<AuthLoginEvent>(_onLogin);
    on<AuthLogoutEvent>(_onLogout);
    on<ClientChangeEvent>(_onClientChangeEvent);
  }

  void _onInitialize(AuthInitializeEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      final credentialsModel = await authRepository.initializeAuth();
      if (credentialsModel != null) {
        emit(AuthAuthenticated(credentialsModel: credentialsModel));
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(errorMessage: 'Initialization failed $e'));
    }
  }

  void _onLogin(AuthLoginEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      final credentialsModel = await authRepository.login();
      emit(AuthAuthenticated(credentialsModel: credentialsModel));
    } on LoginFailedException catch (e) {
      emit(AuthError(errorMessage: e.toString()));
    } catch (e) {
      emit(AuthError(
          errorMessage: 'An unexpected error occurred: ${e.toString()}'));
    }
  }

  void _onLogout(AuthLogoutEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      await authRepository.logout();
      emit(AuthUnauthenticated());
    } on LogoutFailedException catch (e) {
      emit(AuthError(errorMessage: e.toString()));
    } catch (e) {
      emit(AuthError(
          errorMessage: 'An unexpected error occurred: ${e.toString()}'));
    }
  }

  void _onClientChangeEvent(ClientChangeEvent event, Emitter<AuthState> emit) async {
    try {
      // Do NOT clear previous client defaults; persist per client
      final credentialsModel = await authRepository.changeClient(event.clientId);
      emit(AuthAuthenticated(credentialsModel: credentialsModel as CredentialsModel));
    }catch (e) {
      emit(AuthError(
          errorMessage: 'An unexpected error occurred: ${e.toString()}'));
    }
  }
}
