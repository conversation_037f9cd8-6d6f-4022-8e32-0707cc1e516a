import 'package:flutter/material.dart';

class SpinningIconButton extends AnimatedWidget {
  final VoidCallback onPressed;
  final IconData iconData;
  final AnimationController controller;
  const SpinningIconButton({ super.key, required this.controller, required this.iconData, required this.onPressed})
      : super( listenable: controller);

  Widget build(BuildContext context) {
    final Animation<double> _animation = CurvedAnimation(
      parent: controller,
      // Use whatever curve you would like, for more details refer to the Curves class
      curve: Curves.linear,

    );

    return RotationTransition(
     
      turns: _animation,
      child: IconButton(
        
        alignment: Alignment.center,
        icon: Icon(iconData,size: 20,),
        onPressed: onPressed,
      ),
    );
  }
}