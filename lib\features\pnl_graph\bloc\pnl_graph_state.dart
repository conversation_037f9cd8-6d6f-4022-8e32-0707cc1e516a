part of 'pnl_graph_bloc.dart';

@immutable
sealed class PnlGraphState {}

final class PnlGraphInitial extends PnlGraphState {}

class PnlGraphLoading extends PnlGraphState {}

class PnlGraphLoaded extends PnlGraphState {
  final List<FlSpot> chartPoints;
  final DateTime baseDate;

  PnlGraphLoaded(this.chartPoints, this.baseDate);
}

class PnlGraphFormatted extends PnlGraphState {
  final List<FlSpot> chartPoints;
  final DateTime baseDate;
  final bool isRealized;

  PnlGraphFormatted({required this.chartPoints, required this.baseDate, required this.isRealized});
}

class PnlGraphBothFormatted extends PnlGraphState {
  final List<FlSpot> realizedPoints;
  final List<FlSpot> unrealizedPoints;
  final DateTime baseDate;

  PnlGraphBothFormatted({
    required this.realizedPoints,
    required this.unrealizedPoints,
    required this.baseDate,
  });
}

class PnlGraphError extends PnlGraphState {
  final String error;

  PnlGraphError(this.error);
}
