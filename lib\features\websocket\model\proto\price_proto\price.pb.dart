//
//  Generated code. Do not modify.
//  source: price.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../google/protobuf/wrappers.pb.dart' as $0;

class Price extends $pb.GeneratedMessage {
  factory Price({
    $0.Int64Value? zenId,
    $0.DoubleValue? price,
  }) {
    final $result = create();
    if (zenId != null) {
      $result.zenId = zenId;
    }
    if (price != null) {
      $result.price = price;
    }
    return $result;
  }
  Price._() : super();
  factory Price.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Price.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Price', package: const $pb.PackageName(_omitMessageNames ? '' : 'websocket'), createEmptyInstance: create)
    ..aOM<$0.Int64Value>(1, _omitFieldNames ? '' : 'zenId', subBuilder: $0.Int64Value.create)
    ..aOM<$0.DoubleValue>(2, _omitFieldNames ? '' : 'price', subBuilder: $0.DoubleValue.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Price clone() => Price()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Price copyWith(void Function(Price) updates) => super.copyWith((message) => updates(message as Price)) as Price;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Price create() => Price._();
  Price createEmptyInstance() => create();
  static $pb.PbList<Price> createRepeated() => $pb.PbList<Price>();
  @$core.pragma('dart2js:noInline')
  static Price getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Price>(create);
  static Price? _defaultInstance;

  @$pb.TagNumber(1)
  $0.Int64Value get zenId => $_getN(0);
  @$pb.TagNumber(1)
  set zenId($0.Int64Value v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasZenId() => $_has(0);
  @$pb.TagNumber(1)
  void clearZenId() => clearField(1);
  @$pb.TagNumber(1)
  $0.Int64Value ensureZenId() => $_ensure(0);

  @$pb.TagNumber(2)
  $0.DoubleValue get price => $_getN(1);
  @$pb.TagNumber(2)
  set price($0.DoubleValue v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasPrice() => $_has(1);
  @$pb.TagNumber(2)
  void clearPrice() => clearField(2);
  @$pb.TagNumber(2)
  $0.DoubleValue ensurePrice() => $_ensure(1);
}

/// PricesRequest is for streaming all the prices
class PricesEmptyRequest extends $pb.GeneratedMessage {
  factory PricesEmptyRequest() => create();
  PricesEmptyRequest._() : super();
  factory PricesEmptyRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PricesEmptyRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PricesEmptyRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'websocket'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PricesEmptyRequest clone() => PricesEmptyRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PricesEmptyRequest copyWith(void Function(PricesEmptyRequest) updates) => super.copyWith((message) => updates(message as PricesEmptyRequest)) as PricesEmptyRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PricesEmptyRequest create() => PricesEmptyRequest._();
  PricesEmptyRequest createEmptyInstance() => create();
  static $pb.PbList<PricesEmptyRequest> createRepeated() => $pb.PbList<PricesEmptyRequest>();
  @$core.pragma('dart2js:noInline')
  static PricesEmptyRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PricesEmptyRequest>(create);
  static PricesEmptyRequest? _defaultInstance;
}

class Prices extends $pb.GeneratedMessage {
  factory Prices({
    $core.Iterable<Price>? prices,
  }) {
    final $result = create();
    if (prices != null) {
      $result.prices.addAll(prices);
    }
    return $result;
  }
  Prices._() : super();
  factory Prices.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Prices.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Prices', package: const $pb.PackageName(_omitMessageNames ? '' : 'websocket'), createEmptyInstance: create)
    ..pc<Price>(1, _omitFieldNames ? '' : 'prices', $pb.PbFieldType.PM, subBuilder: Price.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Prices clone() => Prices()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Prices copyWith(void Function(Prices) updates) => super.copyWith((message) => updates(message as Prices)) as Prices;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Prices create() => Prices._();
  Prices createEmptyInstance() => create();
  static $pb.PbList<Prices> createRepeated() => $pb.PbList<Prices>();
  @$core.pragma('dart2js:noInline')
  static Prices getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Prices>(create);
  static Prices? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Price> get prices => $_getList(0);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
