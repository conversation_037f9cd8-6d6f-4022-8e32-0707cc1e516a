class BrokerAccountStrategyData {
  final String brokerName;
  final int accountId;
  final String accountName;
  final int strategyId;
  final String strategyName;

  BrokerAccountStrategyData({
    required this.brokerName,
    required this.accountId,
    required this.strategyId,
    required this.accountName,
    required this.strategyName,
  });

  factory BrokerAccountStrategyData.fromJson(Map<String, dynamic> json) {
    return BrokerAccountStrategyData(
      brokerName: json['brokerName'],
      accountId: json['accountId'],
      strategyId: json['strategyId'],
      accountName: json['accountName'],
      strategyName: json['strategyName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'brokerName': brokerName,
      'accountId': accountId,
      'strategyId': strategyId,
      'accountName': accountName,
      'strategyName': strategyName,
    };
  }
  @override
  String toString() {
    return 'BrokerAccountStrategyData(brokerName: $brokerName, accountId: $accountId, strategyId: $strategyId, accountName: $accountName, strategyName: $strategyName)';
  }
}