import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesService {
  static SharedPreferencesService? _instance;
  static late SharedPreferences _preferences;

  SharedPreferencesService._();

  /// Initialize and preload preferences synchronously
  static Future<void> initialize() async {
    if (_instance == null) {
      _preferences = await SharedPreferences.getInstance();
      _instance = SharedPreferencesService._();
      debugPrint("⚡️ Shared Prefrence Service is initlaized");
    }
  }

  /// Singleton accessor
  static SharedPreferencesService get instance {
    if (_instance == null) {
      throw Exception("SharedPreferencesService has not been initialized. Call initialize() first.");
    }
    return _instance!;
  }

  /// Retrieve access token synchronously
  String? get accessToken => _preferences.getString('access_token');

  /// Save access token synchronously
  set accessToken(String? token) {
    if (token != null) {
      _preferences.setString('access_token', token);
    }
  }

  static const String _defaultBrokerKey = 'default_broker';
  static const String _defaultAccountKey = 'default_account';
  static const String _defaultStrategyKey = 'default_strategy';
  static const String _defaultAccountNameKey = 'default_account_name';
  static const String _defaultStrategyNameKey = 'default_strategy_name';

  String _keyWithClient(String base, int clientId) => '${base}_client_$clientId';

  String? getDefaultBroker(int clientId) => _preferences.getString(_keyWithClient(_defaultBrokerKey, clientId));
  String? getDefaultAccount(int clientId) => _preferences.getString(_keyWithClient(_defaultAccountKey, clientId));
  String? getDefaultStrategy(int clientId) => _preferences.getString(_keyWithClient(_defaultStrategyKey, clientId));
  String? getDefaultAccountName(int clientId) => _preferences.getString(_keyWithClient(_defaultAccountNameKey, clientId));
  String? getDefaultStrategyName(int clientId) => _preferences.getString(_keyWithClient(_defaultStrategyNameKey, clientId));

  void saveDefaultSelections({
    required int clientId,
    required String brokerName,
    required String accountId,
    required String strategyId,
    String? accountName,
    String? strategyName,
  }) {
    _preferences.setString(_keyWithClient(_defaultBrokerKey, clientId), brokerName);
    _preferences.setString(_keyWithClient(_defaultAccountKey, clientId), accountId);
    _preferences.setString(_keyWithClient(_defaultStrategyKey, clientId), strategyId);
    if (accountName != null) {
      _preferences.setString(_keyWithClient(_defaultAccountNameKey, clientId), accountName);
    }
    if (strategyName != null) {
      _preferences.setString(_keyWithClient(_defaultStrategyNameKey, clientId), strategyName);
    }
  }

  void clearDefaultSelections(int clientId) {
    _preferences.remove(_keyWithClient(_defaultBrokerKey, clientId));
    _preferences.remove(_keyWithClient(_defaultAccountKey, clientId));
    _preferences.remove(_keyWithClient(_defaultStrategyKey, clientId));
    _preferences.remove(_keyWithClient(_defaultAccountNameKey, clientId));
    _preferences.remove(_keyWithClient(_defaultStrategyNameKey, clientId));
  }

  /// Generic method to get data of any type
  static T? getData<T>(String key) {
    switch (T) {
      case String:
        return _preferences.getString(key) as T?;
      case int:
        return _preferences.getInt(key) as T?;
      case double:
        return _preferences.getDouble(key) as T?;
      case bool:
        return _preferences.getBool(key) as T?;
      default:
        return null;
    }
  }

  /// Generic method to set data of any type
  static Future<bool> setData<T>(String key, T value) async {
    switch (T) {
      case String:
        return await _preferences.setString(key, value as String);
      case int:
        return await _preferences.setInt(key, value as int);
      case double:
        return await _preferences.setDouble(key, value as double);
      case bool:
        return await _preferences.setBool(key, value as bool);
      default:
        return false;
    }
  }

  /// Clear all preferences
  void clearPreferences() {
    _preferences.clear();
  }
}
