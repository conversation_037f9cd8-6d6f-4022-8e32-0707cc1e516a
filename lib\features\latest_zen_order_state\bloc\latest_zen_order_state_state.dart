part of 'latest_zen_order_state_bloc.dart';

@immutable
sealed class LatestZenOrderStateState {}

final class LatestZenOrderStateInitial extends LatestZenOrderStateState {}

final class LatestZenOrderStateLoading extends LatestZenOrderStateState {}

final class LatestZenOrderStateEmpty extends LatestZenOrderStateState{}

final class LatestZenOrderStateLoaded extends LatestZenOrderStateState {
  final LatestOrder data;

  LatestZenOrderStateLoaded({required this.data});
}

final class LatestZenOrderStateError extends LatestZenOrderStateState {
  final String error;

  LatestZenOrderStateError({required this.error});
}


