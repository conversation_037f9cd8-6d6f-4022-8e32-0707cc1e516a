import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';

class CustomRadioWidget<T> extends StatelessWidget {
  final T value;
  final T groupValue;
  final ValueChanged<T> onChanged;
  final double width;
  final double height;
  final Color? isActiveColor;
  final Color? isNotActiveColor;
  final Color? borderColor;
  final Color? diabledColor;
  final bool isDisabled;

  const CustomRadioWidget({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    this.width = 32,
    this.height = 32,
    this.borderColor,
    this.isActiveColor,
    this.isNotActiveColor,
    this.diabledColor,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        final effectiveBorderColor = borderColor ?? AppTheme.textPrimary(themeState.isDarkMode);
        final effectiveActiveColor = isActiveColor ?? AppTheme.textPrimary(themeState.isDarkMode);
        final effectiveNotActiveColor = isNotActiveColor ?? AppTheme.backgroundColor(themeState.isDarkMode);
        final effectiveDisabledColor = diabledColor ?? Colors.grey;

        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            if(!isDisabled){
              onChanged(this.value);
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              height: this.height,
              width: this.width,
              decoration: ShapeDecoration(
                shape: CircleBorder(), 
                color: effectiveBorderColor
              ),
              child: Center(
                child: Container(
                  height: this.height - 2.5,
                  width: this.width - 2.5,
                  decoration: ShapeDecoration(
                    shape: CircleBorder(),
                    color: isDisabled 
                        ? effectiveDisabledColor 
                        : value == groupValue 
                            ? effectiveActiveColor 
                            : effectiveNotActiveColor,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
