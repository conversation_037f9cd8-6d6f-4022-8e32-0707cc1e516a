import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:phoenix/features/websocket/data/provider/option_greeks_websocket_provider.dart';
import 'package:phoenix/features/websocket/model/proto/option_greeks/option_greeks.pb.dart';
import 'package:phoenix/utils/websocket_constants.dart';

part 'option_greeks_websocket_event.dart';
part 'option_greeks_websocket_state.dart';

class OptionGreeksWebSocketBloc extends Bloc<OptionGreeksWebSocketEvent, OptionGreeksWebSocketState> {
  final OptionGreeksWebSocketProvider _provider;
  StreamSubscription<List<ZenOptionGreeks>>? _subscription;
  StreamSubscription<bool>? _connectionSubscription;

  List<ZenOptionGreeks> _optionGreeksList = []; // Store entire option Greeks list
  String? _selectedUnderlyingId; // Store selected underlying stock ID

  OptionGreeksWebSocketBloc(this._provider) : super(OptionGreeksWebSocketInitial()) {
    on<OptionGreeksWebSocketConnect>(_onConnect);
    on<OptionGreeksWebSocketDisconnect>(_onDisconnect);
    on<OptionGreeksWebSocketSendMessage>(_onSendMessage);
    on<OptionGreeksWebSocketReceivedData>(_onDataReceived);
    on<OptionGreeksWebSocketSelectUnderlying>(_onSelectUnderlying);
    on<OptionGreeksWebSocketDisconnected>(_onDisconnected);
  }

  /// Handles WebSocket connection.
  Future<void> _onConnect(
      OptionGreeksWebSocketConnect event, Emitter<OptionGreeksWebSocketState> emit) async {
    if (state is OptionGreeksWebSocketConnected || state is OptionGreeksWebSocketConnecting) return;
    print("⚡ Connecting Option Greeks WebSocket...");
    emit(OptionGreeksWebSocketConnecting());

    try {
      final url = WebSocketConstants.optionGreeksWSUrl;
      final headers = {'Authorization': 'Bearer ${event.accessToken}'};

      _provider.connect(url, headers: headers);

      _subscription?.cancel();
      _subscription = _provider.stream.listen(
        (optionGreeksList) {
          _optionGreeksList = optionGreeksList; // Store the entire option Greeks list
          add(OptionGreeksWebSocketReceivedData(_optionGreeksList)); // Emit full list
        },
        onError: (error) {
          add(OptionGreeksWebSocketErrorEvent('Stream error: ${error.toString()}'));
        },
      );

      // Listen to connection status
      _connectionSubscription?.cancel();
      _connectionSubscription = _provider.connectionStatusStream.listen(
        (isConnected) {
          if (!isConnected) {
            add(OptionGreeksWebSocketDisconnected()); // Trigger disconnect event when WebSocket dies
          }
        },
      );

      emit(OptionGreeksWebSocketConnected(_optionGreeksList));
    } catch (e) {
      emit(OptionGreeksWebSocketError('Connection failed: ${e.toString()}'));
    }
  }

  /// Handles underlying stock selection - filters option Greeks for the selected underlying.
  Future<void> _onSelectUnderlying(
      OptionGreeksWebSocketSelectUnderlying event, Emitter<OptionGreeksWebSocketState> emit) async {
    if (event.underlyingId == null || event.underlyingId!.isEmpty) {
      _selectedUnderlyingId = null; // Reset the selected underlying
      emit(OptionGreeksWebSocketUnderlyingSelected([])); // Emit empty list
      return;
    }

    _selectedUnderlyingId = event.underlyingId;

    final filteredGreeks = _optionGreeksList.where(
      (greeks) => greeks.underlyingZenId.value.toString() == _selectedUnderlyingId,
    ).toList();

    debugPrint("🐸 Bloc emitting for underlying - $_selectedUnderlyingId - ${filteredGreeks.length} options");
    emit(OptionGreeksWebSocketUnderlyingSelected(filteredGreeks));
  }

  /// Handles incoming option Greeks data and updates state.
  Future<void> _onDataReceived(
      OptionGreeksWebSocketReceivedData event, Emitter<OptionGreeksWebSocketState> emit) async {
    _optionGreeksList = event.data; // Update the stored option Greeks list
    debugPrint("😎 Bloc emitting all option Greeks: ${_optionGreeksList.length}");
    emit(OptionGreeksWebSocketDataReceived(_optionGreeksList));

    /// If an underlying is selected, update its option Greeks in UI
    if (_selectedUnderlyingId != null) {
      debugPrint("😎 Bloc emitting for selected underlying");
      add(OptionGreeksWebSocketSelectUnderlying(_selectedUnderlyingId!));
    }
  }

  /// Disconnects WebSocket.
  Future<void> _onDisconnect(
      OptionGreeksWebSocketDisconnect event, Emitter<OptionGreeksWebSocketState> emit) async {
    await _subscription?.cancel();
    _provider.disconnect();
    _optionGreeksList.clear();
    emit(OptionGreeksWebSocketInitial());
  }

  /// Handles unexpected disconnection.
  Future<void> _onDisconnected(
      OptionGreeksWebSocketDisconnected event, Emitter<OptionGreeksWebSocketState> emit) async {
    debugPrint("⚠️ Option Greeks WebSocket unexpectedly disconnected!");
    _optionGreeksList.clear();
    emit(OptionGreeksWebSocketDisconnectedState(isConnected: false));
  }

  /// Sends a message over WebSocket.
  Future<void> _onSendMessage(
      OptionGreeksWebSocketSendMessage event, Emitter<OptionGreeksWebSocketState> emit) async {
    try {
      _provider.sendPing(event.message);
    } catch (e) {
      emit(OptionGreeksWebSocketError('Error sending message: ${e.toString()}'));
    }
  }

  @override
  Future<void> close() async {
    await _subscription?.cancel();
    await _connectionSubscription?.cancel();
    _provider.disconnect();
    return super.close();
  }
}