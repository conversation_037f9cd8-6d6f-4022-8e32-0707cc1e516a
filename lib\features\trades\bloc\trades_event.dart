import 'package:equatable/equatable.dart';

abstract class TradesEvent extends Equatable {
  const TradesEvent();

  @override
  List<Object?> get props => [];
}

class FetchTradesData extends TradesEvent {
  final int clientId;
  final String? startTimestamp;
  final String? endTimestamp;

  const FetchTradesData(this.clientId, {this.startTimestamp, this.endTimestamp});

  @override
  List<Object?> get props => [clientId, startTimestamp, endTimestamp];
}

class TradesInitializeEvent extends TradesEvent {}