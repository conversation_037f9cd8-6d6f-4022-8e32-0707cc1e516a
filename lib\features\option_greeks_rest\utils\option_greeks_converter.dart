import 'package:fixnum/fixnum.dart';
import 'package:phoenix/features/option_greeks_rest/model/option_greeks_rest_model.dart';
import 'package:phoenix/features/websocket/model/proto/option_greeks/option_greeks.pb.dart';
import 'package:phoenix/features/websocket/model/proto/google/protobuf/wrappers.pb.dart' as $0;
import 'package:phoenix/features/websocket/model/proto/google/type/date.pb.dart' as $1;

class OptionGreeksConverter {
  /// Converts REST API data to WebSocket protobuf format
  static List<ZenOptionGreeks> convertRestToWebSocket(List<OptionGreeksRestData> restData) {
    return restData.map((restItem) => _convertSingleItem(restItem)).toList();
  }

  static ZenOptionGreeks _convertSingleItem(OptionGreeksRestData restItem) {
    // Parse expiry date
    final expiryDate = restItem.expiryDate;
    final date = $1.Date()
      ..year = expiryDate.year
      ..month = expiryDate.month
      ..day = expiryDate.day;

    return ZenOptionGreeks(
      zenId: $0.Int64Value()..value = Int64(restItem.zenId),
      underlyingZenId: $0.Int64Value()..value = Int64(restItem.underlyingZenId),
      impliedVolatility: $0.DoubleValue()..value = restItem.impliedVolatility,
      delta: $0.DoubleValue()..value = restItem.delta,
      delta2: $0.DoubleValue()..value = restItem.delta2,
      theta: $0.DoubleValue()..value = restItem.theta,
      gamma: $0.DoubleValue()..value = restItem.gamma,
      vega: $0.DoubleValue()..value = restItem.vega,
      rho: $0.DoubleValue()..value = restItem.rho,
      color: $0.DoubleValue()..value = restItem.color,
      charm: $0.DoubleValue()..value = restItem.charm,
      moneyness: $0.StringValue()..value = restItem.moneyness,
      tradingSymbol: $0.StringValue()..value = restItem.tradingSymbol,
      strike: $0.DoubleValue()..value = restItem.strike,
      expiry: date,
      lastPrice: $0.DoubleValue()..value = restItem.lastPrice,
      oi: $0.Int32Value()..value = restItem.oi,
      oiDayHigh: $0.Int32Value()..value = restItem.oiDayHigh,
      oiDayLow: $0.Int32Value()..value = restItem.oiDayLow,
    );
  }



  /// Filter REST data by underlying zen ID
  static List<OptionGreeksRestData> filterByUnderlying(
    List<OptionGreeksRestData> data,
    String? underlyingZenId,
  ) {
    if (underlyingZenId == null || underlyingZenId.isEmpty) return data;

    final targetZenId = int.tryParse(underlyingZenId);
    if (targetZenId == null) return data;

    return data.where((item) {
      return item.underlyingZenId == targetZenId;
    }).toList();
  }

  /// Filter REST data by expiry date
  static List<OptionGreeksRestData> filterByExpiry(
    List<OptionGreeksRestData> data,
    DateTime? selectedExpiry,
  ) {
    if (selectedExpiry == null) return data;

    return data.where((item) {
      final itemExpiry = item.expiryDate;
      return itemExpiry.year == selectedExpiry.year &&
             itemExpiry.month == selectedExpiry.month &&
             itemExpiry.day == selectedExpiry.day;
    }).toList();
  }

  /// Get unique expiry dates from REST data
  static List<DateTime> getUniqueExpiryDates(List<OptionGreeksRestData> data) {
    final Set<DateTime> uniqueExpiries = {};
    
    for (final item in data) {
      final expiry = item.expiryDate;
      final normalizedExpiry = DateTime(expiry.year, expiry.month, expiry.day);
      uniqueExpiries.add(normalizedExpiry);
    }
    
    final sortedExpiries = uniqueExpiries.toList()..sort();
    return sortedExpiries;
  }
}