import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';

class EmptyContainer extends StatelessWidget {
  final String title;
  final String message;
  final String imagePath;

  const EmptyContainer({
    super.key,
    required this.title,
    required this.message,
    required this.imagePath,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Center(
            child: Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(12),
            boxShadow: ThemeConstants.getNeomorpicShadow(themeState.isDarkMode)
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                imagePath,
                height: 150,
                width: 150,
              ),
              const SizedBox(height: 2),
              Text(
                title,
                style: TextStyle(
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ));
      },
    );
  }
}
