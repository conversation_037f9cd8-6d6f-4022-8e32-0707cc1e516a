import 'dart:convert';
import 'package:flutter/widgets.dart';
import 'package:phoenix/features/common/api_response_model.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';
import 'package:phoenix/services/request_body_filter_service.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/http_service.dart';
import 'package:phoenix/utils/app_exception.dart'; // Import your AppException here

class PnlDataProvider {
  Future<List<PositionPnL>> fetchPnlData(int clientId) async {
    final customHttpService = HttpService();

    try {
      final positionCompKeyFilter =
          await RequestBodyFilterService.buildPositionCompKeyFilter(clientId);

      final requestBody = jsonEncode({
        ...positionCompKeyFilter,
      });
      debugPrint('requestBody: $requestBody');
      final response = await customHttpService.post(
        Uri.parse(ApiPath.getPnL(clientId)),
        body: requestBody,
        headers: {'Content-Type': 'application/json'},
      );

      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if (apiResponse.code == 200 &&
          apiResponse.status == 'SUCCESS' &&
          response.statusCode == 200) {
        final List<PositionPnL> pnlData = (apiResponse.payload as List<dynamic>)
            .map((data) => PositionPnL.fromJson(data))
            .toList();

        return pnlData;
      } else {
        throw AppException(apiResponse.message);
      }
    } catch (e) {
      // Only wrap if it's not already an AppException
      throw e is AppException
          ? e
          : AppException('Error fetching positions: ${e.toString()}');
    }
  }
}
