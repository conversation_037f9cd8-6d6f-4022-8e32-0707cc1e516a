part of 'websocket_bloc.dart';

@immutable
abstract class WebSocketState {
  final bool isConnected;
  final List<Price>? tickList;
  final double? stockPrice;
  final String? error;

  const WebSocketState({
    required this.isConnected,
    this.tickList,
    this.stockPrice,
    this.error,
  });
}

class WebSocketInitial extends WebSocketState {
  const WebSocketInitial() : super(isConnected: false);
}

class WebSocketConnecting extends WebSocketState {
  const WebSocketConnecting() : super(isConnected: false);
}

class WebSocketConnected extends WebSocketState {
  const WebSocketConnected(List<Price> tickList)
      : super(isConnected: false, tickList: tickList);
}

class WebSocketDataReceived extends WebSocketState {
  const WebSocketDataReceived(List<Price> tickList)
      : super(isConnected: true, tickList: tickList);
}

class WebSocketStockPriceUpdated extends WebSocketState {
  const WebSocketStockPriceUpdated(double stockPrice)
      : super(isConnected: true, stockPrice: stockPrice);
}

class WebSocketMultipleStockPricesUpdated extends WebSocketState {
  final Map<int, double> stockPrices;
  const WebSocketMultipleStockPricesUpdated(this.stockPrices) : super(isConnected: true);
}

class WebSocketClearSelectedStocks extends WebSocketEvent {}


class WebSocketStockPriceEmpty extends WebSocketState {
  const WebSocketStockPriceEmpty({required super.isConnected});
  
}

class WebSocketError extends WebSocketState {
  const WebSocketError(String error) : super(isConnected: false, error: error);
}

class WebSocketDisconnectedState extends WebSocketState {
  const WebSocketDisconnectedState({required super.isConnected});
} // New state for disconnection

class PriceUpdate {
  final double previousPrice;
  final double currentPrice;

  PriceUpdate(this.previousPrice, this.currentPrice);

  @override
  String toString() {
   
    return "{current : ${currentPrice} , previous : ${previousPrice}}";
  }
}