part of 'orders_bloc.dart';

@immutable
sealed class OrdersEvent {}

final class OrdersInitializeEvent extends OrdersEvent {}

final class PlaceOrderEvent extends OrdersEvent {

  final OrderFormModel data;

  PlaceOrderEvent(this.data);

}

final class PlaceBulkOrderEvent extends OrdersEvent {

  final List<OrderFormModelForBucket> data;
  final int clientId;

  PlaceBulkOrderEvent(this.data, this.clientId);

}




