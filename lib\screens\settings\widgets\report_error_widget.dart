import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/services/logger_service.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/toast/custom_toast.dart';
import 'package:phoenix/widgets/toast/toast_utils.dart';

class ReportErrorWidget extends StatelessWidget {
  const ReportErrorWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          margin: const EdgeInsets.only(top: 16),
          decoration: BoxDecoration(
            color: AppTheme.cardColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppTheme.borderColor(themeState.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: themeState.isDarkMode ? Colors.black26 : Colors.grey.withOpacity(0.2),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.bug_report_outlined,
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      "Error Reporting",
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  "If you encounter any issues with the app, you can share the debug logs to help us diagnose and fix the problem.",
                  style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildReportButton(
                      context,
                      themeState,
                      "Current Log",
                      Icons.file_present_outlined,
                      _shareCurrentLog,
                    ),
                    const SizedBox(width: 16),
                    _buildReportButton(
                      context,
                      themeState,
                      "All Logs",
                      Icons.folder_outlined,
                      _shareAllLogs,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildReportButton(
    BuildContext context,
    ThemeState themeState,
    String label,
    IconData icon,
    Function(BuildContext) onTap,
  ) {
    return Expanded(
      child: InkWell(
        onTap: () => onTap(context),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppTheme.borderColor(themeState.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.2),
                offset: const Offset(0, 0),
                blurRadius: 4,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: AppTheme.primaryColor(themeState.isDarkMode),
                size: 24,
              ),
              const SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _shareCurrentLog(BuildContext context) async {
    try {
      final loggerService = LoggerService();
      await loggerService.shareCurrentLog(context);
      ToastUtil.showToast(
        context,
        "Log file ready to share",
        ToastType.success,
      );
    } catch (e) {
      ToastUtil.showToast(
        context,
        "Failed to share log: $e",
        ToastType.error,
      );
    }
  }

  void _shareAllLogs(BuildContext context) async {
    try {
      final loggerService = LoggerService();
      await loggerService.shareAllLogs(context);
      ToastUtil.showToast(
        context,
        "Log files ready to share",
        ToastType.success,
      );
    } catch (e) {
      ToastUtil.showToast(
        context,
        "Failed to share logs: $e",
        ToastType.error,
      );
    }
  }
}