import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/trades/model/trades_analytics_model.dart';
import 'package:phoenix/utils/app_theme.dart';

class TradesAnalyticsScreen extends StatelessWidget {
  final TradesAnalyticsModel analytics;

  const TradesAnalyticsScreen({
    super.key,
    required this.analytics,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          appBar: AppBar(
            title: Text(
              'Trade Analytics',
              style: TextStyle(
                color: AppTheme.textPrimary(themeState.isDarkMode),
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
            elevation: 0,
            iconTheme: IconThemeData(color: AppTheme.textPrimary(themeState.isDarkMode)),
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Overall Statistics
                Text(
                  'Overall Statistics',
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildOverallStatsGrid(themeState),
                const SizedBox(height: 32),
                
                // Broker-wise Analytics
                if (analytics.brokerAnalytics.isNotEmpty) ...[
                  Text(
                    'Broker-wise Investment',
                    style: TextStyle(
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildBrokerAnalytics(themeState),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildOverallStatsGrid(ThemeState themeState) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.6,
      children: [
        _buildStatCard(
          'Buy Trades',
          analytics.buyTrades.toString(),
          Icons.trending_up,
          const Color(0xFF4CAF50),
          themeState,
        ),
        _buildStatCard(
          'Sell Trades',
          analytics.sellTrades.toString(),
          Icons.trending_down,
          const Color(0xFFF44336),
          themeState,
        ),
        _buildStatCard(
          'Total Trades',
          analytics.totalTrades.toString(),
          Icons.bar_chart,
          const Color(0xFF4A90E2),
          themeState,
        ),
        _buildStatCard(
          'Total Volume',
          analytics.totalVolume.toString(),
          Icons.assessment,
          const Color(0xFF9C27B0),
          themeState,
        ),
        _buildStatCard(
          'Total Value',
          '₹${_formatCurrency(analytics.totalValue)}',
          Icons.wallet_outlined,
          const Color(0xFF2196F3),
          themeState,
        ),
        _buildStatCard(
          'Avg Trade Size',
          '₹${_formatCurrency(analytics.avgTradeSize)}',
          Icons.analytics,
          const Color(0xFFFF9800),
          themeState,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeState.isDarkMode ? const Color(0xFF2D3035) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: themeState.isDarkMode ? const Color(0xFF404449) : Colors.grey.shade300,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBrokerAnalytics(ThemeState themeState) {
    return Column(
      children: analytics.brokerAnalytics.entries.map((entry) {
        final brokerAnalytics = entry.value;
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: themeState.isDarkMode ? const Color(0xFF2D3035) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: themeState.isDarkMode ? const Color(0xFF404449) : Colors.grey.shade300,
            ),
          ),
          child: Row(
            children: [
              // Broker Avatar/Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getBrokerColor(brokerAnalytics.brokerName),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    _getBrokerInitials(brokerAnalytics.brokerName),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Broker Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _formatBrokerName(brokerAnalytics.brokerName),
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${brokerAnalytics.tradeCount} trades • ${brokerAnalytics.volume} volume',
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              // Investment Amount
              Text(
                '₹${_formatCurrency(brokerAnalytics.totalInvested)}',
                style: TextStyle(
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  String _formatCurrency(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(2)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(2)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(2)}K';
    } else {
      return amount.toStringAsFixed(2);
    }
  }

  String _formatBrokerName(String brokerName) {
    switch (brokerName.toUpperCase()) {
      case 'ZEN_BROKER':
        return 'Zen Broker';
      case 'ZERODHA':
        return 'Zerodha';
      case 'KOTAK':
        return 'Kotak';
      default:
        return brokerName;
    }
  }

  String _getBrokerInitials(String brokerName) {
    switch (brokerName.toUpperCase()) {
      case 'ZEN_BROKER':
        return 'ZB';
      case 'ZERODHA':
        return 'ZD';
      case 'KOTAK':
        return 'KT';
      default:
        return brokerName.substring(0, 2).toUpperCase();
    }
  }

  Color _getBrokerColor(String brokerName) {
    switch (brokerName.toUpperCase()) {
      case 'ZEN_BROKER':
        return const Color(0xFF4A90E2);
      case 'ZERODHA':
        return const Color(0xFF387ED1);
      case 'KOTAK':
        return const Color(0xFFE53E3E);
      default:
        return const Color(0xFF6B7280);
    }
  }
}
