import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';
import 'package:phoenix/features/broker_account_strategy_selection/bloc/broker_account_strategy_selection_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/services/shared_prefrences_service.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/widgets/order_form/order_form_margin_viewer.dart';
import 'package:phoenix/widgets/order_form/selection_drop_down.dart';

class OrderPrefrenceModal extends StatefulWidget {
  final bool showSetAsDefault;
  final ValueChanged<int> onDefaultSet;
  const OrderPrefrenceModal(
      {super.key, this.showSetAsDefault = true, required this.onDefaultSet});
  @override
  State<OrderPrefrenceModal> createState() => _OrderPrefrenceModalState();
}

class _OrderPrefrenceModalState extends State<OrderPrefrenceModal> {
  bool _setAsDefault = false;

  @override
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final prefs = SharedPreferencesService.instance;
      final authState = context.read<AuthBloc>().state;
      int? clientId;
      if (authState is AuthAuthenticated) {
        clientId = authState.credentialsModel.clientId;
      }
      if (clientId == null) return;

      final defaultBroker = prefs.getDefaultBroker(clientId);
      final defaultAccount = prefs.getDefaultAccount(clientId);
      final defaultStrategy = prefs.getDefaultStrategy(clientId);

      final bloc = context.read<BrokerAccountStrategySelectionBloc>();
      final state = bloc.state;

      setState(() {
        _setAsDefault = (defaultBroker != null &&
            defaultAccount != null &&
            defaultStrategy != null);
      });

      // Select default broker
      if (defaultBroker != null) {
        final broker = state.brokers.firstWhere(
          (b) => b.brokerName == defaultBroker,
          orElse: () => state.brokers.first,
        );
        if (broker != state.selectedBroker) {
          bloc.add(BrokerSelected(broker));
          await Future.delayed(Duration(milliseconds: 100));
        }
      }

      // Select default account
      if (defaultAccount != null) {
        final account = bloc.state.availableAccounts.firstWhere(
          (a) => a.accountId.toString() == defaultAccount,
          orElse: () => bloc.state.availableAccounts.first,
        );
        if (account != bloc.state.selectedAccount) {
          bloc.add(AccountSelected(account));
          await Future.delayed(Duration(milliseconds: 100));
        }
      }

      // Select default strategy
      if (defaultStrategy != null) {
        final strategy = bloc.state.availableStrategies.firstWhere(
          (s) => s.strategyId.toString() == defaultStrategy,
          orElse: () => bloc.state.availableStrategies.first,
        );
        if (strategy != bloc.state.selectedStrategy) {
          bloc.add(StrategySelected(strategy));
        }
      }
    });
  }

  void onCancel(BuildContext context) {
    Navigator.of(context).pop();
  }

  void onConfirm(BuildContext context) {
    final state = context.read<BrokerAccountStrategySelectionBloc>().state;
    final brokerName = state.selectedBroker?.brokerName;
    final accountId = state.selectedAccount?.accountId;
    final strategyId = state.selectedStrategy?.strategyId;
    final accountName = state.selectedAccount?.accountName;
    final strategyName = state.selectedStrategy?.strategyName;

    // Prepare the result with actual selected values
    final result = {
      'brokerName': brokerName,
      'accountId': accountId,
      'strategyId': strategyId,
      'accountName': accountName,
      'strategyName': strategyName,
      'setAsDefault': _setAsDefault,
    };

    Navigator.of(context).pop(result);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Dialog(
          backgroundColor: AppTheme.surfaceColor(themeState.isDarkMode),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            constraints: const BoxConstraints(maxWidth: 400),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
            // Header with title and close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Select Account',
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: AppTheme.textSecondary(themeState.isDarkMode)),
                  onPressed: () => Navigator.of(context).pop(),
                  splashRadius: 20,
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Broker Dropdown
            _buildDropdownField(
              context: context,
              label: "Broker",
              themeState: themeState,
              child: BlocBuilder<BrokerAccountStrategySelectionBloc,
                  BrokerAccountStrategyState>(
                builder: (context, state) {
                  // Create a list of brokers with "All" option if there's more than one broker
                  List<BrokerInfo> brokerItems = [...state.brokers];

                  // Check if the current selection is "All" but not in the items list
                  BrokerInfo? selectedBroker = state.selectedBroker;

                  return SelectionDropdown<BrokerInfo>(
                    hint: "Select broker",
                    selectedValue: selectedBroker,
                    items: brokerItems,
                    getItemLabel: (broker) => broker.brokerName,
                    onChanged: (broker) {
                      if (broker != null) {
                        context
                            .read<BrokerAccountStrategySelectionBloc>()
                            .add(BrokerSelected(broker));
                      }
                    },
                    dropdownDecoration: BoxDecoration(
                      color: Colors.grey.shade900,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade800),
                    ),
                    itemTextStyle: const TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            // Account Dropdown
            _buildDropdownField(
              context: context,
              label: "Account",
              themeState: themeState,
              child: BlocBuilder<BrokerAccountStrategySelectionBloc,
                  BrokerAccountStrategyState>(
                builder: (context, state) {
                  // Create a list of accounts with "All" option if there's more than one account
                  List<AccountInfo> accountItems = [...state.availableAccounts];

                  // Check if the current selection is "All" but not in the items list
                  AccountInfo? selectedAccount = state.selectedAccount;

                  return SelectionDropdown<AccountInfo>(
                    hint: "Select account",
                    selectedValue: selectedAccount,
                    items: accountItems,
                    getItemLabel: (account) => account.accountName,
                    onChanged: (account) {
                      if (account != null) {
                        context
                            .read<BrokerAccountStrategySelectionBloc>()
                            .add(AccountSelected(account));
                      }
                    },
                    dropdownDecoration: BoxDecoration(
                      color: Colors.grey.shade900,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade800),
                    ),
                    itemTextStyle: const TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            // Strategy Dropdown
            _buildDropdownField(
              context: context,
              label: "Strategy",
              themeState: themeState,
              child: BlocBuilder<BrokerAccountStrategySelectionBloc,
                  BrokerAccountStrategyState>(
                builder: (context, state) {
                  // Create a list of strategies with "All" option if there's more than one strategy
                  List<Strategy> strategyItems = [...state.availableStrategies];

                  // Check if the current selection is "All" but not in the items list
                  Strategy? selectedStrategy = state.selectedStrategy;

                  return SelectionDropdown<Strategy>(
                    hint: "Select strategy",
                    selectedValue: selectedStrategy,
                    items: strategyItems,
                    getItemLabel: (strategy) => strategy.strategyName,
                    onChanged: (strategy) {
                      if (strategy != null) {
                        context
                            .read<BrokerAccountStrategySelectionBloc>()
                            .add(StrategySelected(strategy));
                      }
                    },
                    dropdownDecoration: BoxDecoration(
                      color: Colors.grey.shade900,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade800),
                    ),
                    itemTextStyle: const TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 20),
            // Balance Row
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "Balance",
                  style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 15,
                  ),
                ),
                const SizedBox(width: 10), // Add spacing if needed
                Expanded(
                  child: OrferFormMarginViewer(),
                ),
              ],
            ),


            // if (widget.showSetAsDefault) ...[
            //   const SizedBox(height: 18),
            //   Row(
            //     children: [
            //       SizedBox(
            //         width: 24,
            //         height: 24,
            //         child: Checkbox(
            //           value: _setAsDefault,
            //           onChanged: (value) {
            //             setState(() {
            //               _setAsDefault = value ?? false;
            //             });
            //           },
            //           fillColor: WidgetStateProperty.resolveWith<Color>(
            //             (Set<WidgetState> states) {
            //               if (states.contains(WidgetState.selected)) {
            //                 return ThemeConstants.blue;
            //               }
            //               return Colors.grey.shade800;
            //             },
            //           ),
            //           shape: RoundedRectangleBorder(
            //             borderRadius: BorderRadius.circular(4),
            //           ),
            //         ),
            //       ),
            //       const SizedBox(width: 10),
            //       Expanded(
            //         child: Text(
            //           "Set as default for future orders",
            //           style: TextStyle(
            //             color: Colors.grey.shade300,
            //             fontSize: 15,
            //             fontWeight: FontWeight.w500,
            //           ),
            //         ),
            //       ),
            //     ],
            //   ),
            // ],
            
            
            
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppTheme.borderColor(themeState.isDarkMode)),
                      foregroundColor: AppTheme.textSecondary(themeState.isDarkMode),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text("Cancel",
                        style: TextStyle(fontWeight: FontWeight.w600)),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => onConfirm(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor(themeState.isDarkMode),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      "Continue",
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            ],
          ),
        ));
      },
    );
  }

  Widget _buildDropdownField({
    required BuildContext context,
    required String label,
    required Widget child,
    required ThemeState themeState,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(
            label,
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 14,
            ),
          ),
        ),
        child,
      ],
    );
  }
}
