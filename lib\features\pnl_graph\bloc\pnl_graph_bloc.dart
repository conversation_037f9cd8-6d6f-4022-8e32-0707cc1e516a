import 'package:bloc/bloc.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';
import 'package:phoenix/features/pnl_graph/repository/pnl_graph_formatter.dart';
import 'package:phoenix/features/pnl_graph/repository/pnl_graph_repository.dart';

part 'pnl_graph_event.dart';
part 'pnl_graph_state.dart';

///
///This bloc is used to fetch and format the PnL graph data
/// And handle date range fetchings

class PnlGraphBloc extends Bloc<PnlGraphEvent, PnlGraphState> {
  final PnlGraphRepository _repository;
  List<PositionPnL> _cachedPnLList = []; // <-- Store here
  String? _currentRequestId; // to track the current request

  PnlGraphBloc(this._repository) : super(PnlGraphInitial()) {
    on<FetchPnlGraphData>(_onFetchPnlGraphData);
    on<FormatPnlGraphData>(_onFormatPnlGraphData);
    on<ClearPnlGraphData>(_onClearPnlGraphData);
  }

  void _onFetchPnlGraphData(
    FetchPnlGraphData event,
    Emitter<PnlGraphState> emit,
  ) async {
    _currentRequestId = event.requestId; // 🆔
    emit(PnlGraphLoading());
    try {
      final chartPoints = await _repository.fetchChartPoints(
        clientId: event.clientId,
        startTimestamp: event.startTimestamp,
        endTimestamp: event.endTimestamp,
        zenSecIds: event.zenSecIds,
        strategyId: event.strategyId,
      );

      if (_currentRequestId != event.requestId) {
        // Ignore if a newer request has taken over
        return;
      }

      // Store the fetched PnL list for future formatting
      _cachedPnLList = chartPoints['chartPoints'] as List<PositionPnL>;
      emit(PnlGraphLoaded(
          _cachedPnLList.cast<FlSpot>(), chartPoints['baseDate']));
    } catch (e) {
      emit(PnlGraphError(e.toString()));
    }
  }

  void _onFormatPnlGraphData(
    FormatPnlGraphData event,
    Emitter<PnlGraphState> emit,
  ) {
    if (_cachedPnLList.isEmpty) {
      emit(PnlGraphError('No PnL data available to format.'));
      return;
    }

    try {
      final baseDate = _cachedPnLList.first.date;

      if (event.isRealized && event.isUnrealized) {
        final realized = PnLGraphFormatter.format(
          pnlList: _cachedPnLList,
          type: event.type,
          isRealized: true,
        );
        final unrealized = PnLGraphFormatter.format(
          pnlList: _cachedPnLList,
          type: event.type,
          isRealized: false,
        );

        emit(PnlGraphBothFormatted(
          realizedPoints: realized,
          unrealizedPoints: unrealized,
          baseDate: baseDate,
        ));
      } else {
        final chartPoints = PnLGraphFormatter.format(
          pnlList: _cachedPnLList,
          type: event.type,
          isRealized: event.isRealized,
        );
        emit(PnlGraphFormatted(
          chartPoints: chartPoints,
          baseDate: baseDate,
          isRealized: event.isRealized,
        ));
      }
    } catch (e) {
      emit(PnlGraphError(e.toString()));
    }
  }

  void _onClearPnlGraphData(
    ClearPnlGraphData event,
    Emitter<PnlGraphState> emit,
  ) {
    _cachedPnLList.clear();
    _currentRequestId = null;
    emit(PnlGraphInitial());
  }
}
