import 'package:intl/intl.dart';

//String toCapitalized functionality
extension StringCasingExtension on String {
  String get toCapitalized =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
  String get toTitleCase => replaceAll(RegExp(' +'), ' ')
      .split(' ')
      .map((str) => str.toCapitalized)
      .join(' ');
}


class UtilFunctions {
  static String formatIndianCurrency(double price) {
    if (price == 0) return "--";
    final formatter = NumberFormat.currency(
      locale: 'en_IN', // Indian numbering format
      symbol: '', // No currency symbol
      decimalDigits: 2, // No decimal places for cts
    );

    return formatter.format(price).trim();
  }

  static String formatIndianCurrencyforPnlGraph(double price) {
    final formatter = NumberFormat.currency(
      locale: 'en_IN', // Indian numbering format
      symbol: '', // No currency symbol
      decimalDigits: 2,
    );

    return formatter.format(price).trim();
  }

  static String formatIndianCurrencyforPnl(double price) {
    final formatter = NumberFormat.currency(
      locale: 'en_IN', // Indian numbering format
      symbol: '', // No currency symbol
      decimalDigits: 2,
    );

    String formatted = formatter.format(price).trim();

    if (price > 0) {
      return '+$formatted';
    } else {
      return formatted;
    }
  }
}
