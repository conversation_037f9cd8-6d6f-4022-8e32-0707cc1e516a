import 'package:equatable/equatable.dart';
import 'package:phoenix/features/trades/model/trades_model.dart';

abstract class TradesState extends Equatable {
  const TradesState();

  @override
  List<Object> get props => [];
}

class TradesInitial extends TradesState {}

class TradesLoading extends TradesState {}

class TradesLoaded extends TradesState {
  final List<TradesModel> tradesData;

  const TradesLoaded(this.tradesData);

  @override
  List<Object> get props => [tradesData];
}

class TradesError extends TradesState {
  final String message;

  const TradesError(this.message);

  @override
  List<Object> get props => [message];
}