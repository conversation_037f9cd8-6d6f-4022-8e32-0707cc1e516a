import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/utils/theme_constants.dart';

class SlideDeleteBackground extends StatelessWidget {
  const SlideDeleteBackground({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ThemeConstants.sellSlideOptionColor,
        borderRadius: const BorderRadius.horizontal(
          left: Radius.circular(20),
          right: Radius.circular(20),
        ),
        boxShadow: [
          // Light inner glow near edges
          BoxShadow(
            color: const Color.fromARGB(255, 255, 89, 89).withValues(alpha: 0.9),
            offset: const Offset(-10, -10),
            blurRadius: 20,
            inset: true,
          ),
          // Dark inner shadow in center-ish
          const BoxShadow(
            color: Colors.black,
            offset: Offset(10, 10),
            blurRadius: 20,
            inset: true,
          ),
        ],
      ),
      child: const Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Icon(Icons.delete, color: Colors.white),
            SizedBox(width: 10),
          ],
        ),
      ),
    );
  }
}