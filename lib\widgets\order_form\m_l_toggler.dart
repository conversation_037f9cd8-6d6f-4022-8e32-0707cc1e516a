import 'package:flutter/material.dart';

class <PERSON><PERSON>og<PERSON> extends StatelessWidget {
  final String selectedType;
  final bool isEnabled;
  final Function(String) onTypeChanged;

  const MLToggler({
    Key? key,
    required this.selectedType,
    required this.onTypeChanged,
    this.isEnabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      width: 90,
      decoration: BoxDecoration(
        color: Color(0xff606F83).withOpacity(0.5),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            spreadRadius: -2,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTypeButton('M'),
          _buildTypeButton('L'),
        ],
      ),
    );
  }

  Widget _buildTypeButton(String type) {
    final bool isSelected = selectedType == type;

    return GestureDetector(
      
      behavior: HitTestBehavior.opaque,
      onTap: isEnabled ?() => onTypeChanged(type):null,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? Color(0xff1C1C1C) : Colors.transparent,
          borderRadius: BorderRadius.circular(50),
        ),
        child: Text(
          type,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey.shade600,
            fontSize: 15,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
