import 'dart:convert';
import 'dart:io';
import 'package:auth0_flutter/auth0_flutter.dart';
import 'package:flutter/material.dart';
import 'package:phoenix/features/authentication/data/data_provider/login_failed_exception.dart';
import 'package:phoenix/features/authentication/model/client_data_model.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';
import 'package:phoenix/features/authentication/model/user_details_model.dart';
import 'package:phoenix/managers/api/api_response_manager.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/app_exception.dart';
import 'package:phoenix/utils/websocket_constants.dart';
import 'package:phoenix/utils/http_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  final Auth0 _auth0 = Auth0(Constants.auth0Domain, Constants.auth0ClientId);

  Future<UserDetailsModel?> fetchUserDetail() async {
    final customHttpService = HttpService();

    final response = await customHttpService.get(
      Uri.parse(ApiPath.userDetail()),
    );

    final data = jsonDecode(response.body);

    if (response.statusCode == 200 && data['status'] == 'SUCCESS') {
      final payload = data['payload'];
      return UserDetailsModel.fromJson(payload);
    }

    return null; // Could be error or no payload
  }

  Future<bool> registerUser(String email) async {
    final customHttpService = HttpService();
    final response = await customHttpService.post(
      Uri.parse(ApiPath.registerUser()),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        "email": email,
        "activate": true,
        "profile_data": jsonEncode({"theme": "dark"})
      }),
    );

    return response.statusCode == 200;
  }

  Future<void> registerClient(String userId, String clientName) async {
    final customHttpService = HttpService();
    final response = await customHttpService.post(
      Uri.parse(ApiPath.registerClient()),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        "user_id": userId,
        "client_name": clientName,
      }),
    );
    if (response.statusCode != 200) {
      throw Exception("Failed to register client");
    }
  }

  ///To get the data from shared Prefrence and fetch clientInfo
  Future<CredentialsModel?> getValidCredentials() async {
    debugPrint(" getValidCredentials");
    final prefs = await SharedPreferences.getInstance();
    debugPrint("📦 Checking for credentials in SharedPreferences");

    if (prefs.containsKey('credentials')) {
      final credentialsJson = prefs.getString('credentials');
      if (credentialsJson != null) {
        debugPrint(" 📦 Credentials found in SharedPreferences");
        debugPrint(credentialsJson.toString());
        try {
          var credentialsModel = CredentialsModel.fromJson(
            Map<String, dynamic>.from(
              jsonDecode(credentialsJson),
            ),
          );

          // Refresh the access token
          final updatedToken =
              await _refreshAccessTokenIfNeeded(credentialsModel.accessToken);

          // If token was updated, update and store the new credentials
          if (updatedToken != credentialsModel.accessToken) {
            credentialsModel =
                credentialsModel.copyWith(accessToken: updatedToken);
            await _storeCredentials(credentialsModel);
            debugPrint("📦 Stored latest access token to SP");
          }

          // Fetch client info and update the model
          final clientInfo = await _fetchClientInfo(credentialsModel.clientId);
          final brokers = (clientInfo['broker_infos'] as List<dynamic>)
              .map((broker) => BrokerInfo.fromJson(broker))
              .toList();
          final clientName = clientInfo['client_name'] as String;

          return credentialsModel.copyWith(brokers: brokers, clientName: clientName);
        } catch (e) {
          debugPrint(
              "Error parsing stored credentials: Please try again after clearing the app cache \n $e");
          return null;
        }
      }
    }

    try {
      debugPrint("Checking for credentials in auth0.credentialsManager");

      final hasValidCredentials =
          await _auth0.credentialsManager.hasValidCredentials();
      if (hasValidCredentials) {
        final credentials = await _auth0.credentialsManager.credentials();
        debugPrint(" ⚙️  ${credentials.refreshToken}");
        debugPrint(credentials.user.sub.toString());

        // Check if the email is verified
        final bool isEmailVerified = credentials.user.isEmailVerified ?? false;
        if (!isEmailVerified) {
          debugPrint("Email is not verified");
          logout();
          throw LoginFailedException(
              "Email is not verified, please verify your email.");
        }

        final userDetails =
            await userDetailsController(credentials.user.email as String);

        final clientsData = await fetchClients();

        ClientDataModel? client;

        try {
          client = clientsData.firstWhere(
              (client) => client.clientName == credentials.user.email);
        } catch (_) {
          // Client not found, register
          await registerClient(
              userDetails.userId.toString(), credentials.user.email as String);

          final updatedClients = await fetchClients();

          client = updatedClients.firstWhere(
            (client) => client.clientName == credentials.user.email,
            orElse: () => throw Exception("Client registration failed"),
          );
        }

        final int clientId = client.clientId;

        // Refresh the access token if needed
        final updatedToken =
            await _refreshAccessTokenIfNeeded(credentials.accessToken);

        // Fetch client info
        final clientInfo = await _fetchClientInfo(clientId);
        final brokers = (clientInfo['broker_infos'] as List<dynamic>)
            .map((broker) => BrokerInfo.fromJson(broker))
            .toList();
       

        final credentialsModel = CredentialsModel(
          accessToken: updatedToken,
          refreshToken: credentials.refreshToken.toString(),
          idToken: credentials.idToken,
          name: credentials.user.name ?? '',
          email: credentials.user.email ?? '',
          pictureUrl: credentials.user.pictureUrl.toString(),
          userId: credentials.user.sub,
          zenUserId: userDetails.userId.toString(),
          clientId: clientId,
          clientName: client.clientName,
          brokers: brokers,
        );

        debugPrint(
            "📦 Storing credentials data of auth0.credentialsManager to SharedPreferences");

        // Store in SharedPreferences
        await _storeCredentials(credentialsModel);

        return credentialsModel;
      }
      return null;
    } catch (e) {
      throw e.toString();
    }
  }

  ///This methos is to login
  ///We are logging in using auth0 then we are fetching the clientId for
  ///that user if not we are registering them a client with autho[sub]
  Future<CredentialsModel> login() async {
    try {
      debugPrint("Logging in...");
      final credentials =
          await _auth0.webAuthentication(scheme: 'phoenixscheme').login(
        audience: "https://service_api",
        scopes: {
          "openid",
          "profile",
          "email",
          "offline_access",
        },

        //redirectUrl: 'phoenixscheme://phoenix-lab.us.auth0.com/android/com.zentropytech.phoenix/callback',
      );

      // Check if the email is verified
      final bool isEmailVerified = credentials.user.isEmailVerified ?? false;
      if (!isEmailVerified) {
        debugPrint("Email is not verified");
        logout();
        throw LoginFailedException(
            "Email is not verified, please verify your email.");
      }

      await _auth0.credentialsManager.storeCredentials(credentials);

      debugPrint("Logged in");
      debugPrint("Credentials are stored in auth0.credentialsManager");
      debugPrint("Sub ${credentials.user.sub}");
      debugPrint(" Access token  ${credentials.accessToken}");
      debugPrint(" ⚙️ ${credentials.refreshToken}");
      debugPrint(credentials.user.sub.toString());
      await _storeAccessToken(credentials.accessToken);

      final userDetails =
          await userDetailsController(credentials.user.email as String);

      final clientsData = await fetchClients();

      ClientDataModel? client;

      try {
        client = clientsData.firstWhere(
            (client) => client.clientName == credentials.user.email);
      } catch (_) {
        // Client not found, register
        await registerClient(
            userDetails.userId.toString(), credentials.user.email as String);

        final updatedClients = await fetchClients();

        client = updatedClients.firstWhere(
          (client) => client.clientName == credentials.user.email,
          orElse: () => throw Exception("Client registration failed"),
        );
      }

      final int clientId = client.clientId;

      // Fetch client info
      final clientInfo = await _fetchClientInfo(clientId);
      final brokers = (clientInfo['broker_infos'] as List<dynamic>)
          .map((broker) => BrokerInfo.fromJson(broker))
          .toList();


      final credentialsModel = CredentialsModel(
        accessToken: credentials.accessToken,
        refreshToken: credentials.refreshToken.toString(),
        idToken: credentials.idToken,
        name: credentials.user.name ?? '',
        email: credentials.user.email ?? '',
        pictureUrl: credentials.user.pictureUrl.toString(),
        userId: credentials.user.sub,
        zenUserId: userDetails.userId.toString(),
        clientId: clientId,
        clientName: client.clientName,
        brokers: brokers,
      );

      // Store in SharedPreferences
      await _storeCredentials(credentialsModel);

      return credentialsModel;
    } catch (e) {
      throw LoginFailedException('Login failed: ${e.toString()}');
    }
  }

  Future<dynamic> _fetchClientInfo(int clientId) async {
    final customHttpService = HttpService();
    try {
      debugPrint("⚡ Fetching Client Info for $clientId");
      final response = await customHttpService
          .get(Uri.parse(ApiPath.getClientInfo("$clientId")));

      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if (apiResponse.code != 200 ||
          apiResponse.status != 'SUCCESS' ||
          response.statusCode != 200) {
        throw AppException(apiResponse.message);
      } else {
        final clientInfo = apiResponse.payload;
        debugPrint("Updated Client Info: ${clientInfo.toString()}");

        
        return clientInfo;
      }
    } catch (e) {
      debugPrint("Error in _fetchClientInfo: $e");
      return [];
    }
  }

  //we will make a fetch to getClients and return the client Ids data of the client
  Future<List<ClientDataModel>> fetchClients() async {
    final customHttpService = HttpService();

    final response =
        await customHttpService.get(Uri.parse(ApiPath.getClients()));
    final data = jsonDecode(response.body);

    if (response.statusCode == 200 && data['status'] == 'SUCCESS') {
      final payload = data['payload'] as List;
      return payload
          .map((clientJson) => ClientDataModel.fromJson(clientJson))
          .toList();
    }

    throw Exception('Failed to fetch clients');
  }

  Future<UserDetailsModel> userDetailsController(String email) async {
    debugPrint("🐚 User Details Controller");

    final userDetails = await fetchUserDetail();

    if (userDetails == null) {
      // Not found, register user
      final registered = await registerUser(email);
      if (!registered) {
        // Handle registration error
        throw Exception("User registration failed");
      }

      // Fetch again after registering
      final newUserDetails = await fetchUserDetail();
      if (newUserDetails == null) {
        throw Exception("User detail fetch failed after registration");
      }

      debugPrint("🐚 New User Details: $newUserDetails");

      //since it is new user we are creating a new client for them
      await registerClient(
          newUserDetails.userId.toString(), newUserDetails.emailId);

      // Save to credentials
      return newUserDetails;

      // ... Save anything else if needed
    }
    // Save existing user info
    debugPrint("🐚 User Details: $userDetails");
    return userDetails;
  }

  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    try {
      await _auth0.webAuthentication(scheme: 'phoenixscheme').logout();
      await prefs.remove('credentials'); // Clear from SharedPreferences
      await prefs.remove('access_token');
    } catch (e) {
      throw LogoutFailedException('Logout failed: ${e.toString()}');
    }
    debugPrint("Logout successful");
  }

  Future<void> _storeCredentials(CredentialsModel credentialsModel) async {
    final prefs = await SharedPreferences.getInstance();
    final credentialsJson = jsonEncode(credentialsModel.toJson());
    await prefs.setString('credentials', credentialsJson);
    await prefs.setString('access_token', credentialsModel.accessToken);
    debugPrint("Credentials & accessToken are stored in SharedPreferences");
  }

  /// Storing access token to SP
  Future<void> _storeAccessToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    final updatedToken = await _refreshAccessTokenIfNeeded(token);
    await prefs.setString('access_token', updatedToken);
    debugPrint("Stored updated access token in SharedPreferences");
  }

  /// Fetch the latest access token if it's expired or refreshed
  Future<String> _refreshAccessTokenIfNeeded(String oldToken) async {
    debugPrint("☑️ Checking access token");

    if (!isTokenExpired(oldToken)) {
      debugPrint("✅ Access token is still valid.");
      return oldToken;
    }

    debugPrint("🔄 Access token is expired or about to expire. Refreshing...");

    try {
      final hasValidCredentials =
          await _auth0.credentialsManager.hasValidCredentials();
      debugPrint(
          "😶‍🌫️ Has valid credentials: ${hasValidCredentials.toString()}");

      if (!hasValidCredentials) {
        debugPrint("❌ No valid credentials. Logging out user...");
        await logout();
        return oldToken;
      }

      final result = await _auth0.credentialsManager.credentials();

      debugPrint("Credentials retrieved successfully: ${result.accessToken}");

      if (result.accessToken != oldToken) {
        debugPrint("✨ Access token updated, storing the new one.");
        // Store the new token securely

        return result.accessToken;
      }
    } catch (e) {
      debugPrint("Error refreshing access token: $e");

      if (e is ApiException) {
        debugPrint("API Error: ${e.statusCode} - ${e.message}");
        if (e.statusCode == 401) {
          debugPrint(
              "Refresh token is invalid or expired. Logging out user...");
          await logout();
        }
      } else if (e is SocketException) {
        debugPrint("Network error: ${e.message}");
      } else {
        debugPrint("Unexpected error: $e");
      }
    }

    return oldToken; // Return the old token if there's no update
  }

  /// Fetch client info and update the stored credentials when client changes.
  /// This will be hit when user changes the client from the dropdown in account screen
  Future<CredentialsModel?> updateClientInfo(int newClientId) async {
    debugPrint("🚀 Updating the client info for $newClientId");
    try {
      final prefs = await SharedPreferences.getInstance();
      final credentialsJson = prefs.getString('credentials');
      if (credentialsJson == null) return null;

      var credentialsModel = CredentialsModel.fromJson(
        Map<String, dynamic>.from(jsonDecode(credentialsJson)),
      );

      // Fetch updated broker info for the new client ID
      final clientInfo = await _fetchClientInfo(newClientId);

      final brokers = (clientInfo['broker_infos'] as List<dynamic>)
            .map((broker) => BrokerInfo.fromJson(broker))
            .toList();
      final clientName = clientInfo['client_name'] as String;

      // Update the credentials model
      credentialsModel =
          credentialsModel.copyWith(clientId: newClientId, brokers: brokers, clientName: clientName );

      // Store updated credentials in SharedPreferences
      await _storeCredentials(credentialsModel);
      return credentialsModel;
    } catch (e) {
      debugPrint("Error updating client info: $e");
      return null;
    }
  }

  bool isTokenExpired(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        return true; // Invalid token format
      }

      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final payloadMap = jsonDecode(decoded);

      final expiration = payloadMap['exp'] as int?;
      if (expiration == null) {
        return true; // No expiration claim
      }

      final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      return currentTime >= expiration;
    } catch (e) {
      return true; // Assume expired if there's an error
    }
  }

  Future<String> refreshAccessToken(String token) =>
      _refreshAccessTokenIfNeeded(token);
}
