import 'package:bloc/bloc.dart';
import 'package:flutter/widgets.dart';
import 'package:phoenix/features/pnl/data/pnl_data_provider.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';

part 'pnl_event.dart';
part 'pnl_state.dart';

class PnlBloc extends Bloc<PnlEvent, PnlState> {
  final PnlDataProvider _pnlDataProvider;

  PnlBloc(this._pnlDataProvider) : super(PnlInitial()) {
    on<FetchPnlData>(_onFetchPnlData);
  }

  void _onFetchPnlData(FetchPnlData event, Emitter<PnlState> emit) async {
    emit(PnlLoading());
    try {
      final pnlData = await _pnlDataProvider.fetchPnlData(event.clientId);
      //final pnlData = await _pnlDataProvider.fetchMockPnlDataFromFile();
      emit(PnlLoaded(pnlData));
    } catch (e) {
      emit(PnlError(e.toString()));
    }
  }


}
