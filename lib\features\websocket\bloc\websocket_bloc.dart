import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:phoenix/features/websocket/data/provider/web_socket_proto_provider.dart';
import 'package:phoenix/features/websocket/model/proto/price_proto/price.pb.dart';
import 'package:phoenix/utils/websocket_constants.dart';

part 'websocket_event.dart';
part 'websocket_state.dart';

class WebSocketBloc extends Bloc<WebSocketEvent, WebSocketState> {
  final WebSocketProtoProvider _protoProvider;
  StreamSubscription<List<Price>>? _subscription;
  StreamSubscription<bool>? _connectionSubscription;

  List<Price> _tickList = []; // Store entire tick list
  String? _selectedStockId; // Store selected stock ID

  List<int> _selectedStockIds = []; // Track selected stock IDs
  Map<int, double> _previousPrices = {}; // Track previous prices

  WebSocketBloc(this._protoProvider) : super(WebSocketInitial()) {
    on<WebSocketConnect>(_onConnect);
    on<WebSocketDisconnect>(_onDisconnect);
    on<WebSocketSendMessage>(_onSendMessage);
    on<WebSocketReceivedData>(_onDataReceived);
    on<WebSocketSelectStock>(_onSelectStock);
    on<WebSocketSelectMultipleStocks>(_onSelectMultipleStocks);
    on<WebSocketClearSelectedStocks>(_onClearSelectedStocks);
    on<WebSocketDisconnected>(
        _onDisconnected); // Handle unexpected disconnections
  }

  /// Handles WebSocket connection.
  Future<void> _onConnect(
      WebSocketConnect event, Emitter<WebSocketState> emit) async {
    if (state is WebSocketConnected || state is WebSocketConnecting) return;
    print("⚡ Connecting...");
    emit(WebSocketConnecting());

    try {
      final url = WebSocketConstants.protoWSUrl;
      final headers = {'Authorization': 'Bearer ${event.accessToken}'};

      _protoProvider.connect(url, headers: headers);

      _subscription?.cancel();
      _subscription = _protoProvider.stream.listen(
        (tickList) {
          _tickList = tickList; // Store the entire tick list
          add(WebSocketReceivedData(_tickList)); // Emit full tick list
        },
        onError: (error) {
          add(WebSocketErrorEvent('Stream error: ${error.toString()}'));
        },
      );

      // Listen to connection status
      _connectionSubscription?.cancel();
      _connectionSubscription = _protoProvider.connectionStatusStream.listen(
        (isConnected) {
          if (!isConnected) {
            add(WebSocketDisconnected()); // Trigger disconnect event when WebSocket dies
          }
        },
      );

      emit(WebSocketConnected(_tickList));
    } catch (e) {
      emit(WebSocketError('Connection failed: ${e.toString()}'));
    }
  }

  /// Handles stock selection - finds the latest price of the selected stock.
  /// Handles stock selection - finds the latest price of the selected stock.
  Future<void> _onSelectStock(
      WebSocketSelectStock event, Emitter<WebSocketState> emit) async {
    if (event.stockId == null || event.stockId!.isEmpty) {
      _selectedStockId = null; // Reset the selected stock
      emit(WebSocketStockPriceUpdated(0)); // Emit reset price (optional)
      return; // Do not emit anything else
    }

    _selectedStockId = event.stockId;

    final selectedStock = _tickList.firstWhere(
      (tick) => tick.zenId.value.toString() == _selectedStockId,
      orElse: () => Price(),
    );

    if (selectedStock.price.value != 0) {
      debugPrint(
          "🐸 Bloc emitting for particular tick - $_selectedStockId - price ${selectedStock.price.value}");
      emit(WebSocketStockPriceUpdated(selectedStock.price.value));
    }
  }

  /// Handles incoming tick data and updates state.
  Future<void> _onDataReceived(
      WebSocketReceivedData event, Emitter<WebSocketState> emit) async {
    _tickList = event.data; // Update the stored tick list
    debugPrint("😎 Bloc emitting all ticks");
    emit(WebSocketDataReceived(_tickList));

    /// If a stock is selected, update its price in UI
    if (_selectedStockId != null) {
      debugPrint("😎 Bloc emitting for only one selected tick");
      add(WebSocketSelectStock(_selectedStockId!));
    }

    /// Update multiple stocks if selected
    if (_selectedStockIds.isNotEmpty) {
      debugPrint("😎 Bloc emitting for set of ${_selectedStockIds.length} ticks");
      add(WebSocketSelectMultipleStocks(_selectedStockIds));
    }
  }

  /// Disconnects WebSocket.
  Future<void> _onDisconnect(
      WebSocketDisconnect event, Emitter<WebSocketState> emit) async {
    await _subscription?.cancel();
    _protoProvider.disconnect();
    _tickList.clear();
    emit(WebSocketInitial());
  }

  /// Handles unexpected disconnection.
  Future<void> _onDisconnected(
      WebSocketDisconnected event, Emitter<WebSocketState> emit) async {
    debugPrint("⚠️ WebSocket unexpectedly disconnected!");
    _tickList.clear();
    emit(WebSocketDisconnectedState(isConnected: false));
  }

  /// Sends a message over WebSocket.
  Future<void> _onSendMessage(
      WebSocketSendMessage event, Emitter<WebSocketState> emit) async {
    try {
      _protoProvider.sendPing(event.message);
    } catch (e) {
      emit(WebSocketError('Error sending message: ${e.toString()}'));
    }
  }

  //positions //orders page
  // Future<void> _onSelectMultipleStocks(
  //     WebSocketSelectMultipleStocks event, Emitter<WebSocketState> emit) async {
  //   if (event.stockIds.isEmpty) {
  //     _selectedStockIds.clear();
  //     emit(WebSocketMultipleStockPricesUpdated({})); // Emit empty state
  //     return;
  //   }


  //   _selectedStockIds = event.stockIds;

  //   Map<int, PriceUpdate> stockPriceUpdates = {};

  //   for (int stockId in _selectedStockIds) {
  //     final selectedStock = _tickList.firstWhere(
  //       (tick) => tick.zenId.value.toString() == stockId.toString(),
  //       orElse: () => Price(),
  //     );
  //     //debugPrint("👺 ${selectedStock.toString()}");

  //     double newPrice = selectedStock.hasPrice()
  //         ? selectedStock.price.value
  //         : (_previousPrices[stockId] ?? 0.0);
  //     double previousPrice = _previousPrices[stockId] ?? 0.0;

  //     //if (newPrice != previousPrice) {
  //     stockPriceUpdates[stockId] = PriceUpdate(previousPrice, newPrice);
  //     _previousPrices[stockId] = newPrice; // Update stored price
  //     // }
  //   }

  //   debugPrint("⚡stock price updates ${stockPriceUpdates.length.toString()}");

  //   if (stockPriceUpdates.isNotEmpty) {
  //     emit(WebSocketMultipleStockPricesUpdated(stockPriceUpdates));
  //   }
  // }

  Future<void> _onSelectMultipleStocks(
    WebSocketSelectMultipleStocks event, Emitter<WebSocketState> emit) async {
  if (event.stockIds.isEmpty) {
    _selectedStockIds.clear();
    emit(WebSocketMultipleStockPricesUpdated({})); // Emit empty state
    return;
  }

  _selectedStockIds = event.stockIds;

  Map<int, double> stockPriceUpdates = {};

  for (int stockId in _selectedStockIds) {
    final selectedStock = _tickList.firstWhere(
      (tick) => tick.zenId.value.toString() == stockId.toString(),
      orElse: () => Price(),
    );

    double latestPrice = selectedStock.hasPrice() ? selectedStock.price.value : 0.0;
    stockPriceUpdates[stockId] = latestPrice;
  }

  debugPrint("⚡ Emitting stock prices for ${stockPriceUpdates.length} stocks");

  if (stockPriceUpdates.isNotEmpty) {
    emit(WebSocketMultipleStockPricesUpdated(stockPriceUpdates));
  }
}


  Future<void> _onClearSelectedStocks(
      WebSocketClearSelectedStocks event, Emitter<WebSocketState> emit) async {
    _selectedStockIds.clear();
    _previousPrices.clear(); // Optional: reset previous prices
  }

  @override
  Future<void> close() async {
    await _subscription?.cancel();
    // await _connectionSubscription?.cancel();
    _protoProvider.disconnect();
    return super.close();
  }
}
