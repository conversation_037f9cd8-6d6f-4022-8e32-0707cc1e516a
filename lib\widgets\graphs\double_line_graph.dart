import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/util_functions.dart';

class DoubleLineGraph extends StatelessWidget {
  final DateTime baseDate;
  final List<FlSpot> lineOnePoints;
  final List<FlSpot> lineTwoPoints;
  final Color lineOneColor;
  final Color lineTwoColor;

  const DoubleLineGraph({
    super.key,
    required this.baseDate,
    required this.lineOnePoints,
    required this.lineTwoPoints,
    required this.lineOneColor,
    required this.lineTwoColor,
  });

  @override
  Widget build(BuildContext context) {
    final lineOneMinY =
        lineOnePoints.fold(0.0, (min, spot) => spot.y < min ? spot.y : min);

    final lineTwoMinY =
        lineTwoPoints.fold(0.0, (min, spot) => spot.y < min ? spot.y : min);

    final minY = lineOneMinY < lineTwoMinY ? lineOneMinY : lineTwoMinY;

    final lineOneMaxY =
        lineOnePoints.fold(0.0, (max, spot) => spot.y > max ? spot.y : max);

    final lineTwoMaxY =
        lineTwoPoints.fold(0.0, (max, spot) => spot.y > max ? spot.y : max);

    final maxY = lineOneMaxY > lineTwoMaxY ? lineOneMaxY : lineTwoMaxY;

    return LineChart(
      curve: Curves.bounceInOut,
      LineChartData(
        gridData: FlGridData(show: false),
        titlesData: FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            fitInsideHorizontally: true,
            tooltipBgColor: Color(0xffF1EFEC),
            tooltipRoundedRadius: 6,
            getTooltipItems: (List<LineBarSpot> touchedSpots) {
              if (touchedSpots.isEmpty) return [];
              // Assume the date is based on the first spot's x value
              final DateTime date =
                  baseDate.add(Duration(days: touchedSpots.first.x.toInt()));
              // Extract values
              double? realizedY;
              double? unrealizedY;
              for (var spot in touchedSpots) {
                if (spot.barIndex == 0) {
                  realizedY = spot.y;
                } else if (spot.barIndex == 1) {
                  unrealizedY = spot.y;
                }
              }
              return touchedSpots.asMap().entries.map((entry) {
                final index = entry.key;
                // Only show tooltip for the first spot
                if (index == 0) {
                  return LineTooltipItem(
                    "R: ₹${UtilFunctions.formatIndianCurrencyforPnlGraph(realizedY ?? 0)}\n"
                    "UR: ₹${UtilFunctions.formatIndianCurrencyforPnlGraph(unrealizedY ?? 0)}\n",
                    const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                      fontSize: 13,
                    ),
                    children: [
                      TextSpan(
                        text: DateFormat('dd MMM yyyy').format(date),
                        style: const TextStyle(
                          color: Color.fromARGB(255, 134, 134, 134),
                          fontWeight: FontWeight.w400,
                          fontSize: 11,
                        ),
                      ),
                    ],
                  );
                }
              }).toList();
            },
          ),
          //this is to remove the vertical bar on the chart on hover
          getTouchedSpotIndicator: (barData, spotIndexes) {
            return spotIndexes.map((index) {
              return TouchedSpotIndicatorData(
                FlLine(
                  color: Colors.transparent, // Make the line invisible
                  strokeWidth: 0, // Ensure it takes no space
                ),
                FlDotData(
                  show: true, // Keep the circle visible
                ),
              );
              // Returning null here disables the vertical line and the dot on top
            }).toList();
          },
        ),
        lineBarsData: [
          LineChartBarData(
            preventCurveOverShooting: true,
            spots: lineOnePoints,
            isCurved: true,
            color: ThemeConstants.netWorthHeaderColor,
            barWidth: 1.8,
            isStrokeCapRound: true,
            curveSmoothness: 0.35,
            dotData: FlDotData(show: false),
            //this is to remove the area below the line
            belowBarData: BarAreaData(
              show: true,
              color: ThemeConstants.blue.withOpacity(0.1),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  ThemeConstants.netWorthHeaderColor.withOpacity(0.3),
                  Colors.transparent,
                ],
              ),
            ),
          ),
          LineChartBarData(
            preventCurveOverShooting: true,
            spots: lineTwoPoints,
            isCurved: true,
            color: const Color(0xFF4A90E2),
            barWidth: 1.8,
            isStrokeCapRound: true,
            curveSmoothness: 0.35,
            dotData: FlDotData(show: false),
            //this is to remove the area below the line
            belowBarData: BarAreaData(
              show: true,
              color: ThemeConstants.blue.withOpacity(0.1),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  ThemeConstants.blue.withOpacity(0.3),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ],
        minX: 0,
        maxX: lineOnePoints.length.toDouble() - 1,
        minY: minY,
        maxY: maxY,
      ),
    );
  }
}
