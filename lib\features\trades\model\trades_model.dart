class TradesModel {
  final int id;
  final int clientId;
  final int accountId;
  final int strategyId;
  final String broker;
  final String symbol;
  final String side;
  final int quantity;
  final double price;
  final double value;
  final DateTime timestamp;
  final String status;
  final String exchange;
  final int zenId;

  TradesModel({
    required this.id,
    required this.clientId,
    required this.accountId,
    required this.strategyId,
    required this.broker,
    required this.symbol,
    required this.side,
    required this.quantity,
    required this.price,
    required this.value,
    required this.timestamp,
    required this.status,
    required this.exchange,
    required this.zenId,
  });

  factory TradesModel.fromJson(Map<String, dynamic> json) {
    return TradesModel(
      id: json['zen_order_id'] ?? 0,
      zenId: json['position_composite_key']['zen_id'] ?? 0,
      clientId: json['position_composite_key']['client_id'] ?? 0,
      accountId: json['position_composite_key']['account_id'] ?? 0,
      strategyId: json['position_composite_key']['strategy_id'] ?? 0,
      broker: json['position_composite_key']['broker'] ?? '',
      symbol: json['trading_symbol'] ?? '',
      side: json['transaction_type'] ?? '',
      quantity: (json['quantity'] ?? 0),
      price: (json['price'] ?? 0).toDouble(),
      value: (json['value'] ?? 0).toDouble(),
      timestamp: DateTime.parse(json['order_execution_time'] ?? DateTime.now().toIso8601String()),
      status: json['status'] ?? '',
      exchange:json['exchange'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'client_id': clientId,
      'account_id': accountId,
      'strategy_id': strategyId,
      'broker': broker,
      'symbol': symbol,
      'side': side,
      'quantity': quantity,
      'price': price,
      'value': value,
      'timestamp': timestamp.toIso8601String(),
      'status': status,
      'exchange': exchange,
      'zen_id': zenId

    };
  }
}