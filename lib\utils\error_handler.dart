import 'dart:async';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:phoenix/services/logger_service.dart';

/// A global error handler to catch and log unhandled exceptions
class GlobalErrorHandler {
  /// Initialize the global error handler
  static void initialize() {
    // Handle errors in the current isolate
    Isolate.current.addErrorListener(RawReceivePort((pair) {
      final List<dynamic> errorAndStacktrace = pair;
      final error = errorAndStacktrace[0];
      final stackTrace = StackTrace.fromString(errorAndStacktrace[1].toString());
      _handleError(error, stackTrace);
    }).sendPort);

    // Handle errors in the Flutter framework
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterError.presentError(details);
      _handleError(details.exception, details.stack);
    };

    // Handle errors in the Dart async code
    PlatformDispatcher.instance.onError = (error, stack) {
      _handleError(error, stack);
      return true;
    };
  }

  /// Handle and log an error
  static void _handleError(dynamic error, StackTrace? stackTrace) {
    LoggerService().error('Unhandled exception', error, stackTrace);
  }
}

/// Extension method to add error handling to Future
extension FutureExtension<T> on Future<T> {
  /// Catches and logs errors in futures
  Future<T> handleError() {
    return catchError((error, stackTrace) {
      LoggerService().error('Future error', error, stackTrace);
      throw error; // Rethrow to allow other error handlers to process it
    });
  }
}