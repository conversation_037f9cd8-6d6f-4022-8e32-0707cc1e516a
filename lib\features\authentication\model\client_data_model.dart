class ClientDataModel {
  final int clientId;
  final String clientName;
  final List<String> permissions;

  ClientDataModel({
    required this.clientId,
    required this.clientName,
    required this.permissions,
  });

  factory ClientDataModel.fromJson(Map<String, dynamic> json) {
    return ClientDataModel(
      clientId: json['client_id'],
      clientName: json['client_name'],
      permissions: List<String>.from(json['permissions']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'client_id': clientId,
      'client_name': clientName,
      'permissions': permissions,
    };
  }

  @override
  String toString() {
    return 'ClientDataModel(clientId: $clientId, clientName: $clientName, permissions: $permissions)';
  }
}
