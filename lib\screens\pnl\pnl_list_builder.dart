import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/broker_data_map/bloc/broker_data_map_bloc.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/screens/pnl/inner_screen/detailed_pnl_info_page.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/empty_state/empty_container.dart';
import 'package:phoenix/widgets/tile/tile_generic.dart';

class PnlListBuilder extends StatelessWidget {
  final List<dynamic> data;
  final String emptyMessage;
  final Function refresh;
  final String filter;

  const PnlListBuilder({
    required this.data,
    required this.emptyMessage,
    super.key,
    required this.refresh,
    required this.filter,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return Center(
        child: EmptyContainer(
            title: "No Pnl, Start trading",
            message: emptyMessage,
            imagePath: "images/database-positions-no.png"),
      );
    }

    return BlocBuilder<WebSocketBloc, WebSocketState>(
      // Extract multiple stock prices if available
      builder: (context, state) {
        Map<int, double> stockPrices = {};

        if (state is WebSocketMultipleStockPricesUpdated) {
          stockPrices = state.stockPrices;
          //debugPrint(" ✌️ Reciving Stock prices length : ${stockPrices.length.toString()}");
        }

        return RefreshIndicator(
          color: ThemeConstants.blue,
          backgroundColor: ThemeConstants.backgroundColor,
          onRefresh: () async {
            refresh();
          },
          child: ListView.builder(
            clipBehavior: Clip.hardEdge,
            padding:
                const EdgeInsets.only(top: 6, bottom: 38, left: 6, right: 6),
            itemCount: data.length + 1,
            itemBuilder: (context, index) {
              if (index == data.length) {
                // Add a SizedBox at the end
                return const SizedBox(height: 50); // Adjust height as needed
              }
              final item = data[index] as PositionPnL;
              final int zenId = item.positionCompositeKey.zenSecId;

              // // Fetch the latest price for this zenId
              final double? priceUpdate = stockPrices[zenId];

              final brokerDataMapState =
                  context.read<BrokerDataMapBloc>().state;
              if (brokerDataMapState is! BrokerDataMapProcessedState) {
                return Center(child: Text("Something went wrong..."));
              }

              final brokerName = brokerDataMapState
                      .brokerNameToLabelMap[item.positionCompositeKey.broker] ??
                  "N/A";
              final accountName = brokerDataMapState.accountIdToNameMap[
                      item.positionCompositeKey.accountId] ??
                  "N/A";
              final strategyName = brokerDataMapState.strategyIdToNameMap[
                      item.positionCompositeKey.strategyId] ??
                  "N/A";
              final brokerMetaData = BrokerAccountStrategyData(
                    brokerName: brokerName,
                    accountId: item.positionCompositeKey.accountId,
                    strategyId: item.positionCompositeKey.strategyId,
                    accountName: accountName,
                    strategyName: strategyName,
                  );

              return GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PnlDetailPage(
                        data: item,
                        brokerAccountStrategyData: brokerMetaData,

                      ),
                    ),
                  );
                },
                child: TileGeneric(
                  data: item,
                  tileType: "pnl",
                  prices: priceUpdate,
                  filter: filter,
                  brokerAccountStrategyMapData: brokerMetaData,
                ),
              );
            },
          ),
        );
      },
    );
  }
}
