import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/widgets/animated_typing_dots.dart';
import '../../utils/theme_constants.dart';

class TypingIndicator extends StatelessWidget {
  const TypingIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Align(
          alignment: Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 20),
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 18),
            decoration: BoxDecoration(
              color: AppTheme.cardColor(themeState.isDarkMode),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppTheme.borderColor(themeState.isDarkMode),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: themeState.isDarkMode ? Colors.black26 : Colors.grey.withOpacity(0.2),
                  blurRadius: 8,
                  offset: Offset(0, 2)
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 24,
                  child: DefaultTextStyle(
                    style: TextStyle(
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                      fontSize: 18,
                    ),
                    child: AnimatedTypingDots(),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'AI is typing...',
                  style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
