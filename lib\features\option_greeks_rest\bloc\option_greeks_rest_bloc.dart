import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:phoenix/features/option_greeks_rest/data/provider/option_greeks_rest_provider.dart';
import 'package:phoenix/features/option_greeks_rest/model/option_greeks_rest_model.dart';

part 'option_greeks_rest_event.dart';
part 'option_greeks_rest_state.dart';

class OptionGreeksRestBloc extends Bloc<OptionGreeksRestEvent, OptionGreeksRestState> {
  final OptionGreeksRestProvider _provider;
  Timer? _refreshTimer;

  OptionGreeksRestBloc(this._provider) : super(OptionGreeksRestInitial()) {
    on<OptionGreeksRestFetch>(_onFetch);
    on<OptionGreeksRestRefresh>(_onRefresh);
  }

  /// Handles fetching option greeks data from REST API
  Future<void> _onFetch(
    OptionGreeksRestFetch event,
    Emitter<OptionGreeksRestState> emit,
  ) async {
    if (state is OptionGreeksRestLoading) return;

    emit(OptionGreeksRestLoading());

    try {
      debugPrint('🔄 Fetching option greeks from REST API...');
      final response = await _provider.fetchOptionGreeks();
      
      if (response.status == 'SUCCESS' && response.payload.isNotEmpty) {
        debugPrint('✅ Successfully loaded ${response.payload.length} option greeks from REST API');
        emit(OptionGreeksRestLoaded(
          optionGreeks: response.payload,
          lastUpdated: DateTime.now(),
        ));
        
        // Start periodic refresh every 30 seconds
        _startPeriodicRefresh();
      } else {
        emit(OptionGreeksRestError('No option greeks data available'));
      }
    } catch (e) {
      debugPrint('❌ Error fetching option greeks from REST API: $e');
      emit(OptionGreeksRestError('Failed to fetch option greeks: ${e.toString()}'));
    }
  }

  /// Handles refreshing option greeks data
  Future<void> _onRefresh(
    OptionGreeksRestRefresh event,
    Emitter<OptionGreeksRestState> emit,
  ) async {
    try {
      debugPrint('🔄 Refreshing option greeks from REST API...');
      final response = await _provider.fetchOptionGreeks();
      
      if (response.status == 'SUCCESS' && response.payload.isNotEmpty) {
        debugPrint('✅ Successfully refreshed ${response.payload.length} option greeks from REST API');
        emit(OptionGreeksRestLoaded(
          optionGreeks: response.payload,
          lastUpdated: DateTime.now(),
        ));
      } else {
        // Keep current state if refresh fails but we have existing data
        if (state is OptionGreeksRestLoaded) {
          debugPrint('⚠️ Refresh failed, keeping existing data');
        } else {
          emit(OptionGreeksRestError('No option greeks data available'));
        }
      }
    } catch (e) {
      debugPrint('❌ Error refreshing option greeks from REST API: $e');
      // Keep current state if refresh fails but we have existing data
      if (state is! OptionGreeksRestLoaded) {
        emit(OptionGreeksRestError('Failed to refresh option greeks: ${e.toString()}'));
      }
    }
  }

  /// Starts periodic refresh of data every 30 seconds
  void _startPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!isClosed) {
        add(OptionGreeksRestRefresh());
      }
    });
  }

  /// Stops periodic refresh
  void stopPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  @override
  Future<void> close() {
    _refreshTimer?.cancel();
    return super.close();
  }
}