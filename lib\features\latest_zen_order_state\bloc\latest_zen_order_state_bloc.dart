import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/latest_zen_order_state/data/provider/latest_zen_order_state_provider.dart';
import 'package:phoenix/features/latest_zen_order_state/model/latest_order.dart';

part 'latest_zen_order_state_event.dart';
part 'latest_zen_order_state_state.dart';

class LatestZenOrderStateBloc extends Bloc<LatestZenOrderStateEvent, LatestZenOrderStateState> {
  final LatestZenOrderStateProvider _provider;

  LatestZenOrderStateBloc(this._provider) : super(LatestZenOrderStateInitial()) {
    on<LatestZenOrderStateFetchEvent>(_onLatestZenOrderFetchEvent);
  }
  void _onLatestZenOrderFetchEvent(
    LatestZenOrderStateFetchEvent event,
    Emitter<LatestZenOrderStateState> emit,
) async {
  debugPrint("Reached _onLatestZenOrderFetchEvent");
  emit(LatestZenOrderStateLoading());
  
  try {
    final LatestOrder? data = await _provider.fetchLatestOrderState(event.zenOrderId,event.clientId);

    if (data == null) {
      emit(LatestZenOrderStateEmpty()); // Emit a separate state for null data
    } else {
      emit(LatestZenOrderStateLoaded(data: data));
    }
  } catch (e) {
    emit(LatestZenOrderStateError(error: e.toString()));
  }
}

}
