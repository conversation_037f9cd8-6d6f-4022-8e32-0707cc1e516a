import 'package:phoenix/features/pnl_graph/provider/pnl_graph_provider.dart';
import 'package:phoenix/utils/app_exception.dart';

class PnlGraphRepository {
  final PnlGraphProvider _provider;

  PnlGraphRepository(this._provider);

  Future<Map<String, dynamic>> fetchChartPoints({
    required int clientId,
    required String startTimestamp,
    required String endTimestamp,
    required List<int> zenSecIds,
    required List<int> strategyId,
  }) async {
    try {
      final pnlList = await _provider.fetchPnlDataByTimestamp(
        clientId: clientId,
        startTimestamp: startTimestamp,
        endTimestamp: endTimestamp,
        zenSecIds: zenSecIds,
        strategyId: strategyId,
      );

      // Convert to FlSpot list
      return {
        'chartPoints': pnlList,
        'baseDate': pnlList.first.date,
      };
    } catch (e) {
      throw e is AppException ? e : AppException(e.toString());
    }
  }

}
