part of 'pnl_graph_bloc.dart';

@immutable
sealed class PnlGraphEvent {}

class FetchPnlGraphData extends PnlGraphEvent {
  final int clientId;
  final String startTimestamp;
  final String endTimestamp;
  final List<int> zenSecIds;
  final String requestId;
  final List<int> strategyId;

  FetchPnlGraphData({
    required this.clientId,
    required this.startTimestamp,
    required this.endTimestamp,
    required this.zenSecIds,
    required this.requestId,
    required this.strategyId,
  });
}

class FormatPnlGraphData extends PnlGraphEvent {
  final PnLPeriodType type;
  final bool isRealized;
  final bool isUnrealized;

  FormatPnlGraphData({
    required this.type,
    required this.isRealized,
    required this.isUnrealized,
  });
}

class ClearPnlGraphData extends PnlGraphEvent {}
