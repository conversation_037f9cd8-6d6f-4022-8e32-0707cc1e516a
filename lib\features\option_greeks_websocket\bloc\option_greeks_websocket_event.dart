part of 'option_greeks_websocket_bloc.dart';

@immutable
abstract class OptionGreeksWebSocketEvent {
  const OptionGreeksWebSocketEvent();
}

class OptionGreeksWebSocketConnect extends OptionGreeksWebSocketEvent {
  final String accessToken;
  const OptionGreeksWebSocketConnect(this.accessToken);
}

class OptionGreeksWebSocketDisconnect extends OptionGreeksWebSocketEvent {}

class OptionGreeksWebSocketSendMessage extends OptionGreeksWebSocketEvent {
  final String message;
  const OptionGreeksWebSocketSendMessage(this.message);
}

class OptionGreeksWebSocketReceivedData extends OptionGreeksWebSocketEvent {
  final List<ZenOptionGreeks> data;
  const OptionGreeksWebSocketReceivedData(this.data);
}

class OptionGreeksWebSocketSelectUnderlying extends OptionGreeksWebSocketEvent {
  final String? underlyingId;
  const OptionGreeksWebSocketSelectUnderlying(this.underlyingId);
}

class OptionGreeksWebSocketErrorEvent extends OptionGreeksWebSocketEvent {
  final String error;
  const OptionGreeksWebSocketErrorEvent(this.error);
}

class OptionGreeksWebSocketDisconnected extends OptionGreeksWebSocketEvent {}