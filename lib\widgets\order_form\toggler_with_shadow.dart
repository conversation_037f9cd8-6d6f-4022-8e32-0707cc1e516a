import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class TogglerWithShadow extends StatelessWidget {
  final bool isEQ;
  final String title1;
  final String title2;
  final void Function() action1;
  final void Function() action2;

  const TogglerWithShadow({
    super.key,
    required this.title1,
    required this.title2,
    required this.isEQ,
    required this.action1,
    required this.action2,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 8),
          height: 40,
          width: MediaQuery.of(context).size.width * 0.4,
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppTheme.borderColor(themeState.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: themeState.isDarkMode 
                    ? Colors.black26 
                    : Colors.grey.withOpacity(0.2),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: _buildToggleItem(
                  text: title1,
                  isSelected: isEQ,
                  onTap: () => {action1()},
                  context: context,
                  themeState: themeState,
                ),
              ),
              Expanded(
                child: _buildToggleItem(
                  text: title2,
                  isSelected: !isEQ,
                  onTap: () => {action2()},
                  context: context,
                  themeState: themeState,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildToggleItem({
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
    required BuildContext context,
    required ThemeState themeState,
  }) {
    return SizedBox(
      height: 30,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        child: Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isSelected 
                ? AppTheme.primaryColor(themeState.isDarkMode)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            border: isSelected 
                ? Border.all(
                    color: AppTheme.primaryColor(themeState.isDarkMode),
                    width: 1,
                  )
                : null,
          ),
          child: Text(
            text,
            style: TextStyle(
              color: isSelected 
                  ? Colors.white 
                  : AppTheme.textPrimary(themeState.isDarkMode),
              fontWeight: FontWeight.w600,
              fontSize: 20,
            ),
          ),
        ),
      ),
    );
  }
}
