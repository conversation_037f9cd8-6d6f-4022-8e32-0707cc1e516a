import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/screens/settings/widgets/price_stream_indicator.dart';
import 'package:phoenix/screens/settings/widgets/report_error_widget.dart';
import 'package:phoenix/screens/settings/widgets/theme_toggle.dart';
import 'package:phoenix/services/logger_service.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/widgets/circular_loader.dart';

import 'widgets/biometric_toggle.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthUnauthenticated) {
              Navigator.pushReplacementNamed(context, '/');
            }
          },
          builder: (context, state) {
            if (state is! AuthAuthenticated) {
              return Scaffold(
                backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
                body: const Center(child: CircularLoader()),
              );
            }

            return Scaffold(
              backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
              appBar: AppBar(
                backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
                title: Row(
                  children: [
                    Text(
                      "Settings",
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                iconTheme: IconThemeData(
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                ),
              ),
              body: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 18),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Theme Toggle - Added first for better UX
                        ThemeToggle(),
                        SizedBox(height: 12),
                        PriceStreamIndicator(),
                        SizedBox(height: 8),
                        BiometricToggle(),
                        ReportErrorWidget(),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
