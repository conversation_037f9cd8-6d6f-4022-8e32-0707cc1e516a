//
//  Generated code. Do not modify.
//  source: order_info.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use orderInfoDescriptor instead')
const OrderInfo$json = {
  '1': 'OrderInfo',
  '2': [
    {'1': 'quantity', '3': 1, '4': 1, '5': 11, '6': '.google.protobuf.Int32Value', '10': 'quantity'},
    {'1': 'price', '3': 2, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'price'},
    {'1': 'orders', '3': 3, '4': 1, '5': 11, '6': '.google.protobuf.Int32Value', '10': 'orders'},
  ],
};

/// Descriptor for `OrderInfo`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List orderInfoDescriptor = $convert.base64Decode(
    'CglPcmRlckluZm8SNwoIcXVhbnRpdHkYASABKAsyGy5nb29nbGUucHJvdG9idWYuSW50MzJWYW'
    'x1ZVIIcXVhbnRpdHkSMgoFcHJpY2UYAiABKAsyHC5nb29nbGUucHJvdG9idWYuRG91YmxlVmFs'
    'dWVSBXByaWNlEjMKBm9yZGVycxgDIAEoCzIbLmdvb2dsZS5wcm90b2J1Zi5JbnQzMlZhbHVlUg'
    'ZvcmRlcnM=');

