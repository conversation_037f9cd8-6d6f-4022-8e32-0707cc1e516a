//
//  Generated code. Do not modify.
//  source: depth.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use depthDescriptor instead')
const Depth$json = {
  '1': 'Depth',
  '2': [
    {'1': 'buy', '3': 1, '4': 3, '5': 11, '6': '.websocket.OrderInfo', '10': 'buy'},
    {'1': 'sell', '3': 2, '4': 3, '5': 11, '6': '.websocket.OrderInfo', '10': 'sell'},
  ],
};

/// Descriptor for `Depth`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List depthDescriptor = $convert.base64Decode(
    'CgVEZXB0aBImCgNidXkYASADKAsyFC53ZWJzb2NrZXQuT3JkZXJJbmZvUgNidXkSKAoEc2VsbB'
    'gCIAMoCzIULndlYnNvY2tldC5PcmRlckluZm9SBHNlbGw=');

