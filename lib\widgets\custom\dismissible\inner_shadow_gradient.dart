import 'package:flutter/material.dart';

class GradientWithBoxInnerShadow extends StatelessWidget {
  const GradientWithBoxInnerShadow({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main Gradient Background
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF338AFF),
                Color(0xFF24272C),
              ],
              stops: [0.29, 0.61],
            ),
          ),
        ),
        // Inner Shadow Layer
        Positioned.fill(
          child: DecoratedBox(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.transparent), // needed to maintain layout
              boxShadow: [
                BoxShadow(
                  color: const Color.fromARGB(255, 0, 0, 0),
                  offset: const Offset(0, 0),
                  blurRadius: 250,
                  spreadRadius: 100,
                ),
              ],
            ),
            child: ClipPath(
              clipper: _InnerShadowClipper(),
              child: Container(color: const Color.fromARGB(122, 140, 190, 255)),
            ),
          ),
        ),
      ],
    );
  }
}

class _InnerShadowClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    const inset = 1.0; // small inset to make shadow appear inside
    return Path()
      ..addRect(Rect.fromLTWH(
        inset,
        inset,
        size.width - 2 * inset,
        size.height - 2 * inset,
      ));
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
