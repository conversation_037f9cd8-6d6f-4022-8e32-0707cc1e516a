import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class AppTheme {
  // Dark Theme Colors
  static const Color _darkBackgroundColor = Color(0xff24272C);
  static const Color _darkPrimaryColor = Color(0xff338AFF);
  static const Color _darkSurfaceColor = Color(0xff2D3035);
  static const Color _darkTextPrimary = Color(0xffCDCDCD);
  static const Color _darkTextSecondary = Color(0xffA2A2A2);
  static const Color _darkCardColor = Color(0xff353535);
  static const Color _darkBorderColor = Color.fromARGB(255, 46, 49, 53);
  
  // Light Theme Colors
  static const Color _lightBackgroundColor = Color(0xffF8F9FA);
  static const Color _lightPrimaryColor = Color(0xff338AFF);
  static const Color _lightSurfaceColor = Color(0xffFFFFFF);
  static const Color _lightTextPrimary = Color(0xff2C3E50);
  static const Color _lightTextSecondary = Color(0xff6C757D);
  static const Color _lightCardColor = Color(0xffF8F9FA);
  static const Color _lightBorderColor = Color(0xffE9ECEF);

  // Dark Theme Data
  static final ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    primaryColor: _darkPrimaryColor,
    scaffoldBackgroundColor: _darkBackgroundColor,
    cardColor: _darkCardColor,
    dividerColor: _darkBorderColor,
    appBarTheme: const AppBarTheme(
      backgroundColor: _darkBackgroundColor,
      foregroundColor: _darkTextPrimary,
      elevation: 0,
      iconTheme: IconThemeData(color: _darkTextPrimary),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return _darkPrimaryColor;
        }
        return _darkTextSecondary;
      }),
      trackColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return _darkPrimaryColor.withOpacity(0.5);
        }
        return _darkTextSecondary.withOpacity(0.3);
      }),
    ),
    textTheme: const TextTheme(
      bodyLarge: TextStyle(color: _darkTextPrimary),
      bodyMedium: TextStyle(color: _darkTextPrimary),
      bodySmall: TextStyle(color: _darkTextSecondary),
      headlineLarge: TextStyle(color: _darkTextPrimary),
      headlineMedium: TextStyle(color: _darkTextPrimary),
      headlineSmall: TextStyle(color: _darkTextPrimary),
      titleLarge: TextStyle(color: _darkTextPrimary),
      titleMedium: TextStyle(color: _darkTextPrimary),
      titleSmall: TextStyle(color: _darkTextPrimary),
    ),
    colorScheme: const ColorScheme.dark(
      primary: _darkPrimaryColor,
      surface: _darkSurfaceColor,
      background: _darkBackgroundColor,
      onPrimary: Colors.white,
      onSurface: _darkTextPrimary,
      onBackground: _darkTextPrimary,
    ),
  );

  // Light Theme Data
  static final ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    primaryColor: _lightPrimaryColor,
    scaffoldBackgroundColor: _lightBackgroundColor,
    cardColor: _lightCardColor,
    dividerColor: _lightBorderColor,
    appBarTheme: const AppBarTheme(
      backgroundColor: _lightBackgroundColor,
      foregroundColor: _lightTextPrimary,
      elevation: 0,
      iconTheme: IconThemeData(color: _lightTextPrimary),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return _lightPrimaryColor;
        }
        return _lightTextSecondary;
      }),
      trackColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return _lightPrimaryColor.withOpacity(0.5);
        }
        return _lightTextSecondary.withOpacity(0.3);
      }),
    ),
    textTheme: const TextTheme(
      bodyLarge: TextStyle(color: _lightTextPrimary),
      bodyMedium: TextStyle(color: _lightTextPrimary),
      bodySmall: TextStyle(color: _lightTextSecondary),
      headlineLarge: TextStyle(color: _lightTextPrimary),
      headlineMedium: TextStyle(color: _lightTextPrimary),
      headlineSmall: TextStyle(color: _lightTextPrimary),
      titleLarge: TextStyle(color: _lightTextPrimary),
      titleMedium: TextStyle(color: _lightTextPrimary),
      titleSmall: TextStyle(color: _lightTextPrimary),
    ),
    colorScheme: const ColorScheme.light(
      primary: _lightPrimaryColor,
      surface: _lightSurfaceColor,
      background: _lightBackgroundColor,
      onPrimary: Colors.white,
      onSurface: _lightTextPrimary,
      onBackground: _lightTextPrimary,
    ),
  );

  // Dynamic Theme Constants that change based on current theme
  static Color backgroundColor(bool isDark) => 
      isDark ? _darkBackgroundColor : _lightBackgroundColor;
  
  
  static Color primaryColor(bool isDark) => 
      isDark ? _darkPrimaryColor : _lightPrimaryColor;
  
  static Color surfaceColor(bool isDark) => 
      isDark ? _darkSurfaceColor : _lightSurfaceColor;
  
  static Color textPrimary(bool isDark) => 
      isDark ? _darkTextPrimary : _lightTextPrimary;
  
  static Color textSecondary(bool isDark) => 
      isDark ? _darkTextSecondary : _lightTextSecondary;
  
  static Color cardColor(bool isDark) => 
      isDark ? _darkCardColor : _lightCardColor;
  
  static Color pillBgColor(bool isDark) =>
      isDark ? _darkCardColor : const Color(0xffE9ECEF);

  static Color pillTextColor(bool isDark) =>
      isDark ? _darkTextPrimary : const Color.fromARGB(255, 134, 134, 134);
  
  static Color borderColor(bool isDark) => 
      isDark ? _darkBorderColor : _lightBorderColor;

  // Keep backward compatibility with existing ThemeConstants
  static const Color blue = Color(0xff338AFF);
  static const Color tileGreenColor = Color(0xff00C400);
  static const Color titleRedColor = Color(0xFFD64242);
  static const Color zenGrey = Color(0xffc5c5c5);
  static const Color floatingActionButtonColor = Color(0xff368cff);
}