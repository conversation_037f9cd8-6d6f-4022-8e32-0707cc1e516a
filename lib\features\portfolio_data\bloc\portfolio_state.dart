part of 'portfolio_bloc.dart';

@immutable
sealed class PortfolioState {}

final class PortfolioInitial extends PortfolioState {}

class PortfolioLoading extends PortfolioState {}

class PortfolioLoaded extends PortfolioState {
  final List<PositionsModel> openPositions;
  final List<PositionsModel> closedPositions;
  PortfolioLoaded( this.openPositions,this.closedPositions, );
}


class PortfolioError extends PortfolioState {
  final String error;
  PortfolioError(this.error);
}