import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/portfolio_data/bloc/portfolio_bloc.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/watchlist/bloc/watchist_bloc.dart';
import 'package:phoenix/features/watchlist/model/watchlist_model.dart';
import 'package:phoenix/screens/watchlist/add_security_screen.dart';
import 'package:phoenix/services/sort_service.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/toast/toast_utils.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import '../../widgets/search/custom_search_bar.dart';
import '../../widgets/search_refresh_sort_bar/search_refresh_sort_bar.dart';
import '../../widgets/tab_bar/dynamic_app_tab_bar.dart';
import '../../widgets/toast/custom_toast.dart';
import '../../features/watchlist/data/watchlist_provider.dart';
import 'widgets/rename_watchlist_dialog.dart';
import 'widgets/selection_action_bar.dart';
import 'widgets/watchlist_tab_content.dart';

class WatchlistScreen extends StatefulWidget {
  const WatchlistScreen({super.key});

  @override
  State<WatchlistScreen> createState() => _WatchlistScreenState();
}

class _WatchlistScreenState extends State<WatchlistScreen>
    with TickerProviderStateMixin {
  TabController? _tabController;
  final SortService _sortService = SortService.instance;
  bool _showSearch = false;
  final TextEditingController _searchController = TextEditingController();
  SortOption? _currentSortOption;
  bool _isAscending = true;

  Map<int, double> _stockPrices = {};
  final currencyFormat = NumberFormat.currency(locale: 'en_IN', symbol: '₹');
  Set<int> _subscribedZenIds = {};

  // Selection state
  final Set<String> _selectedSecurities = <String>{};
  bool get _isSelectionMode => _selectedSecurities.isNotEmpty;

  // Animation controller for floating action button
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize FAB animation controller
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Scale animation with bounce effect - scale in and out
    _fabScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.elasticOut,
    ));

    _loadWatchlistData();

    // Start the scale in animation after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _fabAnimationController.forward();
      }
    });
  }

  void _loadWatchlistData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authState = context.read<AuthBloc>().state;
      if (authState is AuthAuthenticated) {
        // Clear any existing subscriptions
        _subscribedZenIds.clear();
        context
            .read<WatchistBloc>()
            .add(WatchlistData(authState.credentialsModel.clientId));
      }
    });
  }

  @override
  void dispose() {
    _tabController?.dispose();
    _searchController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _animateFAB() {
    _fabAnimationController.reset();
    _fabAnimationController.forward();
  }

  void _setupTabController(List<Watchlist> watchlists) {
    final oldIndex = _tabController?.index ?? 0;
    _tabController?.dispose();
    _tabController = TabController(
      length: watchlists.length,
      vsync: this,
      initialIndex: oldIndex < watchlists.length ? oldIndex : 0,
    );

    // Add listener to clear selection when switching tabs
    _tabController!.addListener(() {
      if (_tabController!.indexIsChanging) {
        _clearSelection();
      }
    });
  }

  void _subscribeToWebsocket(List<Watchlist> watchlists) {
    final zenIds = watchlists
        .expand((watchlist) => watchlist.securities)
        .map((security) => security.zenId)
        .where((id) => id != 0)
        .toSet();

    // Only subscribe if the zen IDs have changed
    if (zenIds.isNotEmpty &&
            !zenIds.every((id) => _subscribedZenIds.contains(id)) ||
        zenIds.length != _subscribedZenIds.length) {
      _subscribedZenIds = zenIds;
      context
          .read<WebSocketBloc>()
          .add(WebSocketSelectMultipleStocks(zenIds.toList()));
    }
  }

  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      if (!_showSearch) {
        _searchController.clear();
      }
    });
  }

  void _showSortOptions() {
    _sortService.showSortOptions(
      context: context,
      currentSortOption: _currentSortOption,
      isAscending: _isAscending,
      onSortChanged: (option, ascending) {
        setState(() {
          _currentSortOption = option;
          _isAscending = ascending;
        });
      },
      availableOptions: [
        SortOption.alphabetical,
        SortOption.lastTradedPrice,
        SortOption.percentChange,
      ],
    );
  }

  List<SecurityModel> _sortSecurities(List<SecurityModel> securities) {
    if (_currentSortOption == null) return securities;

    final watchlistProvider = context.read<WatchlistProvider>();

    securities.sort((a, b) {
      int comparison = 0;

      switch (_currentSortOption!) {
        case SortOption.alphabetical:
          comparison = a.tradingSymbol.compareTo(b.tradingSymbol);
          break;
        case SortOption.lastTradedPrice:
          final priceA = _stockPrices[a.zenId] ?? 0.0;
          final priceB = _stockPrices[b.zenId] ?? 0.0;
          comparison = priceA.compareTo(priceB);
          break;
        case SortOption.percentChange:
          final priceA = _stockPrices[a.zenId] ?? 0.0;
          final priceB = _stockPrices[b.zenId] ?? 0.0;
          final sodPriceA = watchlistProvider.sodPrices[a.zenId] ?? 0.0;
          final sodPriceB = watchlistProvider.sodPrices[b.zenId] ?? 0.0;
          final changeA =
              sodPriceA != 0 ? ((priceA - sodPriceA) / sodPriceA) * 100 : 0.0;
          final changeB =
              sodPriceB != 0 ? ((priceB - sodPriceB) / sodPriceB) * 100 : 0.0;
          comparison = changeA.compareTo(changeB);
          break;
        default:
          comparison = a.tradingSymbol.compareTo(b.tradingSymbol);
      }

      return _isAscending ? comparison : -comparison;
    });
    return securities;
  }

  void _refreshWatchlist() {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      // Clear subscribed zen IDs to allow re-subscription
      _subscribedZenIds.clear();
      context
          .read<WatchistBloc>()
          .add(WatchlistData(authState.credentialsModel.clientId));
    }
  }

  void _handleSecurityTap(SecurityModel security) {
    setState(() {
      if (_selectedSecurities.contains(security.tradingSymbol)) {
        _selectedSecurities.remove(security.tradingSymbol);
      } else {
        _selectedSecurities.add(security.tradingSymbol);
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedSecurities.clear();
    });
  }

  Future<void> _deleteSelectedSecurities() async {
    if (_selectedSecurities.isEmpty) return;

    final watchlists =
        (context.read<WatchistBloc>().state as WatchlistLoaded).watchlistData;
    final currentWatchlist = watchlists[_tabController!.index];

    final securitiesToDelete = currentWatchlist.securities
        .where(
            (security) => _selectedSecurities.contains(security.tradingSymbol))
        .toList();

    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      final watchlistProvider = context.read<WatchlistProvider>();

      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: ThemeConstants.backgroundColor,
          title: const Text(
            'Delete Securities',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Are you sure you want to delete ${_selectedSecurities.length} selected ${_selectedSecurities.length == 1 ? 'security' : 'securities'}?',
            style: const TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child:
                  const Text('Cancel', style: TextStyle(color: Colors.white70)),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: ThemeConstants.titleRedColor,
              ),
              child: const Text('Delete'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        try {
          // Delete securities one by one
          for (final security in securitiesToDelete) {
            await watchlistProvider.deleteWatchlists(
              authState.credentialsModel.clientId.toString(),
              _tabController!.index,
              security.tradingSymbol,
            );
          }

          _clearSelection();
          _refreshWatchlist();

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    '${securitiesToDelete.length} ${securitiesToDelete.length == 1 ? 'security' : 'securities'} deleted successfully'),
                backgroundColor: ThemeConstants.tileGreenColor,
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error deleting securities: $e'),
                backgroundColor: ThemeConstants.titleRedColor,
              ),
            );
          }
        }
      }
    }
  }

  void _addStock() {
    if (_tabController == null) return;
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            AddSecurityScreen(watchlistIndex: _tabController!.index),
      ),
    ).then((_) {
      _refreshWatchlist();
      // Animate the FAB again when user returns
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _animateFAB();
        }
      });
    });
  }

  void _showRenameDialog(List<Watchlist> watchlists) {
    if (_tabController == null) return;
    final currentWatchlist = watchlists[_tabController!.index];

    showDialog(
      context: context,
      builder: (context) => RenameWatchlistDialog(
        initialName: currentWatchlist.name,
        onRename: (newName) async {
          final authState = context.read<AuthBloc>().state;
          if (authState is AuthAuthenticated) {
            final watchlistProvider = context.read<WatchlistProvider>();
            await watchlistProvider.renameWatchlist(
              authState.credentialsModel.clientId.toString(),
              _tabController!.index,
              newName,
            );
            _refreshWatchlist();
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          body: MultiBlocListener(
            listeners: [
              BlocListener<WebSocketBloc, WebSocketState>(
                listener: (context, state) {
                  if (state is WebSocketMultipleStockPricesUpdated) {
                    setState(() {
                      _stockPrices = state.stockPrices;
                    });
                  }
                },
              ),
              BlocListener<WatchistBloc, WatchistState>(
                listener: (context, state) {
                  if (state is WatchlistLoaded) {
                    final watchlists = state.watchlistData;
                    // Subscribe to websocket for price updates only when watchlist data changes
                    _subscribeToWebsocket(watchlists);
                  }
                },
              ),
            ],
            child: BlocBuilder<WatchistBloc, WatchistState>(
              builder: (context, state) {
                if (state is WatchlistLoading) {
                  return const Center(child: CircularLoader());
                } else if (state is WatchlistError) {
                  return Center(child: Text('Error: ${state.error}'));
                } else if (state is WatchlistLoaded) {
                  final watchlists = state.watchlistData;

                  if (watchlists.isEmpty) {
                    return const Center(child: Text('No watchlists found.'));
                  }

                  // Initialize TabController when watchlists are loaded
                  if (_tabController == null ||
                      _tabController!.length != watchlists.length) {
                    _setupTabController(watchlists);
                  }

                  return Column(
                    children: [
                      // Selection Action Bar
                      SelectionActionBar(
                        selectedCount: _selectedSecurities.length,
                        onDelete: _deleteSelectedSecurities,
                        onClear: _clearSelection,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            child: DynamicAppTabBar(
                              controller: _tabController!,
                              tabs: watchlists
                                  .map((w) => TabData(
                                      title: w.name,
                                      count: w.securities.length))
                                  .toList(),
                              showCount: false,
                              isDarkMode: themeState.isDarkMode,
                            ),
                          ),
                          if (!_isSelectionMode)
                            IconButton(
                              icon: const Icon(Icons.edit,
                                  color: ThemeConstants.zenWhite, size: 22),
                              onPressed: () => _showRenameDialog(watchlists),
                            ),
                        ],
                      ),
                      if (!_isSelectionMode)
                        SearchRefreshSortBar(
                          refresh: _refreshWatchlist,
                          toggleSearch: _toggleSearch,
                          showSortOptions: _showSortOptions,
                          isAscending: _isAscending,
                          currentSortOption: _currentSortOption,
                        ),
                      if (_showSearch && !_isSelectionMode)
                        CustomSearchBar(
                          controller: _searchController,
                          hintText: 'Search watchlist...',
                          autofocus: true,
                          onSearch: (query) {
                            setState(() {});
                          },
                          onClose: _toggleSearch,
                        ),
                      Expanded(
                        child: BlocBuilder<PortfolioBloc, PortfolioState>(
                          builder: (context, portfolioState) {
                            List<PositionsModel> positions = [];
                            if (portfolioState is PortfolioLoaded) {
                              positions = portfolioState.openPositions;
                            }

                            return TabBarView(
                              controller: _tabController!,
                              children: watchlists.asMap().entries.map((entry) {
                                final index = entry.key;
                                final watchlist = entry.value;
                                return _buildWatchlistTab(
                                    watchlist, positions, index);
                              }).toList(),
                            );
                          },
                        ),
                      ),
                    ],
                  );
                }

                return const Center(child: CircularLoader());
              },
            ),
          ),
          floatingActionButton: Padding(
            padding: const EdgeInsets.only(bottom: 80.0, right: 4),
            child: AnimatedBuilder(
              animation: _fabAnimationController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _fabScaleAnimation.value,
                  child: FloatingActionButton(
                    onPressed: _addStock,
                    backgroundColor:
                        AppTheme.primaryColor(themeState.isDarkMode),
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(Icons.new_label,
                        size: 28, color: Colors.white),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildWatchlistTab(
    Watchlist watchlist,
    List<PositionsModel> positions,
    int watchlistIndex,
  ) {
    final watchlistProvider = context.read<WatchlistProvider>();
    final sodPrices = watchlistProvider.sodPrices;

    List<SecurityModel> filteredSecurities = _searchController.text.isEmpty
        ? watchlist.securities
        : watchlist.securities
            .where((security) => (
              (
              security.tradingSymbol
                    .toLowerCase()
                    .contains(_searchController.text.toLowerCase()) ||
                security.name
                    .toLowerCase()
                    .contains(_searchController.text.toLowerCase())
                  )
                )
              )
            .toList();

    // Apply sorting if user has explicitly chosen a sort option
    if (_currentSortOption != null) {
      filteredSecurities = _sortSecurities(filteredSecurities);
    }

    return WatchlistTabContent(
      watchlist: watchlist,
      filteredSecurities: filteredSecurities,
      stockPrices: _stockPrices,
      sodPrices: sodPrices,
      positions: positions,
      showSearch: _showSearch,
      selectedSecurities: _selectedSecurities,
      onRefresh: _refreshWatchlist,
      onReorder: (oldIndex, newIndex) async {
        if (newIndex > oldIndex) {
          newIndex -= 1;
        }

        final authState = context.read<AuthBloc>().state;
        if (authState is AuthAuthenticated) {
          // Update local state immediately for instant UI feedback
          setState(() {
            _currentSortOption = null;
            final securities = watchlist.securities;
            final security = securities.removeAt(oldIndex);
            securities.insert(newIndex, security);
          });

          // Persist changes in background
          try {
            final watchlistProvider = context.read<WatchlistProvider>();
            await watchlistProvider.updateWatchlistOrder(
              authState.credentialsModel.clientId.toString(),
              watchlistIndex,
              watchlist.securities,
            );
          } catch (e) {
            // If save fails, refresh to restore correct state
            _refreshWatchlist();
            ToastUtil.showToast(
              context,
              'Failed to save order',
              ToastType.error,
            );
          }
        }
      },
      onSecurityTap: _handleSecurityTap,
      onDeleteSecurities: (securities) async {
        // Not used, deletion is handled by selection action bar
      },
    );
  }
}
