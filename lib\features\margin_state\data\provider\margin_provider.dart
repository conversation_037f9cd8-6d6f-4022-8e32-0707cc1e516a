import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:phoenix/features/margin_state/model/margin_data.dart';
import 'package:phoenix/managers/api/api_response_manager.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/app_exception.dart';
import 'package:phoenix/utils/http_service.dart';

class MarginProvider {
  Future<MarginData> fetchMarginData(int clientId, int accountId) async {
    final HttpService httpService = HttpService();
    try {
      final uri = ApiPath.getMargins(clientId, accountId);

      final response = await httpService.get(Uri.parse(uri));

      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if (apiResponse.code != 200 || apiResponse.status != 'SUCCESS' || response.statusCode != 200) {
        throw AppException(apiResponse.message);
      } else {
        final jsonData = apiResponse.payload;

        if (jsonData == null || jsonData.isEmpty) {
          // Return default margin data if response is empty
          return MarginData(
            clientId: clientId,
            brokerName: "",
            accountName: "",
            accountId: accountId,
            availableCash: 0.0,
          );
        }

        return MarginData.fromJson(jsonData);
      }
    } catch (e) {
      debugPrint("Error fetching margin data: $e");
      return MarginData(
        clientId: clientId,
        brokerName: "",
        accountName: "",
        accountId: accountId,
        availableCash: 0.0,
      );
    }
  }
}
