


import 'package:phoenix/features/security_list/model/security_model.dart';

class Watchlist {
  String name;
  List<SecurityModel> securities;

  Watchlist({required this.name, required this.securities});

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'securities': securities.map((s) => s.toJson()).toList(),
    };
  }

  factory Watchlist.fromJson(Map<String, dynamic> json) {
    return Watchlist(
      name: json['name'],
      securities: (json['securities'] as List)
          .map((s) => SecurityModel.fromJson(s))
          .toList(),
    );
  }
}