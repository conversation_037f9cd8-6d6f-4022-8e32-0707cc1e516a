import 'package:flutter/widgets.dart';
import 'package:phoenix/features/orders/data/provider/order_form_provider.dart';
import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/features/orders/model/order_response_model.dart';

class OrderFormRepository {
  final OrderFormProvider provider;

  OrderFormRepository(this.provider);

  Future<OrderResponseModel> placeOrder(OrderFormModel orderData) async {
    try {
      // Use the provider to fetch data for eqHedge
      final responseData = await provider.placeOrder(orderData);
      return OrderResponseModel.fromJson(responseData);
    } catch (e) {
      debugPrint("2. OrderFormRepository chatch and throw in normal order");
      throw Exception(e);
    }
  }

  Future<OrderResponseModelForBucket> placeBucketOrder(
      List<OrderFormModelForBucket> orderData, int clinetId) async {
    try {
      // Use the provider to fetch data for eqHedge
      final responseData = await provider.placeBucketOrder(orderData, clinetId);
      return  OrderResponseModelForBucket.fromJson(responseData);
    } catch (e) {
      debugPrint("OrderFormRepository chatch and throw in bulk order");
      throw Exception(e);
    }
  }
}
