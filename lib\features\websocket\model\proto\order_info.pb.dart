//
//  Generated code. Do not modify.
//  source: order_info.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'google/protobuf/wrappers.pb.dart' as $0;

class OrderInfo extends $pb.GeneratedMessage {
  factory OrderInfo({
    $0.Int32Value? quantity,
    $0.DoubleValue? price,
    $0.Int32Value? orders,
  }) {
    final $result = create();
    if (quantity != null) {
      $result.quantity = quantity;
    }
    if (price != null) {
      $result.price = price;
    }
    if (orders != null) {
      $result.orders = orders;
    }
    return $result;
  }
  OrderInfo._() : super();
  factory OrderInfo.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory OrderInfo.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'OrderInfo', package: const $pb.PackageName(_omitMessageNames ? '' : 'websocket'), createEmptyInstance: create)
    ..aOM<$0.Int32Value>(1, _omitFieldNames ? '' : 'quantity', subBuilder: $0.Int32Value.create)
    ..aOM<$0.DoubleValue>(2, _omitFieldNames ? '' : 'price', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.Int32Value>(3, _omitFieldNames ? '' : 'orders', subBuilder: $0.Int32Value.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  OrderInfo clone() => OrderInfo()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  OrderInfo copyWith(void Function(OrderInfo) updates) => super.copyWith((message) => updates(message as OrderInfo)) as OrderInfo;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static OrderInfo create() => OrderInfo._();
  OrderInfo createEmptyInstance() => create();
  static $pb.PbList<OrderInfo> createRepeated() => $pb.PbList<OrderInfo>();
  @$core.pragma('dart2js:noInline')
  static OrderInfo getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<OrderInfo>(create);
  static OrderInfo? _defaultInstance;

  @$pb.TagNumber(1)
  $0.Int32Value get quantity => $_getN(0);
  @$pb.TagNumber(1)
  set quantity($0.Int32Value v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasQuantity() => $_has(0);
  @$pb.TagNumber(1)
  void clearQuantity() => clearField(1);
  @$pb.TagNumber(1)
  $0.Int32Value ensureQuantity() => $_ensure(0);

  @$pb.TagNumber(2)
  $0.DoubleValue get price => $_getN(1);
  @$pb.TagNumber(2)
  set price($0.DoubleValue v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasPrice() => $_has(1);
  @$pb.TagNumber(2)
  void clearPrice() => clearField(2);
  @$pb.TagNumber(2)
  $0.DoubleValue ensurePrice() => $_ensure(1);

  @$pb.TagNumber(3)
  $0.Int32Value get orders => $_getN(2);
  @$pb.TagNumber(3)
  set orders($0.Int32Value v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrders() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrders() => clearField(3);
  @$pb.TagNumber(3)
  $0.Int32Value ensureOrders() => $_ensure(2);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
