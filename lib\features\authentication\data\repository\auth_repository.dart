import 'package:phoenix/features/authentication/data/data_provider/auth_service.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';

//data formattings happen here
class AuthRepository {

  final AuthService _authService;

  AuthRepository(this._authService);

  Future<CredentialsModel?> initializeAuth() {
    return _authService.getValidCredentials();
  }

  Future<CredentialsModel> login() {
    return _authService.login();
  }

  Future<void> logout() {
    return _authService.logout();
  }

  Future<CredentialsModel?> changeClient(int id){
    return _authService.updateClientInfo(id);
  }

}