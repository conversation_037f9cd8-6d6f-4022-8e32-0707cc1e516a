import 'package:phoenix/features/security_list/data/provider/security_provider.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';

class SecurityRepository {
  final SecurityProvider provider;

  SecurityRepository({required this.provider});

  // Fetch once and return both equities & futures/options
  Future<Map<String, List<SecurityModel>>> getSecurities() async {
    final securityData = await provider.fetchSecurities();

    // Convert EQ section to SecurityModel
    List<SecurityModel> equities = (securityData['equities'] ?? [])
        .map((data) => SecurityModel.fromJson(data))
        .toList();

    // Convert CE, PE, FUT sections to SecurityModel
    List<SecurityModel> futuresOptions = (securityData['futuresOptions'] ?? [])
        .map((data) => SecurityModel.fromJson(data))
        .toList();

    return {
      'equities': equities,
      'futuresOptions': futuresOptions,
    };
  }
}