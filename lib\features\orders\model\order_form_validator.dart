import 'package:phoenix/features/orders/model/latest_order_type_model.dart';
import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/features/orders/model/order_type.dart';

class OrderFormValidator {
  static String validator(OrderFormModel data) {
    List<String> conditions = [
      "isStoplossEnabled",
      "isTrailingStoplossEnabled",
      "isMarket"
    ];

    for (final condition in conditions) {
      if (data[condition]) {
        if (condition == "isMarket") {
          return data.stoplossType == "M"
              ? OrderType.MARKET_ORDER_WITH_SL_MARKET
              : OrderType.MARKET_ORDER_WITH_SL_LIMIT;
        } else if (condition == "isTrailingStoplossEnabled") {
          return data.stoplossType == "M"
              ? OrderType.TRAILING_STOP_LOSS_MARKET_ORDER
              : OrderType.TRAILING_STOP_LOSS_LIMIT_ORDER;
        }
        continue;
      } else {
        if (condition == "isStoplossEnabled") {
          return data.isMarket == true ? OrderType.MARKET : OrderType.LIMIT;
        } else if (condition == "isTrailingStoplossEnabled") {
          continue;
        } else if (condition == "isMarket") {
          return data.stoplossType == "M"
              ? OrderType.LIMIT_ORDER_WITH_SL_MARKET
              : OrderType.LIMIT_ORDER_WITH_SL_LIMIT;
        }
      }
    }

    throw Exception("Order Type validation Error");
  }

  static LatestOrderTypeModel reverseValidator(String orderType) {
  return LatestOrderTypeModel(
    isStoplossEnabled: [
      OrderType.MARKET_ORDER_WITH_SL_MARKET,
      OrderType.MARKET_ORDER_WITH_SL_LIMIT,
      OrderType.LIMIT_ORDER_WITH_SL_MARKET,
      OrderType.LIMIT_ORDER_WITH_SL_LIMIT,
      OrderType.TRAILING_STOP_LOSS_MARKET_ORDER,
      OrderType.TRAILING_STOP_LOSS_LIMIT_ORDER,
    ].contains(orderType),
    
    isTrailingStoplossEnabled: [
      OrderType.TRAILING_STOP_LOSS_MARKET_ORDER,
      OrderType.TRAILING_STOP_LOSS_LIMIT_ORDER,
    ].contains(orderType),
    
    isMarket: [
      OrderType.MARKET,
      OrderType.MARKET_ORDER_WITH_SL_MARKET,
      OrderType.MARKET_ORDER_WITH_SL_LIMIT,
      OrderType.TRAILING_STOP_LOSS_MARKET_ORDER,
    ].contains(orderType),
    
    stoplossType: [
      OrderType.MARKET_ORDER_WITH_SL_MARKET,
      OrderType.TRAILING_STOP_LOSS_MARKET_ORDER,
      OrderType.LIMIT_ORDER_WITH_SL_MARKET,
    ].contains(orderType) 
        ? "M"
        : "L",
  );
}

}

/*
MARKET - isMarket true
LIMIT - isMarket:false,

LIMIT_ORDER_WITH_SL_LIMIT - isStoplossEnabled:true, stopLossType:"L", isMarket:false

MARKET_ORDER_WITH_SL_LIMIT -  isStoplossEnabled:true, stopLossType:"L", isMarket:true

LIMIT_ORDER_WITH_SL_MARKET - isStoplossEnabled:true, stopLossType:"M", isMarket:false

MARKET_ORDER_WITH_SL_MARKET - StoplossEnabled:true, stopLossType:"M", isMarket:true


TRAILING_STOP_LOSS_MARKET_ORDER - isStoplossEnabled:true, isTrailingStoplossEnabled:true, stoplossType:"M"
TRAILING_STOP_LOSS_LIMIT_ORDER -  isStoplossEnabled:true, isTrailingStoplossEnabled:true, stoplossType:"L"


STANDALONE_SL_LIMIT - isStoplossenabled:true, stopLossType:"L",
STANDALONE_SL_MARKET - isStoplossenabled:true, stopLossType:"M",



1. StoplossEnabled - F - isMarket(MARKET,LIMIT) 
                  2 - T - isTrailingStoplossEnabled - T - stoplossType(TRAILING_STOP_LOSS_MARKET_ORDER,TRAILING_STOP_LOSS_LIMIT_ORDER)
                                                  3 - F - isMarket - T -stopLossType(MARKET_ORDER_WITH_SL_LIMIT,MARKET_ORDER_WITH_SL_MARKET)
                                                                  - F -stoplossType(LIMIT_ORDER_WITH_SL_LIMIT,LIMIT_ORDER_WITH_SL_MARKET)


[StoplossEnabled,isTrailingStoplossEnabled,isMarket]


for condition in validator_arr:
  if condition:
    if condtion== isMarket:
      return stopLossType?MARKET_ORDER_WITH_SL_LIMIT:MARKET_ORDER_WITH_SL_MARKET
    else if condtion== isTrailingStoplossEnabled:
        return stoplossType?TRAILING_STOP_LOSS_MARKET_ORDER:TRAILING_STOP_LOSS_LIMIT_ORDER
    continue
  else:
    if (condition==StoplossEnabled)        
      return isMarket?MARKET:Limit
    else if (condition == isTrailingStoplossEnabled)
      continue
    else if (condtion == isMarket)
      return stopLossType?LIMIT_ORDER_WITH_SL_LIMIT:LIMIT_ORDER_WITH_SL_MARKET

*/
