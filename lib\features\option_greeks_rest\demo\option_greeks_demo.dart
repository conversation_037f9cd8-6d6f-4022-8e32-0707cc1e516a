import 'package:flutter/foundation.dart';
import 'package:phoenix/features/option_greeks_rest/data/provider/option_greeks_rest_provider.dart';
import 'package:phoenix/features/option_greeks_rest/utils/option_greeks_converter.dart';

/// Demo class to test the REST API fallback mechanism
class OptionGreeksDemo {
  static final OptionGreeksRestProvider _provider = OptionGreeksRestProvider();

  /// Test the REST API endpoint and conversion
  static Future<void> testRestApiFallback() async {
    try {
      debugPrint('🧪 Testing Option Greeks REST API fallback...');
      
      // Fetch data from REST API
      final response = await _provider.fetchOptionGreeks();
      debugPrint('✅ Successfully fetched ${response.payload.length} option greeks from REST API');
      
      // Convert to WebSocket format
      final convertedData = OptionGreeksConverter.convertRestToWebSocket(response.payload);
      debugPrint('✅ Successfully converted ${convertedData.length} items to WebSocket format');
      
      // Test filtering
      final niftyOptions = OptionGreeksConverter.filterByUnderlying(response.payload, 'NIFTY');
      final bankNiftyOptions = OptionGreeksConverter.filterByUnderlying(response.payload, 'BANKNIFTY');
      
      debugPrint('📊 Filtering results:');
      debugPrint('   - NIFTY options: ${niftyOptions.length}');
      debugPrint('   - BANKNIFTY options: ${bankNiftyOptions.length}');
      
      // Test expiry dates
      final expiryDates = OptionGreeksConverter.getUniqueExpiryDates(response.payload);
      debugPrint('📅 Available expiry dates: ${expiryDates.length}');
      for (final date in expiryDates.take(3)) {
        debugPrint('   - ${date.toString().split(' ')[0]}');
      }
      
      debugPrint('🎉 REST API fallback mechanism is working correctly!');
      
    } catch (e) {
      debugPrint('❌ Error testing REST API fallback: $e');
    }
  }

  /// Simulate WebSocket disconnection for testing
  static void simulateWebSocketDisconnection() {
    debugPrint('🔌 Simulating WebSocket disconnection...');
    debugPrint('📡 Fallback mechanism should now switch to REST API');
    debugPrint('🔄 Data will be refreshed every 30 seconds from REST endpoint');
  }

  /// Simulate WebSocket reconnection
  static void simulateWebSocketReconnection() {
    debugPrint('🔌 Simulating WebSocket reconnection...');
    debugPrint('📡 Switching back to live WebSocket data');
    debugPrint('⏹️ REST API periodic refresh stopped');
  }
}