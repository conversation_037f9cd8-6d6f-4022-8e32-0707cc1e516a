import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';

class MarketLimitToggler extends StatelessWidget {
  final bool isMarket;
  final VoidCallback onMarketTap;
  final VoidCallback onLimitTap;
  final bool isEnabled;

  const MarketLimitToggler({
    Key? key,
    required this.isMarket,
    required this.onMarketTap,
    required this.onLimitTap,
    this.isEnabled = true,
  }) : super(key: key);

  Widget _buildToggleButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required bool isDarkMode,
    required bool isEnabled,
  }) {
    return SizedBox(
      height: 40,
      width: 100,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: isEnabled ? onTap : null,
        child: Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isEnabled
                ? (isSelected
                    ? AppTheme.primaryColor(isDarkMode)
                    : AppTheme.cardColor(isDarkMode))
                : AppTheme.cardColor(isDarkMode).withOpacity(0.5),
            borderRadius: BorderRadius.circular(50),
            border: Border.all(
              color: isEnabled
                  ? AppTheme.borderColor(isDarkMode)
                  : AppTheme.borderColor(isDarkMode).withOpacity(0.5),
              width: 1,
            ),
          ),
          child: Text(
            label,
            style: TextStyle(
              color: isEnabled
                  ? (isSelected
                      ? Colors.white
                      : AppTheme.textPrimary(isDarkMode))
                  : AppTheme.textPrimary(isDarkMode).withOpacity(0.5),
              fontWeight: FontWeight.w600,
              fontSize: 20,
            ),
          ),
        ),
      ),
    );
  }
  

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          height: 58,
          width: 380,
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: AppTheme.borderColor(themeState.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: themeState.isDarkMode 
                    ? Colors.black26 
                    : Colors.grey.withOpacity(0.2),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            spacing: 13,
            children: [
              const SizedBox(width: 1),
              _buildToggleButton(
                label: "Market",
                isSelected: isMarket,
                onTap: onMarketTap,
                isDarkMode: themeState.isDarkMode,
                isEnabled: isEnabled,
              ),
              _buildToggleButton(
                label: "Limit",
                isSelected: !isMarket,
                onTap: onLimitTap,
                isDarkMode: themeState.isDarkMode,
                isEnabled: isEnabled,
              ),
            ],
          ),
        );
      },
    );
  }
}
