//
//  Generated code. Do not modify.
//  source: price.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use priceDescriptor instead')
const Price$json = {
  '1': 'Price',
  '2': [
    {'1': 'zen_id', '3': 1, '4': 1, '5': 11, '6': '.google.protobuf.Int64Value', '10': 'zenId'},
    {'1': 'price', '3': 2, '4': 1, '5': 11, '6': '.google.protobuf.DoubleValue', '10': 'price'},
  ],
};

/// Descriptor for `Price`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List priceDescriptor = $convert.base64Decode(
    'CgVQcmljZRIyCgZ6ZW5faWQYASABKAsyGy5nb29nbGUucHJvdG9idWYuSW50NjRWYWx1ZVIFem'
    'VuSWQSMgoFcHJpY2UYAiABKAsyHC5nb29nbGUucHJvdG9idWYuRG91YmxlVmFsdWVSBXByaWNl');

@$core.Deprecated('Use pricesEmptyRequestDescriptor instead')
const PricesEmptyRequest$json = {
  '1': 'PricesEmptyRequest',
};

/// Descriptor for `PricesEmptyRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pricesEmptyRequestDescriptor = $convert.base64Decode(
    'ChJQcmljZXNFbXB0eVJlcXVlc3Q=');

@$core.Deprecated('Use pricesDescriptor instead')
const Prices$json = {
  '1': 'Prices',
  '2': [
    {'1': 'prices', '3': 1, '4': 3, '5': 11, '6': '.websocket.Price', '10': 'prices'},
  ],
};

/// Descriptor for `Prices`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pricesDescriptor = $convert.base64Decode(
    'CgZQcmljZXMSKAoGcHJpY2VzGAEgAygLMhAud2Vic29ja2V0LlByaWNlUgZwcmljZXM=');

