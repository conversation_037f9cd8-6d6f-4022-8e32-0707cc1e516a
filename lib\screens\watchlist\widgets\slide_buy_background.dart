import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/utils/theme_constants.dart';

class SlideBuyBackground extends StatelessWidget {
  const SlideBuyBackground({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ThemeConstants.buySlideOptionColor,
        borderRadius: const BorderRadius.horizontal(
          left: Radius.circular(10),
          right: Radius.circular(10),
        ),
        boxShadow: [
          // Light inner glow near edges
          BoxShadow(
            color: Colors.lightGreenAccent.withValues(alpha: 0.9),
            offset: const Offset(-10, -10),
            blurRadius: 20,
            inset: true,
          ),
          // Dark inner shadow in center-ish
          const BoxShadow(
            color: Colors.black,
            offset: Offset(10, 10),
            blurRadius: 20,
            inset: true,
          ),
        ],
      ),
      child: const Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              "Buy",
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w800,
                fontSize: 15,
              ),
            ),
            SizedBox(width: 20),
          ],
        ),
      ),
    );
  }
}