import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:phoenix/features/common/api_response_model.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';
import 'package:phoenix/services/request_body_filter_service.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/http_service.dart';
import 'package:phoenix/utils/app_exception.dart'; // Import your AppException here

class PortfolioProvider {
  Future<Map<String, List<PositionsModel>>> fetchPositionsData(
      int clientId) async {
    final customHttpService = HttpService();

    try {
      final positionCompKeyFilter =
          await RequestBodyFilterService.buildPositionCompKeyFilter(clientId);

      final requestBody = jsonEncode({
        ...positionCompKeyFilter,
      });
      debugPrint('requestBody: $requestBody');
      final response = await customHttpService.post(
        Uri.parse(ApiPath.getOpenColosedPositions(clientId)),
        body: requestBody,
        headers: {'Content-Type': 'application/json'},
      );

      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if (apiResponse.code == 200 &&
          apiResponse.status == 'SUCCESS' &&
          response.statusCode == 200) {
        final List<PositionsModel> openPositions =
            (apiResponse.payload["open_positions"] as List<dynamic>)
                .map((data) => PositionsModel.fromJson(data, true))
                .toList();

        final List<PositionsModel> closedPositions =
            (apiResponse.payload["closed_positions"] as List<dynamic>)
                .map((data) => PositionsModel.fromJson(data, false))
                .toList();

        return {
          "open_positions": openPositions,
          "closed_positions": closedPositions,
        };
      } else {
        throw AppException(apiResponse.message);
      }
    } catch (e) {
      // Only wrap if it's not already an AppException
      throw e is AppException
          ? e
          : AppException('Error fetching positions: ${e.toString()}');
    }
  }
}
