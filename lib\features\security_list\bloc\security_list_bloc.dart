import 'package:bloc/bloc.dart';
import 'package:flutter/widgets.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/security_list/data/repository/security_repository.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';

part 'security_list_event.dart';
part 'security_list_state.dart';

class SecurityListBloc extends Bloc<SecurityListEvent, SecurityListState> {
  // Repository reference
  final SecurityRepository _securityRepository;

  // Constructor
  SecurityListBloc(this._securityRepository) : super(SecurityListInitial()) {
    on<FetchSecurityListEvent>(_onFetchSecurityList);
  }

  Future<void> _onFetchSecurityList(
    FetchSecurityListEvent event, // ✅ Correct event type
    Emitter<SecurityListState> emit,
  ) async {
    debugPrint("Reached _onFetchSecurityList");

    emit(SecurityListLoading());

    try {
      final securities = await _securityRepository.getSecurities();

      List<SecurityModel> equities = securities['equities']!;
      List<SecurityModel> futuresAndOptions = securities['futuresOptions']!;
      emit(SecurityListLoaded(equities, futuresAndOptions));
    } catch (e) {
      debugPrint("Error fetching securities: $e");
      emit(SecurityListError("Failed to fetch securities"));
    }
  }
}
