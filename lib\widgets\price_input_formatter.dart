import 'package:flutter/services.dart';

///Limit Price feild
///Validator on typing
class PriceInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // Allow full clear
    if (text.isEmpty) return newValue;

    // Allow just "." or ".x" or ".xx" for smooth editing
    if (text == "." || RegExp(r'^\.\d{0,2}$').hasMatch(text)) {
      return newValue;
    }

    // Allow numbers with optional decimal up to 2 digits
    final regex = RegExp(r'^\d{0,6}(\.\d{0,2})?$');
    if (!regex.hasMatch(text)) {
      return oldValue;
    }

    // Check numeric limit only if parseable
    final numValue = double.tryParse(text);
    if (numValue != null && numValue <= 900000) {
      return newValue;
    }

    return oldValue;
  }
}

///Quantity Lots
///Validator on typing
class QuantityInputFormatter extends TextInputFormatter {
  final int maxValue;

  QuantityInputFormatter({this.maxValue = 100000}); // Optional max limit

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // Allow full clear
    if (text.isEmpty) return newValue;

    // Only digits allowed
    final regex = RegExp(r'^\d+$');
    if (!regex.hasMatch(text)) {
      return oldValue;
    }

    // Limit max value
    final numValue = int.tryParse(text);
    if (numValue != null && numValue <= maxValue) {
      return newValue;
    }

    return oldValue;
  }
}
