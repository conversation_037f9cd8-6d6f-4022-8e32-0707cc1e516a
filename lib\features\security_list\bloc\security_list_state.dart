part of 'security_list_bloc.dart';

@immutable
sealed class SecurityListState {}

final class SecurityListInitial extends SecurityListState {}

final class SecurityListLoading extends SecurityListState {}

final class SecurityListLoaded extends SecurityListState {
  final List<SecurityModel> equityList;
  final List<SecurityModel> featuresList;
  SecurityListLoaded(this.equityList, this.featuresList);
}

final class SecurityListError extends SecurityListState {
  final String error;
  SecurityListError(this.error);
}
