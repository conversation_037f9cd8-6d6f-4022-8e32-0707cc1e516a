import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/theme_constants.dart';

enum PnlViewState { unrealized, realized, both }

class PnlTypeToggler extends StatefulWidget {
  final void Function(PnlViewState state) onToggle;

  const PnlTypeToggler({super.key, required this.onToggle});

  @override
  State<PnlTypeToggler> createState() => _PnlTypeTogglerState();
}

class _PnlTypeTogglerState extends State<PnlTypeToggler> {
  PnlViewState _currentState = PnlViewState.unrealized;

  void _cycleState() {
    setState(() {
      switch (_currentState) {
        case PnlViewState.unrealized:
          _currentState = PnlViewState.realized;
          break;
        case PnlViewState.realized:
          _currentState = PnlViewState.both;
          break;
        case PnlViewState.both:
          _currentState = PnlViewState.unrealized; // or back to unrealized?
          break;
      }
    });

    widget.onToggle(_currentState);
  }

  String _getLabel() {
    switch (_currentState) {
      case PnlViewState.unrealized:
        return 'Unrealized';
      case PnlViewState.realized:
        return 'Realized';
      case PnlViewState.both:
        return 'Both';
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        final bool isDarkMode = state.isDarkMode;
        final Color textColor = isDarkMode ? ThemeConstants.zenGrey : ThemeConstants.zenBlack;
        final Color backgroundColor = isDarkMode ? Colors.grey.withOpacity(0.2) : const Color(0xffE9ECEF);
        final Color iconColor = isDarkMode ? ThemeConstants.zenGrey : const Color.fromARGB(255, 156, 156, 156);
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: _cycleState,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            margin: const EdgeInsets.only(left: 8),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(4),
              
            ),
            child: Row(
              children: [
                Text(
                  _getLabel(),
                  style: TextStyle(
                    color: textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.swap_horiz_outlined,
                  color: iconColor,
                  size: 20,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
