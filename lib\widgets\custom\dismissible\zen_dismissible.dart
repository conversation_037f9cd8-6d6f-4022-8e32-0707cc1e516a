library;

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

// Constants for the dismissible widget
const Curve _kResizeTimeCurve = Interval(0.4, 1.0, curve: Curves.ease);
const double _kMinFlingVelocity = 700.0;
const double _kMinFlingVelocityDelta = 400.0;
const double _kFlingVelocityScale = 1.0 / 300.0;
const double _kDismissThreshold = 0.4;

/// Direction in which a dismissible widget can be dismissed
enum ZenDismissDirection {
  vertical,
  horizontal,
  endToStart,
  startToEnd,
  up,
  down,
  none,
}

/// Callback for when a dismissible is dismissed in a specific direction
typedef DismissDirectionCallback = void Function(DismissDirection direction);

/// Callback to confirm or reject a dismiss action
typedef ConfirmDismissCallback = Future<bool?> Function(DismissDirection direction);

/// Callback for when a dismissible is being dragged
typedef DismissUpdateCallback = void Function(DismissUpdateDetails details);

/// Details provided during drag updates
class DismissUpdateDetails {
  DismissUpdateDetails({
    this.direction = DismissDirection.horizontal,
    this.reached = false,
    this.previousReached = false,
    this.progress = 0.0,
  });

  /// The direction of the dismissible motion
  final DismissDirection direction;

  /// Whether the dismiss threshold has been reached
  final bool reached;

  /// Whether the dismiss threshold was reached in the previous update
  final bool previousReached;

  /// Progress of the dismissible (0.0 to 1.0)
  final double progress;
}

/// A widget that can be dismissed by dragging in the specified direction.
///
/// This widget allows users to swipe items away, commonly used in lists
/// to remove items with a gesture.
class ZenDismissible extends StatefulWidget {
  const ZenDismissible({
    required Key super.key,
    required this.child,
    this.background,
    this.secondaryBackground,
    this.confirmDismiss,
    this.onResize,
    this.onUpdate,
    this.onDismissed,
    this.direction = DismissDirection.horizontal,
    this.resizeDuration = const Duration(milliseconds: 300),
    this.dismissThresholds = const <ZenDismissDirection, double>{},
    this.movementDuration = const Duration(milliseconds: 200),
    this.crossAxisEndOffset = 0.0,
    this.dragStartBehavior = DragStartBehavior.start,
    this.behavior = HitTestBehavior.opaque,
  }) : assert(secondaryBackground == null || background != null);

  /// The widget to be dismissed
  final Widget child;

  /// Widget displayed behind the child when dragged
  final Widget? background;

  /// Widget displayed behind the child when dragged in the opposite direction
  final Widget? secondaryBackground;

  /// Callback to confirm or veto a dismissal
  final ConfirmDismissCallback? confirmDismiss;

  /// Called when the widget changes size during dismissal
  final VoidCallback? onResize;

  /// Called when the widget is dismissed
  final DismissDirectionCallback? onDismissed;

  /// Direction in which the widget can be dismissed
  final DismissDirection direction;

  /// Duration for the resize animation after dismissal
  final Duration? resizeDuration;

  /// Thresholds for different dismiss directions
  final Map<ZenDismissDirection, double> dismissThresholds;

  /// Duration for the movement animation
  final Duration movementDuration;

  /// Offset in the cross axis after dismissal
  final double crossAxisEndOffset;

  /// How the drag gesture should behave
  final DragStartBehavior dragStartBehavior;

  /// How to behave during hit tests
  final HitTestBehavior behavior;

  /// Called during drag updates
  final DismissUpdateCallback? onUpdate;

  @override
  State<ZenDismissible> createState() => _ZenDismissibleState();
}

class _ZenDismissibleState extends State<ZenDismissible> with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // Initialize with a default value to prevent late initialization errors
  late AnimationController _moveController = AnimationController(
    duration: const Duration(milliseconds: 200), // Default duration
    vsync: this,
  );
  late Animation<Offset> _moveAnimation = Tween<Offset>(
    begin: Offset.zero,
    end: Offset.zero,
  ).animate(_moveController);
  
  AnimationController? _resizeController;
  Animation<double>? _resizeAnimation;
  
  double _dragExtent = 0.0;
  bool _dragUnderway = false;
  Size? _sizePriorToCollapse;
  bool _dismissThresholdReached = false;
  
  final GlobalKey _contentKey = GlobalKey();
  
  @override
  void initState() {
    super.initState();
    // Properly initialize with the widget's duration
    _moveController = AnimationController(
      duration: widget.movementDuration,
      vsync: this,
    );
    _moveController.addStatusListener(_handleDismissStatusChanged);
    _moveController.addListener(_handleDismissUpdateValueChanged);
    _updateMoveAnimation();
  }
  
  @override
  void dispose() {
    _moveController.dispose();
    _resizeController?.dispose();
    super.dispose();
  }
  
  @override
  bool get wantKeepAlive => 
      _moveController.isAnimating || (_resizeController?.isAnimating ?? false);
  
  bool get _directionIsXAxis {
    return widget.direction == DismissDirection.horizontal ||
           widget.direction == DismissDirection.endToStart ||
           widget.direction == DismissDirection.startToEnd;
  }
  
  DismissDirection get _dismissDirection {
    if (_dragExtent > 0) {
      return _directionIsXAxis ? DismissDirection.startToEnd : DismissDirection.down;
    }
    return _directionIsXAxis ? DismissDirection.endToStart : DismissDirection.up;
  }
  
  double get _dismissThreshold {
    final ZenDismissDirection zenDirection = _convertToZenDirection(_dismissDirection);
    // Debug the threshold value
    final threshold = widget.dismissThresholds[zenDirection] ?? _kDismissThreshold;
    //debugPrint("Dismiss threshold for ${zenDirection.toString()}: $threshold");
    return threshold;
  }
  
  ZenDismissDirection _convertToZenDirection(DismissDirection direction) {
    switch (direction) {
      case DismissDirection.startToEnd:
        return ZenDismissDirection.startToEnd;
      case DismissDirection.endToStart:
        return ZenDismissDirection.endToStart;
      case DismissDirection.up:
        return ZenDismissDirection.up;
      case DismissDirection.down:
        return ZenDismissDirection.down;
      case DismissDirection.horizontal:
        return ZenDismissDirection.horizontal;
      case DismissDirection.vertical:
        return ZenDismissDirection.vertical;
      case DismissDirection.none:
        return ZenDismissDirection.none;
    }
  }
  
  void _updateMoveAnimation() {
    final double end = _dragExtent.sign;
    _moveAnimation = _moveController.drive(
      Tween<Offset>(
        begin: Offset.zero,
        end: _directionIsXAxis
            ? Offset(end, widget.crossAxisEndOffset)
            : Offset(widget.crossAxisEndOffset, end),
      ),
    );
  }
  
  void _handleDismissStatusChanged(AnimationStatus status) {
    if (status == AnimationStatus.completed && !_dragUnderway) {
      _handleMoveCompleted();
    }
    updateKeepAlive();
  }
  
  void _handleDismissUpdateValueChanged() {
    if (widget.onUpdate != null) {
      final bool oldDismissThresholdReached = _dismissThresholdReached;
      _dismissThresholdReached = _moveController.value > _dismissThreshold;
      final DismissUpdateDetails details = DismissUpdateDetails(
        direction: _dismissDirection,
        reached: _dismissThresholdReached,
        previousReached: oldDismissThresholdReached,
        progress: _moveController.value,
      );
      widget.onUpdate!(details);
    }
  }
  
  void _handleDragStart(DragStartDetails details) {
    _dragUnderway = true;
    if (_moveController.isAnimating) {
      _dragExtent = _moveController.value * _extentToMove;
      _moveController.stop();
    } else {
      _dragExtent = 0.0;
      _moveController.value = 0.0;
    }
    setState(() {
      _updateMoveAnimation();
    });
  }
  
  void _handleDragUpdate(DragUpdateDetails details) {
    if (!_dragUnderway) {
      return;
    }
    
    final double delta = details.primaryDelta ?? 0.0;
    final double oldDragExtent = _dragExtent;
    
    if (_directionIsXAxis && widget.direction == DismissDirection.horizontal) {
      _dragExtent += delta;
    } else if (_directionIsXAxis && widget.direction == DismissDirection.endToStart && delta < 0) {
      _dragExtent += delta;
    } else if (_directionIsXAxis && widget.direction == DismissDirection.startToEnd && delta > 0) {
      _dragExtent += delta;
    } else if (!_directionIsXAxis && widget.direction == DismissDirection.vertical) {
      _dragExtent += delta;
    } else if (!_directionIsXAxis && widget.direction == DismissDirection.up && delta < 0) {
      _dragExtent += delta;
    } else if (!_directionIsXAxis && widget.direction == DismissDirection.down && delta > 0) {
      _dragExtent += delta;
    }
    
    if (oldDragExtent.sign != _dragExtent.sign) {
      setState(() {
        _updateMoveAnimation();
      });
    }
    
    if (!_moveController.isAnimating) {
      final value = _dragExtent.abs() / _extentToMove;
      _moveController.value = value;
      
      // Debug the current drag value vs threshold
      //debugPrint("Drag value: $value, Threshold: $_dismissThreshold");
      
      // Check if we've crossed the threshold
      if (value > _dismissThreshold && !_dismissThresholdReached) {
        _dismissThresholdReached = true;
        // Provide haptic feedback when threshold is crossed
        //HapticFeedback.mediumImpact();
        debugPrint("Threshold reached!");
      } else if (value <= _dismissThreshold && _dismissThresholdReached) {
        _dismissThresholdReached = false;
      }
    }
  }
  
  void _handleDragEnd(DragEndDetails details) {
    if (!_dragUnderway || _moveController.isAnimating) {
      return;
    }
    _dragUnderway = false;
    
    if (_moveController.isCompleted) {
      _handleMoveCompleted();
      return;
    }
    
    final double flingVelocity = _directionIsXAxis
        ? details.velocity.pixelsPerSecond.dx
        : details.velocity.pixelsPerSecond.dy;
    
    // Check if we've reached the threshold
    if (_moveController.value > _dismissThreshold) {
      // If we've reached the threshold, trigger the confirmation
      _handleMoveCompleted();
      return;
    }
    
    if (flingVelocity.abs() >= _kMinFlingVelocity) {
      final double visualVelocity = flingVelocity.abs() / _extentToMove;
      
      if (flingVelocity.sign == _dragExtent.sign &&
          visualVelocity >= _kMinFlingVelocityDelta) {
        _moveController.fling(velocity: visualVelocity * _kFlingVelocityScale);
      } else if (_moveController.value > 0.0) {
        _moveController.reverse();
      }
    } else {
      _moveController.reverse();
    }
  }
  
  Future<void> _handleMoveCompleted() async {
    // If we're already at or past the threshold, trigger the confirmation
    final bool result = await _confirmStartResizeAnimation();
    if (mounted) {
      if (result) {
        // Ensure the controller is completed before starting resize animation
        if (!_moveController.isCompleted) {
          await _moveController.forward();
        }
        _startResizeAnimation();
      } else {
        _moveController.reverse();
      }
    }
  }
  
  Future<bool> _confirmStartResizeAnimation() async {
    if (widget.confirmDismiss != null) {
      final DismissDirection direction = _dismissDirection;
      try {
        return await widget.confirmDismiss!(direction) ?? false;
      } finally {
      }
    }
    return true;
  }
  
  void _startResizeAnimation() {
    assert(_moveController.isCompleted);
    assert(_resizeController == null);
    assert(_sizePriorToCollapse == null);
    
    if (widget.resizeDuration == null) {
      if (widget.onDismissed != null) {
        final DismissDirection direction = _dismissDirection;
        widget.onDismissed!(direction);
      }
    } else {
      _resizeController = AnimationController(
        duration: widget.resizeDuration,
        vsync: this
      )
        ..addListener(_handleResizeProgressChanged)
        ..addStatusListener((AnimationStatus status) => updateKeepAlive());
      
      _resizeController!.forward();
      setState(() {
        _sizePriorToCollapse = context.size;
        _resizeAnimation = _resizeController!
            .drive(CurveTween(curve: _kResizeTimeCurve))
            .drive(Tween<double>(begin: 1.0, end: 0.0));
      });
    }
  }
  
  void _handleResizeProgressChanged() {
    if (_resizeController!.isCompleted) {
      widget.onDismissed?.call(_dismissDirection);
    } else {
      widget.onResize?.call();
    }
  }
  
  double get _extentToMove {
    final RenderBox? box = context.findRenderObject() as RenderBox?;
    if (box == null) {
      return 0.0;
    }
    return _directionIsXAxis ? box.size.width : box.size.height;
  }
  
  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    Widget? background = widget.background;
    if (widget.secondaryBackground != null) {
      final DismissDirection direction = _dismissDirection;
      if (direction == DismissDirection.endToStart || direction == DismissDirection.up) {
        background = widget.secondaryBackground;
      }
    }
    
    if (_resizeAnimation != null) {
      // We've been dragged aside and are now resizing.
      return SizeTransition(
        sizeFactor: _resizeAnimation!,
        axis: _directionIsXAxis ? Axis.vertical : Axis.horizontal,
        child: SizedBox(
          width: _sizePriorToCollapse!.width,
          height: _sizePriorToCollapse!.height,
          child: background,
        ),
      );
    }
    
    Widget content = SlideTransition(
      position: _moveAnimation,
      child: KeyedSubtree(key: _contentKey, child: widget.child),
    );
    
    if (background != null) {
      content = Stack(
        children: <Widget>[
          if (!_moveAnimation.isDismissed)
            Positioned.fill(
              child: background,
            ),
          content,
        ],
      );
    }
    
    // If the direction is none, we don't add drag gestures
    if (widget.direction == DismissDirection.none) {
      return content;
    }
    
    // We are not resizing but may be being dragged
    return GestureDetector(
      onHorizontalDragStart: _directionIsXAxis ? _handleDragStart : null,
      onHorizontalDragUpdate: _directionIsXAxis ? _handleDragUpdate : null,
      onHorizontalDragEnd: _directionIsXAxis ? _handleDragEnd : null,
      onVerticalDragStart: _directionIsXAxis ? null : _handleDragStart,
      onVerticalDragUpdate: _directionIsXAxis ? null : _handleDragUpdate,
      onVerticalDragEnd: _directionIsXAxis ? null : _handleDragEnd,
      behavior: widget.behavior,
      dragStartBehavior: widget.dragStartBehavior,
      child: content,
    );
  }
}
