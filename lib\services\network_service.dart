import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

class NetworkService {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  // Stream controller for network status
  final StreamController<bool> _networkStatusController = StreamController<bool>.broadcast();
  
  // Current network status
  bool _isConnected = true;
  
  // Getters
  bool get isConnected => _isConnected;
  Stream<bool> get networkStatusStream => _networkStatusController.stream;

  /// Initialize the network service
  Future<void> initialize() async {
    // Check initial connectivity
    await _checkConnectivity();
    
    // Listen to connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) async {
        await _checkConnectivity();
      },
    );
  }

  /// Check current connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final List<ConnectivityResult> connectivityResults = await _connectivity.checkConnectivity();
      
      // Check if any connection type is available
      bool hasConnection = connectivityResults.any((result) => 
        result == ConnectivityResult.mobile || 
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.ethernet
      );

      // If we have a connection type, verify with actual internet connectivity
      if (hasConnection) {
        hasConnection = await _hasInternetConnection();
      }

      _updateNetworkStatus(hasConnection);
    } catch (e) {
      debugPrint('#1 Error checking connectivity: $e');
      _updateNetworkStatus(false);
    }
  }

  /// Verify actual internet connectivity by attempting to reach a reliable host
  Future<bool> _hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    } catch (e) {
      debugPrint('#2 Error checking internet connection: $e');
      return false;
    }
  }

  /// Update network status and notify listeners
  void _updateNetworkStatus(bool isConnected) {
    if (_isConnected != isConnected) {
      _isConnected = isConnected;
      _networkStatusController.add(_isConnected);
      debugPrint('🌐 Network status changed: ${_isConnected ? "Connected" : "Disconnected"}');
    }
  }

  /// Manually check connectivity (for retry functionality)
  Future<bool> checkConnectivity() async {
    await _checkConnectivity();
    return _isConnected;
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _networkStatusController.close();
  }
}