import 'dart:convert';
import 'package:phoenix/managers/api/api_response_manager.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/app_exception.dart';
import 'package:phoenix/utils/http_service.dart';

class SecurityProvider {
  

  Future<Map<String, List<Map<String, dynamic>>>> fetchSecurities() async {
    final customHttpService = HttpService();
    // Make the GET request
    final response = await customHttpService.get(
      Uri.parse(ApiPath.getTradableSecurities()),
      headers: {'Content-Type': 'application/json'},
    );

    final jsonData = jsonDecode(response.body);

    final apiResponse = ApiResponse.fromJson(
      jsonData,
      (dynamic payload) => payload,
    );

    if (apiResponse.code != 200 || apiResponse.status != 'SUCCESS' || response.statusCode != 200) {
      throw AppException(apiResponse.message);
    }
    else {
      Map<String, dynamic> data = apiResponse.payload;
      // Extract EQ for Equities
      List<Map<String, dynamic>> equityData =
          List<Map<String, dynamic>>.from(data['EQ'] ?? []);

      // Extract CE, PE, and FUT for Futures & Options
      List<Map<String, dynamic>> futuresOptionsData = [
        ...List<Map<String, dynamic>>.from(data['CE'] ?? []),
        ...List<Map<String, dynamic>>.from(data['PE'] ?? []),
        ...List<Map<String, dynamic>>.from(data['FUT'] ?? []),
      ];

      futuresOptionsData.sort((a, b) {
        final aStrike = a['strike'] as double;
        final bStrike = b['strike'] as double;

        final aInstrumentType = a['instrument_type'] as String;
        final bInstrumentType = b['instrument_type'] as String;

        // Sort by strike price first
        if (aStrike != bStrike) {
          return aStrike.compareTo(bStrike);
        }

        // For the same strike price, sort by instrument type (CE before PE)
        if (aInstrumentType == 'CE' && bInstrumentType == 'PE') {
          return -1;
        } else if (aInstrumentType == 'PE' && bInstrumentType == 'CE') {
          return 1;
        } else {
          return 0;
        }
      });

      return {
        'equities': equityData,
        'futuresOptions': futuresOptionsData,
      };
    }
  }
}
