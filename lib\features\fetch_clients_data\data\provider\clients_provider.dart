import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:phoenix/features/fetch_clients_data/model/client_data.dart';
import 'package:phoenix/managers/api/api_response_manager.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/app_exception.dart';
import 'package:phoenix/utils/http_service.dart';

class ClientsProvider {
  Future<List<ClientData>> fetchClientData() async {
    try {
      final customHttpService = HttpService();
      // Make the GET request
      final response = await customHttpService.get(
        Uri.parse(ApiPath.getClients()),
        headers: {'Content-Type': 'application/json'},
      );

      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if (apiResponse.code != 200 || apiResponse.status != 'SUCCESS' || response.statusCode != 200) {
        throw AppException(apiResponse.message);
      } else {
        // Decode the JSON response into a list of dynamic objects
        final jsonData = apiResponse.payload as List<dynamic>;
        // Map each dynamic object to a ClientData model
        return jsonData.map((data) => ClientData.fromJson(data)).toList();
      }

      // Check if the response is successful
    } catch (e) {
      // Handle any errors that may occur
      debugPrint('Error fetching clients: $e');

      throw e is AppException
          ? e
          : AppException('Failed to fetch clients: ${e.toString()}');
    }
  }
}
