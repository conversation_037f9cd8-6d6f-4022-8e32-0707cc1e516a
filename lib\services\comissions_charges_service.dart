import 'package:phoenix/features/commissions_data_map/model/broker_charge_model.dart';
import 'package:phoenix/features/commissions_data_map/model/exchange_charge_model.dart';


class CommissionChargesService {

  //roundto2decimal doble
  static double roundTo2Decimal(double value) {
    return double.parse(value.toStringAsFixed(2));
  }



  static CalculatedCharges calculateCharges({
    required double turnover,
    required BrokerChargeModel brokerModel,
    required ExchangeChargeModel exchangeModel,
  }) {
    // Calculate brokerage charge
    double brokerageCharge;

    if (brokerModel.flatCharges > 0.0) {
      brokerageCharge = roundTo2Decimal(brokerModel.flatCharges);
    } else {
      double charge = roundTo2Decimal(turnover * brokerModel.brokerageCharges);

      if (brokerModel.threshold > 0.0) {
        brokerageCharge = charge < roundTo2Decimal(brokerModel.threshold) ? charge : roundTo2Decimal(brokerModel.threshold);
      } else {
        brokerageCharge = charge;
      }
    }

    // Exchange charges
    double sttCttCharge = double.parse((turnover * exchangeModel.sttCtt).toStringAsFixed(2));
    double transactionCharge =  double.parse((turnover * exchangeModel.transactionCharges).toStringAsFixed(2));
    double ipftCharge =  double.parse((turnover * exchangeModel.ipft).toStringAsFixed(2));
    double sebiCharge = double.parse((turnover * exchangeModel.sebi).toStringAsFixed(2));
    double stampCharge = double.parse((turnover * exchangeModel.stampCharges).toStringAsFixed(2));

    double gstCharge = roundTo2Decimal((brokerageCharge + sebiCharge + transactionCharge) * exchangeModel.gst);

    double exchangeCharge = roundTo2Decimal(sttCttCharge + transactionCharge + sebiCharge + stampCharge + ipftCharge + gstCharge);

    double finalCharges = brokerageCharge + exchangeCharge;

    return CalculatedCharges(
      brokerageCharge: brokerageCharge,
      sttCttCharge: sttCttCharge,
      transactionCharge: transactionCharge,
      ipftCharge: ipftCharge,
      sebiCharge: sebiCharge,
      stampCharge: stampCharge,
      gstCharge: gstCharge,
      exchangeCharge: exchangeCharge,
      finalCharges: finalCharges,
    );
  }
}

class CalculatedCharges {
  final double brokerageCharge;
  final double sttCttCharge;
  final double transactionCharge;
  final double ipftCharge;
  final double sebiCharge;
  final double stampCharge;
  final double gstCharge;
  final double exchangeCharge;
  final double finalCharges;

  CalculatedCharges({
    required this.brokerageCharge,
    required this.sttCttCharge,
    required this.transactionCharge,
    required this.ipftCharge,
    required this.sebiCharge,
    required this.stampCharge,
    required this.gstCharge,
    required this.exchangeCharge,
    required this.finalCharges,
  });

  @override
  String toString() {
    return 'CalculatedCharges(brokerageCharge: $brokerageCharge, sttCttCharge: $sttCttCharge, transactionCharge: $transactionCharge, ipftCharge: $ipftCharge, sebiCharge: $sebiCharge, stampCharge: $stampCharge, gstCharge: $gstCharge, exchangeCharge: $exchangeCharge, finalCharges: $finalCharges)';
  }

}

