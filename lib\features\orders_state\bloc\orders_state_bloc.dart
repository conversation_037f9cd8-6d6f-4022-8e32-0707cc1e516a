import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:phoenix/features/orders_state/data/repository/order_state_repository.dart';
import 'package:phoenix/features/orders_state/model/unified_order_data.dart';

part 'orders_state_event.dart';
part 'orders_state_state.dart';

class OrdersStateBloc extends Bloc<OrdersStateEvent, OrdersStateState> {
  final OrderStateRepository _orderStateRepository;
  OrdersStateBloc(this._orderStateRepository) : super(OrdersStateInitial()) {
    on<FetchOrdersState>(_onFetchZenOrdersState);
  }

  void _onFetchZenOrdersState(
    FetchOrdersState event,
    Emitter<OrdersStateState> emit,
  ) async {
    debugPrint("Reach _onFetchSentinalOrdersState");
    emit(OrdersStateLoading());
    try {
      final data =  await _orderStateRepository.getUnifiedOrderState(event.clientId);

      emit(OrdersStateLoaded(data));
    } catch (e) {
      emit(OrdersStateError(e.toString()));
    }
  }
}
