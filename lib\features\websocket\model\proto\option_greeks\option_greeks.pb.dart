//
//  Generated code. Do not modify.
//  source: option_greeks.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../google/protobuf/wrappers.pb.dart' as $0;
import '../google/type/date.pb.dart' as $1;

class ZenOptionGreeks extends $pb.GeneratedMessage {
  factory ZenOptionGreeks({
    $0.Int64Value? zenId,
    $0.Int64Value? underlyingZenId,
    $0.DoubleValue? impliedVolatility,
    $0.DoubleValue? delta,
    $0.DoubleValue? delta2,
    $0.DoubleValue? theta,
    $0.DoubleValue? gamma,
    $0.DoubleValue? vega,
    $0.DoubleValue? rho,
    $0.DoubleValue? color,
    $0.DoubleValue? charm,
    $0.StringValue? moneyness,
    $0.StringValue? tradingSymbol,
    $0.DoubleValue? strike,
    $1.Date? expiry,
    $0.DoubleValue? lastPrice,
    $0.Int32Value? oi,
    $0.Int32Value? oiDayHigh,
    $0.Int32Value? oiDayLow,
  }) {
    final $result = create();
    if (zenId != null) {
      $result.zenId = zenId;
    }
    if (underlyingZenId != null) {
      $result.underlyingZenId = underlyingZenId;
    }
    if (impliedVolatility != null) {
      $result.impliedVolatility = impliedVolatility;
    }
    if (delta != null) {
      $result.delta = delta;
    }
    if (delta2 != null) {
      $result.delta2 = delta2;
    }
    if (theta != null) {
      $result.theta = theta;
    }
    if (gamma != null) {
      $result.gamma = gamma;
    }
    if (vega != null) {
      $result.vega = vega;
    }
    if (rho != null) {
      $result.rho = rho;
    }
    if (color != null) {
      $result.color = color;
    }
    if (charm != null) {
      $result.charm = charm;
    }
    if (moneyness != null) {
      $result.moneyness = moneyness;
    }
    if (tradingSymbol != null) {
      $result.tradingSymbol = tradingSymbol;
    }
    if (strike != null) {
      $result.strike = strike;
    }
    if (expiry != null) {
      $result.expiry = expiry;
    }
    if (lastPrice != null) {
      $result.lastPrice = lastPrice;
    }
    if (oi != null) {
      $result.oi = oi;
    }
    if (oiDayHigh != null) {
      $result.oiDayHigh = oiDayHigh;
    }
    if (oiDayLow != null) {
      $result.oiDayLow = oiDayLow;
    }
    return $result;
  }
  ZenOptionGreeks._() : super();
  factory ZenOptionGreeks.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ZenOptionGreeks.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ZenOptionGreeks', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.zentropy.phoenix.common.dataset.derivatives'), createEmptyInstance: create)
    ..aOM<$0.Int64Value>(1, _omitFieldNames ? '' : 'zenId', subBuilder: $0.Int64Value.create)
    ..aOM<$0.Int64Value>(2, _omitFieldNames ? '' : 'underlyingZenId', subBuilder: $0.Int64Value.create)
    ..aOM<$0.DoubleValue>(3, _omitFieldNames ? '' : 'impliedVolatility', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(4, _omitFieldNames ? '' : 'delta', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(5, _omitFieldNames ? '' : 'delta2', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(6, _omitFieldNames ? '' : 'theta', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(7, _omitFieldNames ? '' : 'gamma', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(8, _omitFieldNames ? '' : 'vega', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(9, _omitFieldNames ? '' : 'rho', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(10, _omitFieldNames ? '' : 'color', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(11, _omitFieldNames ? '' : 'charm', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.StringValue>(12, _omitFieldNames ? '' : 'moneyness', subBuilder: $0.StringValue.create)
    ..aOM<$0.StringValue>(13, _omitFieldNames ? '' : 'tradingSymbol', subBuilder: $0.StringValue.create)
    ..aOM<$0.DoubleValue>(14, _omitFieldNames ? '' : 'strike', subBuilder: $0.DoubleValue.create)
    ..aOM<$1.Date>(15, _omitFieldNames ? '' : 'expiry', subBuilder: $1.Date.create)
    ..aOM<$0.DoubleValue>(16, _omitFieldNames ? '' : 'lastPrice', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.Int32Value>(17, _omitFieldNames ? '' : 'oi', subBuilder: $0.Int32Value.create)
    ..aOM<$0.Int32Value>(18, _omitFieldNames ? '' : 'oiDayHigh', subBuilder: $0.Int32Value.create)
    ..aOM<$0.Int32Value>(19, _omitFieldNames ? '' : 'oiDayLow', subBuilder: $0.Int32Value.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ZenOptionGreeks clone() => ZenOptionGreeks()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ZenOptionGreeks copyWith(void Function(ZenOptionGreeks) updates) => super.copyWith((message) => updates(message as ZenOptionGreeks)) as ZenOptionGreeks;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ZenOptionGreeks create() => ZenOptionGreeks._();
  ZenOptionGreeks createEmptyInstance() => create();
  static $pb.PbList<ZenOptionGreeks> createRepeated() => $pb.PbList<ZenOptionGreeks>();
  @$core.pragma('dart2js:noInline')
  static ZenOptionGreeks getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ZenOptionGreeks>(create);
  static ZenOptionGreeks? _defaultInstance;

  @$pb.TagNumber(1)
  $0.Int64Value get zenId => $_getN(0);
  @$pb.TagNumber(1)
  set zenId($0.Int64Value v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasZenId() => $_has(0);
  @$pb.TagNumber(1)
  void clearZenId() => clearField(1);
  @$pb.TagNumber(1)
  $0.Int64Value ensureZenId() => $_ensure(0);

  @$pb.TagNumber(2)
  $0.Int64Value get underlyingZenId => $_getN(1);
  @$pb.TagNumber(2)
  set underlyingZenId($0.Int64Value v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasUnderlyingZenId() => $_has(1);
  @$pb.TagNumber(2)
  void clearUnderlyingZenId() => clearField(2);
  @$pb.TagNumber(2)
  $0.Int64Value ensureUnderlyingZenId() => $_ensure(1);

  @$pb.TagNumber(3)
  $0.DoubleValue get impliedVolatility => $_getN(2);
  @$pb.TagNumber(3)
  set impliedVolatility($0.DoubleValue v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasImpliedVolatility() => $_has(2);
  @$pb.TagNumber(3)
  void clearImpliedVolatility() => clearField(3);
  @$pb.TagNumber(3)
  $0.DoubleValue ensureImpliedVolatility() => $_ensure(2);

  @$pb.TagNumber(4)
  $0.DoubleValue get delta => $_getN(3);
  @$pb.TagNumber(4)
  set delta($0.DoubleValue v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasDelta() => $_has(3);
  @$pb.TagNumber(4)
  void clearDelta() => clearField(4);
  @$pb.TagNumber(4)
  $0.DoubleValue ensureDelta() => $_ensure(3);

  @$pb.TagNumber(5)
  $0.DoubleValue get delta2 => $_getN(4);
  @$pb.TagNumber(5)
  set delta2($0.DoubleValue v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasDelta2() => $_has(4);
  @$pb.TagNumber(5)
  void clearDelta2() => clearField(5);
  @$pb.TagNumber(5)
  $0.DoubleValue ensureDelta2() => $_ensure(4);

  @$pb.TagNumber(6)
  $0.DoubleValue get theta => $_getN(5);
  @$pb.TagNumber(6)
  set theta($0.DoubleValue v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasTheta() => $_has(5);
  @$pb.TagNumber(6)
  void clearTheta() => clearField(6);
  @$pb.TagNumber(6)
  $0.DoubleValue ensureTheta() => $_ensure(5);

  @$pb.TagNumber(7)
  $0.DoubleValue get gamma => $_getN(6);
  @$pb.TagNumber(7)
  set gamma($0.DoubleValue v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasGamma() => $_has(6);
  @$pb.TagNumber(7)
  void clearGamma() => clearField(7);
  @$pb.TagNumber(7)
  $0.DoubleValue ensureGamma() => $_ensure(6);

  @$pb.TagNumber(8)
  $0.DoubleValue get vega => $_getN(7);
  @$pb.TagNumber(8)
  set vega($0.DoubleValue v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasVega() => $_has(7);
  @$pb.TagNumber(8)
  void clearVega() => clearField(8);
  @$pb.TagNumber(8)
  $0.DoubleValue ensureVega() => $_ensure(7);

  @$pb.TagNumber(9)
  $0.DoubleValue get rho => $_getN(8);
  @$pb.TagNumber(9)
  set rho($0.DoubleValue v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasRho() => $_has(8);
  @$pb.TagNumber(9)
  void clearRho() => clearField(9);
  @$pb.TagNumber(9)
  $0.DoubleValue ensureRho() => $_ensure(8);

  @$pb.TagNumber(10)
  $0.DoubleValue get color => $_getN(9);
  @$pb.TagNumber(10)
  set color($0.DoubleValue v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasColor() => $_has(9);
  @$pb.TagNumber(10)
  void clearColor() => clearField(10);
  @$pb.TagNumber(10)
  $0.DoubleValue ensureColor() => $_ensure(9);

  @$pb.TagNumber(11)
  $0.DoubleValue get charm => $_getN(10);
  @$pb.TagNumber(11)
  set charm($0.DoubleValue v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasCharm() => $_has(10);
  @$pb.TagNumber(11)
  void clearCharm() => clearField(11);
  @$pb.TagNumber(11)
  $0.DoubleValue ensureCharm() => $_ensure(10);

  @$pb.TagNumber(12)
  $0.StringValue get moneyness => $_getN(11);
  @$pb.TagNumber(12)
  set moneyness($0.StringValue v) { setField(12, v); }
  @$pb.TagNumber(12)
  $core.bool hasMoneyness() => $_has(11);
  @$pb.TagNumber(12)
  void clearMoneyness() => clearField(12);
  @$pb.TagNumber(12)
  $0.StringValue ensureMoneyness() => $_ensure(11);

  @$pb.TagNumber(13)
  $0.StringValue get tradingSymbol => $_getN(12);
  @$pb.TagNumber(13)
  set tradingSymbol($0.StringValue v) { setField(13, v); }
  @$pb.TagNumber(13)
  $core.bool hasTradingSymbol() => $_has(12);
  @$pb.TagNumber(13)
  void clearTradingSymbol() => clearField(13);
  @$pb.TagNumber(13)
  $0.StringValue ensureTradingSymbol() => $_ensure(12);

  @$pb.TagNumber(14)
  $0.DoubleValue get strike => $_getN(13);
  @$pb.TagNumber(14)
  set strike($0.DoubleValue v) { setField(14, v); }
  @$pb.TagNumber(14)
  $core.bool hasStrike() => $_has(13);
  @$pb.TagNumber(14)
  void clearStrike() => clearField(14);
  @$pb.TagNumber(14)
  $0.DoubleValue ensureStrike() => $_ensure(13);

  @$pb.TagNumber(15)
  $1.Date get expiry => $_getN(14);
  @$pb.TagNumber(15)
  set expiry($1.Date v) { setField(15, v); }
  @$pb.TagNumber(15)
  $core.bool hasExpiry() => $_has(14);
  @$pb.TagNumber(15)
  void clearExpiry() => clearField(15);
  @$pb.TagNumber(15)
  $1.Date ensureExpiry() => $_ensure(14);

  @$pb.TagNumber(16)
  $0.DoubleValue get lastPrice => $_getN(15);
  @$pb.TagNumber(16)
  set lastPrice($0.DoubleValue v) { setField(16, v); }
  @$pb.TagNumber(16)
  $core.bool hasLastPrice() => $_has(15);
  @$pb.TagNumber(16)
  void clearLastPrice() => clearField(16);
  @$pb.TagNumber(16)
  $0.DoubleValue ensureLastPrice() => $_ensure(15);

  @$pb.TagNumber(17)
  $0.Int32Value get oi => $_getN(16);
  @$pb.TagNumber(17)
  set oi($0.Int32Value v) { setField(17, v); }
  @$pb.TagNumber(17)
  $core.bool hasOi() => $_has(16);
  @$pb.TagNumber(17)
  void clearOi() => clearField(17);
  @$pb.TagNumber(17)
  $0.Int32Value ensureOi() => $_ensure(16);

  @$pb.TagNumber(18)
  $0.Int32Value get oiDayHigh => $_getN(17);
  @$pb.TagNumber(18)
  set oiDayHigh($0.Int32Value v) { setField(18, v); }
  @$pb.TagNumber(18)
  $core.bool hasOiDayHigh() => $_has(17);
  @$pb.TagNumber(18)
  void clearOiDayHigh() => clearField(18);
  @$pb.TagNumber(18)
  $0.Int32Value ensureOiDayHigh() => $_ensure(17);

  @$pb.TagNumber(19)
  $0.Int32Value get oiDayLow => $_getN(18);
  @$pb.TagNumber(19)
  set oiDayLow($0.Int32Value v) { setField(19, v); }
  @$pb.TagNumber(19)
  $core.bool hasOiDayLow() => $_has(18);
  @$pb.TagNumber(19)
  void clearOiDayLow() => clearField(19);
  @$pb.TagNumber(19)
  $0.Int32Value ensureOiDayLow() => $_ensure(18);
}

class ZenOptionGreeksList extends $pb.GeneratedMessage {
  factory ZenOptionGreeksList({
    $core.Iterable<ZenOptionGreeks>? zenOptionGreeks,
  }) {
    final $result = create();
    if (zenOptionGreeks != null) {
      $result.zenOptionGreeks.addAll(zenOptionGreeks);
    }
    return $result;
  }
  ZenOptionGreeksList._() : super();
  factory ZenOptionGreeksList.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ZenOptionGreeksList.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ZenOptionGreeksList', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.zentropy.phoenix.common.dataset.derivatives'), createEmptyInstance: create)
    ..pc<ZenOptionGreeks>(1, _omitFieldNames ? '' : 'zenOptionGreeks', $pb.PbFieldType.PM, subBuilder: ZenOptionGreeks.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ZenOptionGreeksList clone() => ZenOptionGreeksList()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ZenOptionGreeksList copyWith(void Function(ZenOptionGreeksList) updates) => super.copyWith((message) => updates(message as ZenOptionGreeksList)) as ZenOptionGreeksList;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ZenOptionGreeksList create() => ZenOptionGreeksList._();
  ZenOptionGreeksList createEmptyInstance() => create();
  static $pb.PbList<ZenOptionGreeksList> createRepeated() => $pb.PbList<ZenOptionGreeksList>();
  @$core.pragma('dart2js:noInline')
  static ZenOptionGreeksList getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ZenOptionGreeksList>(create);
  static ZenOptionGreeksList? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<ZenOptionGreeks> get zenOptionGreeks => $_getList(0);
}

class OptionGreeksEmptyRequest extends $pb.GeneratedMessage {
  factory OptionGreeksEmptyRequest() => create();
  OptionGreeksEmptyRequest._() : super();
  factory OptionGreeksEmptyRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory OptionGreeksEmptyRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'OptionGreeksEmptyRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.zentropy.phoenix.common.dataset.derivatives'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  OptionGreeksEmptyRequest clone() => OptionGreeksEmptyRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  OptionGreeksEmptyRequest copyWith(void Function(OptionGreeksEmptyRequest) updates) => super.copyWith((message) => updates(message as OptionGreeksEmptyRequest)) as OptionGreeksEmptyRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static OptionGreeksEmptyRequest create() => OptionGreeksEmptyRequest._();
  OptionGreeksEmptyRequest createEmptyInstance() => create();
  static $pb.PbList<OptionGreeksEmptyRequest> createRepeated() => $pb.PbList<OptionGreeksEmptyRequest>();
  @$core.pragma('dart2js:noInline')
  static OptionGreeksEmptyRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<OptionGreeksEmptyRequest>(create);
  static OptionGreeksEmptyRequest? _defaultInstance;
}

const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');