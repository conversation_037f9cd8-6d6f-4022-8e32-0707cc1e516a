import 'package:phoenix/features/common/position_comp_key.dart';

/// This data is used to get the latest order state and 
/// show it in the edit order form
///  
class LatestOrder {
  final String tradingSymbol;
  final int quantity;
  final String orderType;
  final String product;
  final double limitPrice;
  final double triggerPrice;
  final int zenOrderId;
  final String orderVariety;
  final String validity;
  final String currency;
  final String exchange;
  final String transactionType;
  final int validityTtl;
  final int disclosedQuantity;
  final String method;
  final double stopLossLimitPrice;
  final DateTime orderCreationTime;
  final int minutes;
  final PositionCompKey positionCompKey;

  LatestOrder({
    required this.tradingSymbol,
    required this.quantity,
    required this.orderType,
    required this.product,
    required this.limitPrice,
    required this.triggerPrice,
    required this.zenOrderId,
    required this.orderVariety,
    required this.validity,
    required this.currency,
    required this.exchange,
    required this.transactionType,
    required this.validityTtl,
    required this.disclosedQuantity,
    required this.method,
    required this.stopLossLimitPrice,
    required this.orderCreationTime,
    required this.minutes,
    required this.positionCompKey,
  });

  factory LatestOrder.fromJson(Map<String, dynamic> json) {
    return LatestOrder(
      tradingSymbol: json['trading_symbol'],
      quantity: json['quantity'],
      orderType: json['order_type'],
      product: json['product'],
      limitPrice: (json['limit_price'] as num).toDouble(),
      triggerPrice: (json['trigger_price'] as num).toDouble(),
      zenOrderId: json['zen_order_id'],
      orderVariety: json['order_variety'],
      validity: json['validity'],
      currency: json['currency'],
      exchange: json['exchange'],
      transactionType: json['transaction_type'],
      validityTtl: json['validity_ttl'],
      disclosedQuantity: json['disclosed_quantity'],
      method: json['method'],
      stopLossLimitPrice: (json['stop_loss_limit_price'] as num).toDouble(),
      orderCreationTime: DateTime.parse(json['order_creation_time']),
      minutes: json['minutes'],
      positionCompKey:
          PositionCompKey.fromJson(json['position_comp_key']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'trading_symbol': tradingSymbol,
      'quantity': quantity,
      'order_type': orderType,
      'product': product,
      'limit_price': limitPrice,
      'trigger_price': triggerPrice,
      'zen_order_id': zenOrderId,
      'order_variety': orderVariety,
      'validity': validity,
      'currency': currency,
      'exchange': exchange,
      'transaction_type': transactionType,
      'validity_ttl': validityTtl,
      'disclosed_quantity': disclosedQuantity,
      'method': method,
      'stop_loss_limit_price': stopLossLimitPrice,
      'order_creation_time': orderCreationTime.toIso8601String(),
      'minutes': minutes,
      'position_comp_key': positionCompKey.toJson(),
    };
  }

  @override
  String toString() {
    return 'LatestOrder('
        'tradingSymbol: $tradingSymbol, '
        'quantity: $quantity, '
        'orderType: $orderType, '
        'product: $product, '
        'limitPrice: $limitPrice, '
        'triggerPrice: $triggerPrice, '
        'zenOrderId: $zenOrderId, '
        'orderVariety: $orderVariety, '
        'validity: $validity, '
        'currency: $currency, '
        'exchange: $exchange, '
        'transactionType: $transactionType, '
        'validityTtl: $validityTtl, '
        'disclosedQuantity: $disclosedQuantity, '
        'method: $method, '
        'stopLossLimitPrice: $stopLossLimitPrice, '
        'orderCreationTime: $orderCreationTime, '
        'minutes: $minutes, '
        'positionCompKey: $positionCompKey'
        ')';
  }
}
