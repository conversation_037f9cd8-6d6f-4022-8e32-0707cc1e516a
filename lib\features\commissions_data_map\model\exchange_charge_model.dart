// MODEL: ExchangeChargesModel
class ExchangeChargeModel {
  final double sttCtt;
  final double transactionCharges;
  final double ipft;
  final double sebi;
  final double stampCharges;
  final double gst;

  ExchangeChargeModel({
    required this.sttCtt,
    required this.transactionCharges,
    required this.ipft,
    required this.sebi,
    required this.stampCharges,
    required this.gst,
  });

  factory ExchangeChargeModel.fromJson(Map<String, dynamic> json) {
    return ExchangeChargeModel(
      sttCtt: json['stt_ctt']?.toDouble() ?? 0.0,
      transactionCharges: json['transaction_charges']?.toDouble() ?? 0.0,
      ipft: json['ipft']?.toDouble() ?? 0.0,
      sebi: json['sebi']?.toDouble() ?? 0.0,
      stampCharges: json['stamp_charges']?.toDouble() ?? 0.0,
      gst: json['gst']?.toDouble() ?? 0.0,
    );
  }


  Map<String, dynamic> toJson() {
    return {
      'stt_ctt': sttCtt,
      'transaction_charges': transactionCharges,
      'ipft': ipft,
      'sebi': sebi,
      'stamp_charges': stampCharges,
      'gst': gst,
    };
  }

  @override
  String toString() {
    return 'ExchangeChargeModel(sttCtt: $sttCtt, transactionCharges: $transactionCharges, ipft: $ipft, sebi: $sebi, stampCharges: $stampCharges, gst: $gst)';
  }
}
