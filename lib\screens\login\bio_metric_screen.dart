import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class BioMetricScreen extends StatelessWidget {
  final VoidCallback checkBiometric;

  const BioMetricScreen({super.key, required this.checkBiometric});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        backgroundColor: ThemeConstants.backgroundColor,
        body: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              const Spacer(flex: 3), // Push icon to upper center
              Center(
                child: Icon(
                  Icons.fingerprint,
                  size: 100,
                  color: ThemeConstants.netWorthAmountColor,
                ),
              ),
              const Spacer(flex: 4), // Push button to bottom
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 24.0),
                child: Sized<PERSON><PERSON>(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: checkBiometric,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ThemeConstants.blue,
                      elevation: 4,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      shadowColor: ThemeConstants.blue.withOpacity(0.4),
                    ),
                    child: const Text(
                      'Authenticate',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
