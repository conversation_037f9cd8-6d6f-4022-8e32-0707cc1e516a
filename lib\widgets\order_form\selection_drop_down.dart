import 'package:flutter/material.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class SelectionDropdown<T> extends StatelessWidget {
  final String hint;
  final T? selectedValue;
  final List<T> items;
  final String Function(T) getItemLabel;
  final void Function(T?) onChanged;

  final TextStyle? itemTextStyle;
  final BoxDecoration? dropdownDecoration;

  const SelectionDropdown({
    super.key,
    required this.hint,
    required this.selectedValue,
    required this.items,
    required this.getItemLabel,
    required this.onChanged,
    this.itemTextStyle,
    this.dropdownDecoration,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        final effectiveItemTextStyle = itemTextStyle ?? TextStyle(
          fontSize: 15,
          color: AppTheme.textPrimary(themeState.isDarkMode),
          fontWeight: FontWeight.w400,
        );
        
        final effectiveDropdownDecoration = dropdownDecoration ?? BoxDecoration(
          color: AppTheme.cardColor(themeState.isDarkMode),
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          border: Border.all(
            color: AppTheme.borderColor(themeState.isDarkMode),
            width: 1,
          ),
        );

        return Container(
          alignment: Alignment.center,
          height: 40,
          width: MediaQuery.of(context).size.width * 0.445,
          decoration: effectiveDropdownDecoration,
          child: Center(
            child: DropdownButtonHideUnderline(
              child: DropdownButton2<T>(
                hint: Text(
                  hint,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                isExpanded: true,
                items: items.map((T item) {
                  return DropdownMenuItem<T>(
                    value: item,
                    child: Text(
                      getItemLabel(item),
                      overflow: TextOverflow.ellipsis,
                      style: effectiveItemTextStyle,
                    ),
                  );
                }).toList(),
                value: selectedValue,
                onChanged: onChanged,
                buttonStyleData: const ButtonStyleData(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  height: 100,
                  width: 400,
                ),
                dropdownStyleData: DropdownStyleData(
                  decoration: effectiveDropdownDecoration,
                  maxHeight: 500,
                  width: 200,
                ),
                menuItemStyleData: const MenuItemStyleData(
                  height: 40,
                ),
                iconStyleData: IconStyleData(
                  icon: Icon(Icons.keyboard_arrow_down),
                  iconEnabledColor: AppTheme.textSecondary(themeState.isDarkMode),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
