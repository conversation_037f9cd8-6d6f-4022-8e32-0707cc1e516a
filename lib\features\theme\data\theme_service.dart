import 'package:phoenix/services/shared_prefrences_service.dart';

class ThemeService {
  static const String _themeKey = 'theme_mode';
  
  // Get saved theme preference (true for dark, false for light)
  static bool getThemeMode() {
    return SharedPreferencesService.getData(_themeKey) ?? true; // Default to dark mode
  }
  
  // Save theme preference
  static Future<void> setThemeMode(bool isDarkMode) async {
    await SharedPreferencesService.setData(_themeKey, isDarkMode);
  }
}