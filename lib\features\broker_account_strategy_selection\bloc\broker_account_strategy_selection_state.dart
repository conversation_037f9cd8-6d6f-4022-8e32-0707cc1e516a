part of 'broker_account_strategy_selection_bloc.dart';

class BrokerAccountStrategyState {
  final List<BrokerInfo> brokers;
  final BrokerInfo? selectedBroker;
  final List<AccountInfo> availableAccounts;
  final AccountInfo? selectedAccount;
  final List<Strategy> availableStrategies;
  final Strategy? selectedStrategy;

  BrokerAccountStrategyState({
    required this.brokers,
    this.selectedBroker,
    required this.availableAccounts,
    this.selectedAccount,
    required this.availableStrategies,
    this.selectedStrategy,
  });

  BrokerAccountStrategyState copyWith({
    BrokerInfo? selectedBroker,
    List<AccountInfo>? availableAccounts,
    AccountInfo? selectedAccount,
    List<Strategy>? availableStrategies,
    Strategy? selectedStrategy,
  }) {
    return BrokerAccountStrategyState(
      brokers: brokers,
      selectedBroker: selectedBroker ?? this.selectedBroker,
      availableAccounts: availableAccounts ?? this.availableAccounts,
      selectedAccount: selectedAccount ?? this.selectedAccount,
      availableStrategies: availableStrategies ?? this.availableStrategies,
      selectedStrategy: selectedStrategy ?? this.selectedStrategy,
    );
  }
}
