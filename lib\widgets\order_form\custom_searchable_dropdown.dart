import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';

class CustomSearchableDropdown extends StatefulWidget {
  final String type;
  final ValueChanged<SecurityModel?> onSelected;
  final String hintText;

  CustomSearchableDropdown({
    required this.onSelected,
    this.hintText = 'Select an item',
    required this.type,
  });

  @override
  _CustomSearchableDropdownState createState() =>
      _CustomSearchableDropdownState();
}

class _CustomSearchableDropdownState extends State<CustomSearchableDropdown> {
  //SecurityModel selectedValue = SecurityModel(zenId: 90360, tradingSymbol: "TCS", strike: 0.0, exchange: "NSE", lotSize: 1, instrumentType: "EQ");

  SecurityModel? selectedValue;
  void _navigateToSearchPage() async {
    final SecurityModel? result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SearchPage(type: widget.type),
      ),
    );

    if (result != null) {
      setState(() {
        selectedValue = result;
      });
      widget.onSelected(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: _navigateToSearchPage,
          child: Container(
            padding: const EdgeInsets.all(2),
            alignment: Alignment.center,
            height: 40,
            width: 205,
            decoration: BoxDecoration(
              color: AppTheme.cardColor(themeState.isDarkMode),
              border: Border.all(
                color: AppTheme.borderColor(themeState.isDarkMode),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      selectedValue == null
                          ? widget.hintText
                          : selectedValue!.tradingSymbol,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 16,
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: Icon(
                    Icons.arrow_drop_down,
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

//Search page
class SearchPage extends StatefulWidget {
  final SecurityModel? selectedValue;
  final String type;

  SearchPage({this.selectedValue, required this.type});

  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  String searchQuery = '';
  List<SecurityModel> filteredItems = [];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          appBar: AppBar(
            backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
            foregroundColor: AppTheme.textPrimary(themeState.isDarkMode),
            title: TextField(
              autofocus: true,
              style: TextStyle(
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                  fontFamily: 'CupertinoSystemText',
                  fontSize: 18),
              decoration: InputDecoration(
                hintText: 'Search...',
                hintStyle: TextStyle(
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                ),
                border: InputBorder.none,
              ),
              onChanged: _filterItems,
            ),
          ),
          body: BlocBuilder<SecurityListBloc, SecurityListState>(
            builder: (context, state) {
              if (state is SecurityListLoaded) {
                // Initialize filteredItems based on the type
                final List<SecurityModel> items =
                    widget.type == 'equity' ? state.equityList : state.featuresList;
                // Initialize filteredItems with all securities when the state is loaded
                if (filteredItems.isEmpty) {
                  filteredItems = items;
                }

                return ListView.builder(
                  itemCount: filteredItems.length,
                  itemBuilder: (context, index) {
                    final security = filteredItems[index];
                    return ListTile(
                      titleAlignment: ListTileTitleAlignment.center,
                      title: Text(
                        security.tradingSymbol,
                        style: TextStyle(
                            color: AppTheme.textPrimary(themeState.isDarkMode),
                            fontFamily: 'CupertinoSystemText',
                            fontWeight: FontWeight.w400),
                      ),
                      onTap: () {
                        // Return the selected SecurityModel to the caller
                        Navigator.of(context).pop(security);
                      },
                    );
                  },
                );
              } else if (state is SecurityListLoading) {
                return Center(
                  child: CircularProgressIndicator(
                    color: AppTheme.primaryColor(themeState.isDarkMode),
                  ),
                );
              } else if (state is SecurityListError) {
                return Center(
                  child: Text(
                    'Error loading securities',
                    style: TextStyle(
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                  ),
                );
              } else {
                return Center(
                  child: Text(
                    'No data available',
                    style: TextStyle(
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                  ),
                );
              }
            },
          ),
        );
      },
    );
  }

  void _filterItems(String query) {
    setState(() {
      searchQuery = query;
      if (query.isEmpty) {
        // If the query is empty, show all items
        // If the query is empty, show all items
        final state =
            context.read<SecurityListBloc>().state as SecurityListLoaded;
        filteredItems =
            widget.type == 'equity' ? state.equityList : state.featuresList;
      } else {
        // Filter the list based on tradingSymbol
        final state =
            context.read<SecurityListBloc>().state as SecurityListLoaded;
        final items =
            widget.type == 'equity' ? state.equityList : state.featuresList;
        filteredItems = items
            .where((security) => security.tradingSymbol
                .toLowerCase()
                .contains(query.toLowerCase()))
            .toList();
      }
    });
  }
}
