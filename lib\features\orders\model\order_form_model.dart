//Model to maintain the order form data

import 'package:phoenix/features/orders/model/order_type.dart';

class OrderFormModel {
  final int clientId;
  final int accountId;
  final int strategyId;
  final String broker;
  final String exchange;
  final String transactionType;
  final int quantity;
  final String product;
  final String validity;
  final int? minutes;
  final double? stopLossLimitPrice;
  final double? triggerPrice;
  final double? limitPrice;
  final double? activationPoints;
  final double? trailingTriggerPoints;
  final double? trailingLimitPoints;

  String orderType;
  String methodType;

  final int zenId;

  final int? zenOrderId;

  //for validations
  final bool? isMarket;
  final bool? isStoplossEnabled;
  final bool? isTrailingStoplossEnabled;
  //M L
  final String? stoplossType;

  OrderFormModel(
      {required this.clientId,
      required this.accountId,
      required this.strategyId,
      required this.broker,
      required this.exchange,
      required this.transactionType,
      required this.quantity,
      required this.product,
      required this.validity,
      this.minutes,
      this.stopLossLimitPrice,
      this.triggerPrice,
      this.limitPrice,
      this.activationPoints,
      this.trailingTriggerPoints,
      this.trailingLimitPoints,
      this.orderType = OrderType.MARKET,
      required this.methodType,
      required this.zenId,
      this.isMarket,
      this.isStoplossEnabled,
      this.isTrailingStoplossEnabled,
      this.stoplossType,
      this.zenOrderId});

  /// Method to convert an instance to JSON
  Map<String, dynamic> toJson() {
    return {
      "client_id": clientId,
      "account_id": accountId,
      "strategy_id": strategyId,
      "broker": broker,
      "exchange": exchange,
      "transaction_type": transactionType,
      "quantity": quantity,
      "product": product,
      "validity": validity,
      "minutes": null,
      "stop_loss_limit_price": stopLossLimitPrice,
      "trigger_price": triggerPrice,
      "limit_price": limitPrice,
      "activation_points": activationPoints,
      "trailing_trigger_points": trailingTriggerPoints,
      "trailing_limit_points": trailingLimitPoints,
      "order_type": orderType,
      "method_type": methodType,
      "zen_id": zenId,
      "zen_order_id":zenOrderId,
    };
  }

  dynamic operator [](String key) {
    if (key == 'isMarket') {
      return isMarket;
    }

    if (key == 'isStoplossEnabled') {
      return isStoplossEnabled;
    }

    if (key == 'isTrailingStoplossEnabled') {
      return isTrailingStoplossEnabled;
    }

    throw ArgumentError('Invalid key: $key');
  }

  @override
  String toString() {
    return 'OrderFormModel{'
        'clientId: $clientId, '
        'accountId: $accountId, '
        'strategyId: $strategyId, '
        'broker: $broker, '
        'exchange: $exchange, '
        'transactionType: $transactionType, '
        'quantity: $quantity, '
        'product: $product, '
        'validity: $validity, '
        'minutes: $minutes, '
        'stopLossLimitPrice: $stopLossLimitPrice, '
        'triggerPrice: $triggerPrice, '
        'limitPrice: $limitPrice, '
        'activationPoints: $activationPoints, '
        'trailingTriggerPoints: $trailingTriggerPoints, '
        'trailingLimitPoints: $trailingLimitPoints, '
        'orderType: $orderType, '
        'methodType: $methodType, '
        'zenId: $zenId, '
        'isMarket: $isMarket, '
        'isStoplossEnabled: $isStoplossEnabled, '
        'isTrailingStoplossEnabled: $isTrailingStoplossEnabled, '
        'stoplossType: $stoplossType'
        'zenOrderId: $zenOrderId'
        '}';
  }
}

class OrderFormModelForBucket {
  final int accountId;
  final int strategyId;
  final String broker;
  final String exchange;
  final String transactionType;
  final int quantity;
  final String product;
  final String validity;
  final int? minutes;
  final double? stopLossLimitPrice;
  final double? triggerPrice;
  final double? limitPrice;
  final double? activationPoints;
  final double? trailingTriggerPoints;
  final double? trailingLimitPoints;

  String orderType;


  final int zenId;

  final int? zenOrderId;

  //for validations
  final bool? isMarket;
  final bool? isStoplossEnabled;
  final bool? isTrailingStoplossEnabled;
  //M L
  final String? stoplossType;

  OrderFormModelForBucket(
      {
      required this.accountId,
      required this.strategyId,
      required this.broker,
      required this.exchange,
      required this.transactionType,
      required this.quantity,
      required this.product,
      required this.validity,
      this.minutes,
      this.stopLossLimitPrice,
      this.triggerPrice,
      this.limitPrice,
      this.activationPoints,
      this.trailingTriggerPoints,
      this.trailingLimitPoints,
      this.orderType = OrderType.MARKET,
      
      required this.zenId,
      this.isMarket,
      this.isStoplossEnabled,
      this.isTrailingStoplossEnabled,
      this.stoplossType,
      this.zenOrderId});

  /// Method to convert an instance to JSON
  Map<String, dynamic> toJson() {
    return {
      
      "account_id": accountId,
      "strategy_id": strategyId,
      "broker": broker,
      "exchange": exchange,
      "transaction_type": transactionType,
      "quantity": quantity,
      "product": product,
      "validity": validity,
      "minutes": null,
      "stop_loss_limit_price": stopLossLimitPrice,
      "trigger_price": triggerPrice,
      "limit_price": limitPrice,
      "activation_points": activationPoints,
      "trailing_trigger_points": trailingTriggerPoints,
      "trailing_limit_points": trailingLimitPoints,
      "order_type": orderType,
      "zen_id": zenId,
      "zen_order_id":zenOrderId,
    };
  }

  dynamic operator [](String key) {
    if (key == 'isMarket') {
      return isMarket;
    }

    if (key == 'isStoplossEnabled') {
      return isStoplossEnabled;
    }

    if (key == 'isTrailingStoplossEnabled') {
      return isTrailingStoplossEnabled;
    }

    throw ArgumentError('Invalid key: $key');
  }

  @override
  String toString() {
    return 'OrderFormModel{'
        'accountId: $accountId, '
        'strategyId: $strategyId, '
        'broker: $broker, '
        'exchange: $exchange, '
        'transactionType: $transactionType, '
        'quantity: $quantity, '
        'product: $product, '
        'validity: $validity, '
        'minutes: $minutes, '
        'stopLossLimitPrice: $stopLossLimitPrice, '
        'triggerPrice: $triggerPrice, '
        'limitPrice: $limitPrice, '
        'activationPoints: $activationPoints, '
        'trailingTriggerPoints: $trailingTriggerPoints, '
        'trailingLimitPoints: $trailingLimitPoints, '
        'orderType: $orderType, '
        'zenId: $zenId, '
        'isMarket: $isMarket, '
        'isStoplossEnabled: $isStoplossEnabled, '
        'isTrailingStoplossEnabled: $isTrailingStoplossEnabled, '
        'stoplossType: $stoplossType'
        'zenOrderId: $zenOrderId'
        '}';
  }
}
