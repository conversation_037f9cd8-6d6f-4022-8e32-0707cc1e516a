//
//  Generated code. Do not modify.
//  source: req.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class ZenTicksEmptyRequest extends $pb.GeneratedMessage {
  factory ZenTicksEmptyRequest() => create();
  ZenTicksEmptyRequest._() : super();
  factory ZenTicksEmptyRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ZenTicksEmptyRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ZenTicksEmptyRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'websocket'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ZenTicksEmptyRequest clone() => ZenTicksEmptyRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ZenTicksEmptyRequest copyWith(void Function(ZenTicksEmptyRequest) updates) => super.copyWith((message) => updates(message as ZenTicksEmptyRequest)) as ZenTicksEmptyRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ZenTicksEmptyRequest create() => ZenTicksEmptyRequest._();
  ZenTicksEmptyRequest createEmptyInstance() => create();
  static $pb.PbList<ZenTicksEmptyRequest> createRepeated() => $pb.PbList<ZenTicksEmptyRequest>();
  @$core.pragma('dart2js:noInline')
  static ZenTicksEmptyRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ZenTicksEmptyRequest>(create);
  static ZenTicksEmptyRequest? _defaultInstance;
}


const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
