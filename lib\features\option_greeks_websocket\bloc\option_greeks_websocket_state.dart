part of 'option_greeks_websocket_bloc.dart';

@immutable
abstract class OptionGreeksWebSocketState {
  final bool isConnected;
  final List<ZenOptionGreeks>? optionGreeksList;
  final String? error;

  const OptionGreeksWebSocketState({
    required this.isConnected,
    this.optionGreeksList,
    this.error,
  });
}

class OptionGreeksWebSocketInitial extends OptionGreeksWebSocketState {
  const OptionGreeksWebSocketInitial() : super(isConnected: false);
}

class OptionGreeksWebSocketConnecting extends OptionGreeksWebSocketState {
  const OptionGreeksWebSocketConnecting() : super(isConnected: false);
}

class OptionGreeksWebSocketConnected extends OptionGreeksWebSocketState {
  const OptionGreeksWebSocketConnected(List<ZenOptionGreeks> optionGreeksList)
      : super(isConnected: true, optionGreeksList: optionGreeksList);
}

class OptionGreeksWebSocketDataReceived extends OptionGreeksWebSocketState {
  const OptionGreeksWebSocketDataReceived(List<ZenOptionGreeks> optionGreeksList)
      : super(isConnected: true, optionGreeksList: optionGreeksList);
}

class OptionGreeksWebSocketUnderlyingSelected extends OptionGreeksWebSocketState {
  final List<ZenOptionGreeks> filteredGreeks;
  const OptionGreeksWebSocketUnderlyingSelected(this.filteredGreeks) 
      : super(isConnected: true, optionGreeksList: filteredGreeks);
}

class OptionGreeksWebSocketError extends OptionGreeksWebSocketState {
  const OptionGreeksWebSocketError(String error) : super(isConnected: false, error: error);
}

class OptionGreeksWebSocketDisconnectedState extends OptionGreeksWebSocketState {
  const OptionGreeksWebSocketDisconnectedState({required super.isConnected});
}