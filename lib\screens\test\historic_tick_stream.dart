import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class HistoricTickStream extends StatefulWidget {
  const HistoricTickStream({super.key});

  @override
  State<HistoricTickStream> createState() => _HistoricTickStreamState();
}

class _HistoricTickStreamState extends State<HistoricTickStream>
    with WidgetsBindingObserver {
  WebSocketChannel? _channel;
  bool isConnecting = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this); // Observe app lifecycle changes
    _startConnection();
  }

  void _startConnection() {
    if (isConnecting || _channel != null) return; // Avoid duplicate connections
    isConnecting = true;

    try {
      debugPrint("Connecting to WebSocket...");
      _channel = IOWebSocketChannel.connect(
        Uri.parse('ws://************:8766/'),
      );
      if (mounted) {
        setState(() {});
      }
      debugPrint("WebSocket connected.");
    } catch (e) {
      debugPrint("WebSocket connection error: $e");
      _attemptReconnect();
    } finally {
      isConnecting = false;
    }
  }

  void _stopConnection() {
    _channel!.sink.close();
  }

  void _attemptReconnect() {
    if (isConnecting || !mounted) return; // Avoid multiple attempts
    debugPrint("Attempting to reconnect...");
    Future.delayed(const Duration(seconds: 0), () {
      if (mounted) {
        _startConnection();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(25.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 24),
          StreamBuilder(
            stream: _channel?.stream,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Text(
                  "Connecting...",
                  style: TextStyle(color: Colors.yellow),
                );
              }

              if (snapshot.hasError) {
                debugPrint("Error in WebSocket stream: ${snapshot.error}");
                return Text(
                  "Error: ${snapshot.error}",
                  style: const TextStyle(color: Colors.red),
                );
              }
              try {
                var data = jsonDecode(snapshot.data);
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      snapshot.hasData
                          ? 'Infy Price : ${data['ohlc']['close']} '
                          : 'No data received yet.',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Text(
                      snapshot.hasData
                          ? 'Time : ${data['time']}'
                          : 'No data received yet.',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ],
                );
              } catch (e) {
                return const Text(
                  'Invalid data format received.',
                  style: TextStyle(color: Colors.red, fontSize: 14),
                );
              }
            },
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  @override
  void dispose() {
    print("Called dispose");
    _stopConnection();
    WidgetsBinding.instance.removeObserver(this); // Remove observer
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.paused) {
      // App goes to the background
      debugPrint("App moved to background. Closing WebSocket...");
      _stopConnection();
    } else if (state == AppLifecycleState.resumed) {
      // App comes back to the foreground
      debugPrint("App resumed. Reconnecting WebSocket...");
      _startConnection();
    }
  }
}
