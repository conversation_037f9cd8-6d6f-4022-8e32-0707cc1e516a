import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';
import 'package:phoenix/features/fetch_clients_data/bloc/clients_data_bloc.dart';
import 'package:phoenix/features/fetch_clients_data/model/client_data.dart';
import 'package:phoenix/features/margin_state/bloc/margin_state_bloc.dart';
import 'package:phoenix/features/orders_state/bloc/orders_state_bloc.dart';
import 'package:phoenix/features/pnl/bloc/pnl_bloc.dart';
import 'package:phoenix/features/portfolio_data/bloc/portfolio_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/order_form/selection_drop_down.dart';

///Clients Data selection page
///
class ClientSelectionWidget extends StatefulWidget {
  final int userClientId;
  final Function(int)? onClientChanged;
  final bool isCompact;

  const ClientSelectionWidget({
    super.key,
    required this.userClientId,
    this.onClientChanged,
    this.isCompact = false,
  });

  @override
  _ClientSelectionWidgetState createState() => _ClientSelectionWidgetState();
}

class _ClientSelectionWidgetState extends State<ClientSelectionWidget> {
  int? selectedClientId;

  @override
  void initState() {
    super.initState();
    selectedClientId = widget.userClientId;

    context.read<ClientsDataBloc>().add(FetchClientsData());
  }

  void _fetchMarginDataForClient(int clientId) {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      final user = authState.credentialsModel;

      // If the clientId matches the current user's clientId, use their account IDs
      if (clientId == user.clientId) {
        List<int> accountIds = [];

        // Collect all account IDs from all brokers for the current user
        for (var broker in user.brokers) {
          for (var account in broker.accounts) {
            accountIds.add(account.accountId);
          }
        }

        if (accountIds.isNotEmpty) {
          context.read<MarginBloc>().add(
            MarginFetchMultipleEvent(clientId, accountIds),
          );
        }
      } else {
        // For different clients, we need to trigger a client change first
        // which will update the credentials with the new client's broker/account info
        context.read<AuthBloc>().add(
          ClientChangeEvent(clientId: clientId),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        if (widget.isCompact) {
          return _buildCompactView(themeState);
        }

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: themeState.isDarkMode
                  ? [Colors.grey[850]!, Colors.grey[800]!]
                  : [Colors.white, Colors.grey[50]!],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppTheme.borderColor(themeState.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: themeState.isDarkMode
                    ? Colors.black.withOpacity(0.2)
                    : Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.person_outline,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Client Selection',
                    style: TextStyle(
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Client dropdown and default button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  BlocBuilder<ClientsDataBloc, ClientsDataState>(
                    builder: (context, state) {
                      if (state is ClientsDataLoading) {
                        return Transform.scale(
                          scale: 0.5,
                          child: CircularLoader(),
                        );
                      } else if (state is ClientsDataLoaded) {
                        final ClientData selectedClient =
                            state.clientsList.isNotEmpty
                                ? state.clientsList.firstWhere(
                                    (element) =>
                                        element.clientId == selectedClientId,
                                    orElse: () => state.clientsList.first,
                                  )
                                : throw Exception("Clients list is empty");

                        return SelectionDropdown<ClientData>(
                          hint: "Account",
                          selectedValue: selectedClient,
                          items: state.clientsList,
                          getItemLabel: (val) => val.clientName,
                          onChanged: (id) {
                            setState(() {
                              selectedClientId = id?.clientId;
                            });
                            // Fetch margin data for the new client
                            if (id?.clientId != null) {
                              _fetchMarginDataForClient(id!.clientId);
                              // The AuthBloc listener will handle parent refresh
                              // after client change is complete
                            }
                          },
                          dropdownDecoration: BoxDecoration(
                            color: ThemeConstants.getDropdownBackgroundColor(
                                themeState.isDarkMode),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: ThemeConstants.getDropdownBorderColor(
                                    themeState.isDarkMode)),
                          ),
                          itemTextStyle: TextStyle(
                            color:
                                AppTheme.textSecondary(themeState.isDarkMode),
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                          ),
                        );
                      } else {
                        return GestureDetector(
                          child: Row(
                            children: [
                              Text(
                                "Network error, please try again",
                                style: TextStyle(
                                  color: AppTheme.textPrimary(
                                      themeState.isDarkMode),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Icon(Icons.refresh,
                                  color: AppTheme.primaryColor(
                                      themeState.isDarkMode)),
                            ],
                          ),
                          onTap: () {
                            context
                                .read<ClientsDataBloc>()
                                .add(FetchClientsData());
                          },
                        );
                      }
                    },
                  ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: selectedClientId == widget.userClientId
                          ? LinearGradient(
                              colors: [
                                Colors.green.withOpacity(0.1),
                                Colors.green.withOpacity(0.05),
                              ],
                            )
                          : LinearGradient(
                              colors: [
                                Colors.blue.withOpacity(0.1),
                                Colors.blue.withOpacity(0.05),
                              ],
                            ),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: selectedClientId == widget.userClientId
                            ? Colors.green.withOpacity(0.3)
                            : Colors.blue.withOpacity(0.3),
                      ),
                    ),
                    child: TextButton(
                      onPressed: selectedClientId == widget.userClientId
                          ? null
                          : () {
                            //to refresh the data once the account changes
                            BlocProvider.of<AuthBloc>(context).add(
                              ClientChangeEvent(clientId: selectedClientId!),
                            );
                            BlocProvider.of<PortfolioBloc>(context).add(
                              FetchPortfolio(selectedClientId!),
                            );
                            BlocProvider.of<OrdersStateBloc>(context).add(
                              FetchOrdersState(clientId: selectedClientId!),
                            );
                            BlocProvider.of<PnlBloc>(context).add(
                              FetchPnlData(selectedClientId!),
                            );
                            // The AuthBloc listener will handle margin data refresh
                            // after the client change is complete
                          },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 4,
                      children: [
                        if (selectedClientId == widget.userClientId)
                          Icon(
                            Icons.check,
                            color: Colors.green,
                            size: 15,
                          ),
                        Text(
                          selectedClientId == widget.userClientId
                              ? 'Default'
                              : 'Set as Default',
                          style: TextStyle(
                            color: selectedClientId == widget.userClientId
                                ? Colors.green
                                : AppTheme.primaryColor(themeState.isDarkMode),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ),
                ],
              ),

            ],
          ),
        );
      },
    );
  }

  Widget _buildCompactView(ThemeState themeState) {
    return BlocBuilder<ClientsDataBloc, ClientsDataState>(
      builder: (context, state) {
        if (state is ClientsDataLoading) {
          return Container(
            height: 40,
            child: Center(
              child: Transform.scale(
                scale: 0.7,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.textSecondary(themeState.isDarkMode),
                  ),
                ),
              ),
            ),
          );
        } else if (state is ClientsDataLoaded) {
          final ClientData selectedClient = state.clientsList.isNotEmpty
              ? state.clientsList.firstWhere(
                  (element) => element.clientId == selectedClientId,
                  orElse: () => state.clientsList.first,
                )
              : throw Exception("Clients list is empty");

          return Row(
            children: [
              Expanded(
                child: SelectionDropdown<ClientData>(
                  hint: "Select Client",
                  selectedValue: selectedClient,
                  items: state.clientsList,
                  getItemLabel: (val) => val.clientName,
                  onChanged: (id) {
                    setState(() {
                      selectedClientId = id?.clientId;
                    });
                    if (id?.clientId != null) {
                      _fetchMarginDataForClient(id!.clientId);
                    }
                  },
                  dropdownDecoration: BoxDecoration(
                    color: ThemeConstants.getDropdownBackgroundColor(themeState.isDarkMode),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: ThemeConstants.getDropdownBorderColor(themeState.isDarkMode),
                    ),
                  ),
                  itemTextStyle: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              // const SizedBox(width: 12),
              // Container(
              //   decoration: BoxDecoration(
              //     gradient: selectedClientId == widget.userClientId
              //         ? LinearGradient(
              //             colors: [
              //               Colors.green.withOpacity(0.1),
              //               Colors.green.withOpacity(0.05),
              //             ],
              //           )
              //         : LinearGradient(
              //             colors: [
              //               Colors.blue.withOpacity(0.1),
              //               Colors.blue.withOpacity(0.05),
              //             ],
              //           ),
              //     borderRadius: BorderRadius.circular(6),
              //     border: Border.all(
              //       color: selectedClientId == widget.userClientId
              //           ? Colors.green.withOpacity(0.3)
              //           : Colors.blue.withOpacity(0.3),
              //     ),
              //   ),
              //   child: TextButton(
              //     onPressed: selectedClientId == widget.userClientId
              //         ? null
              //         : () {
              //             BlocProvider.of<AuthBloc>(context).add(
              //               ClientChangeEvent(clientId: selectedClientId!),
              //             );
              //             BlocProvider.of<PortfolioBloc>(context).add(
              //               FetchPortfolio(selectedClientId!),
              //             );
              //             BlocProvider.of<OrdersStateBloc>(context).add(
              //               FetchOrdersState(clientId: selectedClientId!),
              //             );
              //             BlocProvider.of<PnlBloc>(context).add(
              //               FetchPnlData(selectedClientId!),
              //             );
              //           },
              //     child: Row(
              //       mainAxisSize: MainAxisSize.min,
              //       children: [
              //         if (selectedClientId == widget.userClientId)
              //           Icon(
              //             Icons.check,
              //             color: Colors.green,
              //             size: 14,
              //           ),
              //         Text(
              //           selectedClientId == widget.userClientId ? 'Default' : 'Set Default',
              //           style: TextStyle(
              //             color: selectedClientId == widget.userClientId
              //                 ? Colors.green
              //                 : AppTheme.primaryColor(themeState.isDarkMode),
              //             fontSize: 12,
              //           ),
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
            ],
          );
        } else {
          return Container(
            height: 40,
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  "Error loading clients",
                  style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }
}
