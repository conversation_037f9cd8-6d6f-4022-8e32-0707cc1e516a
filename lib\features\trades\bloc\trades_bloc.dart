import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/trades/bloc/trades_event.dart';
import 'package:phoenix/features/trades/bloc/trades_state.dart';
import 'package:phoenix/features/trades/data/trades_data_provider.dart';
import 'package:phoenix/utils/app_exception.dart';

class TradesBloc extends Bloc<TradesEvent, TradesState> {
  final TradesDataProvider _tradesDataProvider;

  TradesBloc(this._tradesDataProvider) : super(TradesInitial()) {
    on<FetchTradesData>(_onFetchTradesData);
    on<TradesInitializeEvent>(_onTradesInitialize);
  }

  void _onFetchTradesData(FetchTradesData event, Emitter<TradesState> emit) async {
    emit(TradesLoading());
    try {
      final tradesData = await _tradesDataProvider.fetchTradesData(
        event.clientId,
        startTimestamp: event.startTimestamp,
        endTimestamp: event.endTimestamp,
      );
      emit(TradesLoaded(tradesData));
    } on AppException catch (e) {
      emit(TradesError(e.message));
    } catch (e) {
      emit(TradesError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  void _onTradesInitialize(TradesInitializeEvent event, Emitter<TradesState> emit) {
    emit(TradesInitial());
  }
}