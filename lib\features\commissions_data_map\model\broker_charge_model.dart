// MODEL: BrokerChargesModel
class BrokerChargeModel {
  final double threshold;
  final double brokerageCharges;
  final double flatCharges;

  BrokerChargeModel({
    required this.threshold,
    required this.brokerageCharges,
    required this.flatCharges,
  });

  factory BrokerChargeModel.fromJson(Map<String, dynamic> json) {
    return BrokerChargeModel(
      threshold: json['threshold']?.toDouble() ?? 0.0,
      brokerageCharges: json['brokerage_charges']?.toDouble() ?? 0.0,
      flatCharges: json['flat_charges']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'threshold': threshold,
      'brokerage_charges': brokerageCharges,
      'flat_charges': flatCharges,
    };
  }

  @override
  String toString() {
    return 'BrokerChargeModel(threshold: $threshold, brokerageCharges: $brokerageCharges, flatCharges: $flatCharges)';
  }
}
