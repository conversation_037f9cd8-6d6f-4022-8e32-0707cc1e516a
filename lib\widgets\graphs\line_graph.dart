import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:phoenix/utils/util_functions.dart';

class LineGraph extends StatelessWidget {
  const LineGraph({
    super.key,
    required this.chartPoints,
    required this.baseDate,
    required this.lineColor,
  });

  final List<FlSpot> chartPoints;
  final DateTime baseDate;

  final Color lineColor;

  @override
  Widget build(BuildContext context) {
    return LineChart(
      curve: Curves.bounceInOut,
      LineChartData(
        gridData: FlGridData(show: false),
        titlesData: FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            fitInsideHorizontally: true,
            tooltipBgColor: Color(0xffF1EFEC),
            tooltipRoundedRadius: 6,
            getTooltipItems: (List<LineBarSpot> touchedSpots) {
              return touchedSpots.map((spot) {
                final DateTime date =
                    baseDate.add(Duration(days: spot.x.toInt()));
                return LineTooltipItem(
                  "₹${UtilFunctions.formatIndianCurrencyforPnlGraph(spot.y)}",
                  const TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                  ),
                  children: [
                    const TextSpan(
                      text: "\n",
                    ),
                    TextSpan(
                      text: DateFormat('dd MMM yyyy').format(date),
                      style: const TextStyle(
                        color: Color.fromARGB(255, 134, 134, 134),
                        fontWeight: FontWeight.w400,
                        fontSize: 11,
                      ),
                    ),
                  ],
                );
              }).toList();
            },
          ),
          //this is to remove the vertical bar on the chart on hover
          getTouchedSpotIndicator: (barData, spotIndexes) {
            return spotIndexes.map((index) {
              return TouchedSpotIndicatorData(
                FlLine(
                  color: Colors.transparent, // Make the line invisible
                  strokeWidth: 0, // Ensure it takes no space
                ),
                FlDotData(
                  show: true, // Keep the circle visible
                ),
              );
              // Returning null here disables the vertical line and the dot on top
            }).toList();
          },
        ),
        lineBarsData: [
          LineChartBarData(
            preventCurveOverShooting: true,
            spots: chartPoints,
            isCurved: true,
            color: lineColor,
            barWidth: 1.8,
            isStrokeCapRound: true,
            curveSmoothness: 0.35,
            dotData: FlDotData(show: false),
            //this is to remove the area below the line
            belowBarData: BarAreaData(
              show: true,
              color: lineColor.withOpacity(0.1),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  lineColor.withOpacity(0.6),
                  lineColor.withOpacity(0.4),
                  lineColor.withOpacity(0.2),
                  lineColor.withOpacity(0.1),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ],
        minX: 0,
        maxX: chartPoints.length.toDouble() - 1,
        minY:
            chartPoints.fold(0.0, (min, spot) => spot.y < min! ? spot.y : min),
        maxY:
            chartPoints.fold(0.0, (max, spot) => spot.y > max! ? spot.y : max),
      ),
    );
  }
}
