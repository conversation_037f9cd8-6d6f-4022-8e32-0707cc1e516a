//
//  Generated code. Do not modify.
//  source: depth.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'order_info.pb.dart' as $0;

class Depth extends $pb.GeneratedMessage {
  factory Depth({
    $core.Iterable<$0.OrderInfo>? buy,
    $core.Iterable<$0.OrderInfo>? sell,
  }) {
    final $result = create();
    if (buy != null) {
      $result.buy.addAll(buy);
    }
    if (sell != null) {
      $result.sell.addAll(sell);
    }
    return $result;
  }
  Depth._() : super();
  factory Depth.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Depth.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Depth', package: const $pb.PackageName(_omitMessageNames ? '' : 'websocket'), createEmptyInstance: create)
    ..pc<$0.OrderInfo>(1, _omitFieldNames ? '' : 'buy', $pb.PbFieldType.PM, subBuilder: $0.OrderInfo.create)
    ..pc<$0.OrderInfo>(2, _omitFieldNames ? '' : 'sell', $pb.PbFieldType.PM, subBuilder: $0.OrderInfo.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Depth clone() => Depth()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Depth copyWith(void Function(Depth) updates) => super.copyWith((message) => updates(message as Depth)) as Depth;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Depth create() => Depth._();
  Depth createEmptyInstance() => create();
  static $pb.PbList<Depth> createRepeated() => $pb.PbList<Depth>();
  @$core.pragma('dart2js:noInline')
  static Depth getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Depth>(create);
  static Depth? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$0.OrderInfo> get buy => $_getList(0);

  @$pb.TagNumber(2)
  $core.List<$0.OrderInfo> get sell => $_getList(1);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
