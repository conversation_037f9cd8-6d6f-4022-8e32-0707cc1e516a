import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
//For this to work we need to do some changes in the local files downloaded.

import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/orders_state/model/unified_order_data.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';
import 'package:phoenix/widgets/tile/pnl_child.dart';
import 'package:phoenix/widgets/tile/positions_child.dart';
import 'package:phoenix/widgets/tile/orders_state_tile/unified_order_state_child.dart';

//I have done some unforgivable things and I deserve to bare this pain and need to because I am a bad person.

class TileGeneric extends StatelessWidget {
  final String tileType;
  final dynamic data; // Can be PositionModel or OrderModel
  final String filter; //type ltd dtd wtd mtd ytd in pnl

  AnimationController? formAnimeController;

  //streaming price
  final double? prices;
  final bool isOrderOpen;
  final BrokerAccountStrategyData? brokerAccountStrategyMapData;

   TileGeneric({
    super.key,
    required this.tileType,
    required this.data,
    this.formAnimeController,
    this.prices,
    this.isOrderOpen = false,
    this.filter = 'latest',
    this.brokerAccountStrategyMapData,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      color: Color(0xFFD9D9D9),
      child: _getChildWidget(),
    );
  }

  Widget _getChildWidget() {
    final widgetMap = {
      'position': () => PositionsChild(
            data: data as PositionsModel,
            prices: prices,
            brokerAccountStrategyMapData: brokerAccountStrategyMapData,
          ),
      'zenOrder': () => UnifiedOrderStateChild(
            data: data as UnifiedOrderData,
            prices: prices,
            animationController: formAnimeController as AnimationController,
            brokerAccountStrategyMapData: brokerAccountStrategyMapData,
            isOrderOpen: isOrderOpen,
          ),
      'pnl': () => PnlChild(
            data: data as PositionPnL,
            prices: prices,
            filter: filter, //ltd dtd wtd mtd ytd filter
            brokerAccountStrategyData: brokerAccountStrategyMapData,
          ),
    };

    return widgetMap[tileType]?.call() ??
        const SizedBox(); // Default to empty widget
  }
}
