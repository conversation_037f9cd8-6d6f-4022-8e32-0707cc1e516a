import 'package:phoenix/features/fetch_clients_data/data/provider/clients_provider.dart';
import 'package:phoenix/features/fetch_clients_data/model/client_data.dart';
import 'package:phoenix/utils/app_exception.dart';

class ClientsDataRepo {
  final ClientsProvider provider;

  ClientsDataRepo(this.provider);

  Future<List<ClientData>> getClients() async {
    try {
      // Use the provider to fetch client data
      final List<ClientData> clients = await provider.fetchClientData();
      return clients;
    } catch (e) {
      throw e is AppException ? e : AppException(e.toString());
    }
  }
}
