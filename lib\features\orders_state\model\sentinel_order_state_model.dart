import 'package:phoenix/features/common/position_comp_key.dart';

class SentinelOrderState {
  final PositionCompKey positionCompKey;
  final int internalOrderId;
  final String transactionType;
  final String orderType;
  final int totalQuantity;
  final int totalFilledQuantity;
  final int currentFilledQuantity;
  final double price;
  final String orderStatus;
  final String message;
  final DateTime exchangeTimestamp;
  final DateTime brokerTimestamp;
  final String tradingSymbol;
  final String exchange;
  final int zenOrderId;
  final double? triggerPrice;

  SentinelOrderState({
    required this.positionCompKey,
    required this.internalOrderId,
    required this.transactionType,
    required this.orderType,
    required this.totalQuantity,
    required this.totalFilledQuantity,
    required this.currentFilledQuantity,
    required this.price,
    required this.orderStatus,
    required this.message,
    required this.exchangeTimestamp,
    required this.brokerTimestamp,
    required this.tradingSymbol,
    required this.exchange,
    required this.zenOrderId,
    required this.triggerPrice,
  });

  factory SentinelOrderState.fromJson(Map<String, dynamic> json) {
    return SentinelOrderState(
      positionCompKey: PositionCompKey.from<PERSON>son(json['position_comp_key']),
      internalOrderId: json['internal_order_id'],
      transactionType: json['transaction_type'],
      orderType: json['order_type'],
      totalQuantity: json['total_quantity'],
      totalFilledQuantity: json['total_filled_quantity'],
      currentFilledQuantity: json['current_filled_quantity'],
      price: (json['price'] as num).toDouble(),
      orderStatus: json['order_status'],
      message: json['message'],
      exchangeTimestamp: DateTime.parse(json['exchange_timestamp']),
      brokerTimestamp: DateTime.parse(json['broker_timestamp']),
      tradingSymbol: json['trading_symbol'],
      exchange: json['exchange'],
      zenOrderId: json['zen_order_id'],
      triggerPrice: json['trigger_price'] != null
          ? (json['trigger_price'] as num).toDouble()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'position_comp_key': positionCompKey.toJson(),
      'internal_order_id': internalOrderId,
      'transaction_type': transactionType,
      'order_type': orderType,
      'total_quantity': totalQuantity,
      'total_filled_quantity': totalFilledQuantity,
      'current_filled_quantity': currentFilledQuantity,
      'price': price,
      'order_status': orderStatus,
      'message': message,
      'exchange_timestamp': exchangeTimestamp.toIso8601String(),
      'broker_timestamp': brokerTimestamp.toIso8601String(),
      'trading_symbol': tradingSymbol,
      'exchange': exchange,
      'zen_order_id': zenOrderId,
      'trigger_price': triggerPrice,
    };
  }
}
