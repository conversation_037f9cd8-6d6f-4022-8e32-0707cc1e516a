import 'package:phoenix/features/pnl/model/pnl_data_model.dart';
import 'package:fl_chart/fl_chart.dart';

enum PnLPeriodType {
  ltd,
  dtd,
  wtd,
  mtd,
  ytd,
}

class PnLGraphFormatter {
  static List<FlSpot> format({
    required List<PositionPnL> pnlList,
    required PnLPeriodType type,
    required bool isRealized,
  }) {
    final List<FlSpot> spots = [];

    if (pnlList.isEmpty) return spots;

    final DateTime baseDate = pnlList.first.date;

    for (final item in pnlList) {
      final data = isRealized ? item.realized : item.unRealized;
      final double yValue;

      switch (type) {
        case PnLPeriodType.ltd:
          yValue = data.latest;
          break;
        case PnLPeriodType.dtd:
          yValue = data.latest - data.sod;
          break;
        case PnLPeriodType.wtd:
          yValue = data.latest - data.sow;
          break;
        case PnLPeriodType.mtd:
          yValue = data.latest - data.som;
          break;
        case PnLPeriodType.ytd:
          yValue = data.latest - data.soy;
          break;
      }

      final double xValue = item.date.difference(baseDate).inDays.toDouble();
      spots.add(FlSpot(xValue, yValue));
    }

    return spots;
  }
}
