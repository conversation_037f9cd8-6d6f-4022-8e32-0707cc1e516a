import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:phoenix/features/commissions_data_map/model/broker_charge_model.dart';
import 'package:phoenix/features/commissions_data_map/model/exchange_charge_model.dart';
import 'package:phoenix/features/common/api_response_model.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/app_exception.dart';
import 'package:phoenix/utils/http_service.dart';

class CommissionsProvider {
  final customHttpService = HttpService();
  Future<dynamic> getCharges() async {
    debugPrint("Reached getCharges");
    try {
      // Make the POST request
      final response = await customHttpService.get(
        Uri.parse(ApiPath.getAllCharges()),
        headers: {'Content-Type': 'application/json'},
      );

      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if (apiResponse.code != 200 ||
          apiResponse.status != 'SUCCESS' ||
          response.statusCode != 200) {
        throw AppException(apiResponse.message);
      } else {
        // Decode the JSON response into a list of dynamic objects
        final jsonData = apiResponse.payload as Map<String, dynamic>;
        
      

      debugPrint("Commisions Data: $jsonData");

      final exchangeChargesMap = extractExchangeCharges(jsonData);
      final brokerChargesMap = extractBrokerCharges(jsonData);

      return {
        'exchangeCharges': exchangeChargesMap,
        'brokerCharges': brokerChargesMap,
      };
      }
    } catch (e) {
      // Handle any errors that may occur
      debugPrint('Error fetching UnifiedOrderState: $e');

      throw e is AppException
          ? e
          : AppException(
              'Failed to fetch Unified Order State: ${e.toString()}');
    }
  }

  //formatters repositroy things
  Map<String, ExchangeChargeModel> extractExchangeCharges(
      Map<String, dynamic> json) {
    Map<String, ExchangeChargeModel> exchangeCharges = {};

    for (var exchange in ['NSE', 'BSE']) {
      final exchangeData = json[exchange];
      if (exchangeData == null) continue;

      exchangeData.forEach((segment, segmentData) {
        segmentData.forEach((side, sideData) {
          sideData.forEach((orderType, chargeList) {
            if (chargeList is List && chargeList.isNotEmpty) {
              final model = ExchangeChargeModel.fromJson(chargeList[0]);
              final key = '$exchange|$segment|$side|$orderType';
              exchangeCharges[key] = model;
            }
          });
        });
      });
    }

    return exchangeCharges;
  }

  Map<String, BrokerChargeModel> extractBrokerCharges(
      Map<String, dynamic> json) {
    Map<String, BrokerChargeModel> brokerCharges = {};

    for (var broker in ['ZERODHA']) {
      final brokerData = json[broker];
      if (brokerData == null) continue;

      brokerData.forEach((segment, segmentData) {
        segmentData.forEach((orderType, chargeList) {
          if (chargeList is List && chargeList.isNotEmpty) {
            final model = BrokerChargeModel.fromJson(chargeList[0]);
            final key = '$broker|$segment|$orderType';
            brokerCharges[key] = model;
          }
        });
      });
    }

    return brokerCharges;
  }
}
