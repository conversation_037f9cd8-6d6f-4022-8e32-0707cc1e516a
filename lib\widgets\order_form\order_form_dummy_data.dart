abstract class OrderFormDummyData {
  static final List<String> securitiesList = [
    "R<PERSON><PERSON><PERSON><PERSON>",
    "TCS",
    "HDFCBANK",
    "ICICIBANK",
    "INFY",
    "HINDUNILVR",
    "SBIN",
    "BHARTIARTL",
    "KOTAKBANK",
    "ITC",
    "AXISBANK",
    "LT",
    "BAJFINANCE",
    "WIPRO",
    "ADANIENT",
    "ASIANPAINT",
    "ULTRACEMCO",
    "TATAS<PERSON>EL",
    "MARUTI",
    "SUNPHARMA",
    "POWERGRID",
    "NTPC",
    "HCLTECH",
    "TECHM",
    "DIVISLAB",
    "TITAN",
    "GRASIM",
    "COALINDIA",
    "ONGC",
    "BPCL"
  ];



  static final List<Map<String, dynamic>> securitiesWithZenId = [
    {"trading_symbol": "TCS", "zen_id": 90360},
    {"trading_symbol": "INFY", "zen_id": 88399},
    {"trading_symbol": "IDEA", "zen_id": 88290},

    {"trading_symbol": "HDFCBANK", "zen_id": 87491},
    {"trading_symbol": "APOLLO", "zen_id": 15619},
    {"trading_symbol": "BEL", "zen_id": 86827},
    {"trading_symbol": "RVNL", "zen_id": 89927},
    {"trading_symbol": "CDSL", "zen_id": 86920},

    {"trading_symbol": "GOLDBEES", "zen_id": 87301},
    {"trading_symbol": "APOLLOHOSP", "zen_id": 86686},
    {"trading_symbol": "HCLTECH", "zen_id": 87486},
    {"trading_symbol": "PTC", "zen_id": 34474},
    {"trading_symbol": "ADANIPORTS", "zen_id": 86599},
    {"trading_symbol": "TATASTEEL", "zen_id": 90341},
    {"trading_symbol": "ZOMATO", "zen_id": 91168},
    {"trading_symbol": "5PAISA", "zen_id": 83781},

    {"trading_symbol": "MRF", "zen_id": 88909},

  ];

  static final List<String> brokersList = ["ZEN_BROKER", "ZERODHA", "KOTAK"];

  static final Map<int,String> accountsList = {
    4:"Zen Test",
  };

  static final Map<int,String> strategiesList = {
    1 : "Default",
  };
}
