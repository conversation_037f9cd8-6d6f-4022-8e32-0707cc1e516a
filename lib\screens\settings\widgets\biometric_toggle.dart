import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/data/data_provider/biometric_service.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class BiometricToggle extends StatefulWidget {
  const BiometricToggle({Key? key}) : super(key: key);

  @override
  State<BiometricToggle> createState() => _BiometricToggleState();
}

class _BiometricToggleState extends State<BiometricToggle> {
  final BiometricService _biometricService = BiometricService();
  bool _isEnabled = false;
  bool _isAvailable = false;

  @override
  void initState() {
    super.initState();
    _checkBiometrics();
  }

  Future<void> _checkBiometrics() async {
    final available = await _biometricService.isBiometricsAvailable();
    final enabled = await _biometricService.isBiometricEnabled();
    setState(() {
      _isAvailable = available;
      _isEnabled = enabled;
    });
  }

  Future<void> _toggleBiometric(bool value) async {
    try {
      // If enabling biometrics
      if (value) {
        final authenticated = await _biometricService.authenticate();
        if (!authenticated) {
          // If authentication fails or is canceled, keep it disabled
          await _biometricService.setBiometricEnabled(false);
          setState(() => _isEnabled = false);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Biometric authentication failed or was canceled.'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }
      
      // Only reach here if authentication succeeded or we're disabling biometrics
      await _biometricService.setBiometricEnabled(value);
      setState(() => _isEnabled = value);
    } catch (e) {
      setState(() => _isEnabled = false); // Ensure toggle is reset on error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppTheme.borderColor(themeState.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: themeState.isDarkMode 
                    ? Colors.black.withOpacity(0.3)
                    : Colors.grey.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Biometric icon with background
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _isAvailable 
                      ? AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.15)
                      : Colors.red.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.fingerprint_rounded,
                  color: _isAvailable 
                      ? AppTheme.primaryColor(themeState.isDarkMode)
                      : Colors.red,
                  size: 20,
                ),
              ),
              const SizedBox(width: 14),
              // Title and subtitle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Biometric Authentication',
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.2,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _isAvailable 
                          ? (_isEnabled ? 'Enabled' : 'Disabled')
                          : 'Not available on this device',
                      style: TextStyle(
                        color: _isAvailable 
                            ? AppTheme.textSecondary(themeState.isDarkMode)
                            : Colors.red,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              // Modern switch
              Switch(
                value: _isEnabled,
                onChanged: _isAvailable ? _toggleBiometric : null,
                activeColor: AppTheme.primaryColor(themeState.isDarkMode),
                inactiveThumbColor: AppTheme.textSecondary(themeState.isDarkMode),
                inactiveTrackColor: AppTheme.textSecondary(themeState.isDarkMode).withOpacity(0.3),
              ),
            ],
          ),
        );
      },
    );
  }
}

