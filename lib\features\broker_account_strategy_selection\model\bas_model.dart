// This file contains the model for selected broker, account, and strategy

class BrokerInfo {
  final int brokerId;
  final String brokerName;
  final List<AccountInfo> accounts;

  BrokerInfo({required this.brokerId, required this.brokerName, required this.accounts});

  @override
  bool operator ==(Object other) =>
      identical(this, other) || (other is BrokerInfo && brokerId == other.brokerId);

  @override
  int get hashCode => brokerId.hashCode;

  @override
  String toString() {
    return 'BrokerInfo(brokerId: $brokerId, brokerName: $brokerName, accounts: $accounts)';
  }
}

class AccountInfo {
  final int accountId;
  final String accountName;
  final List<Strategy> strategies;

  AccountInfo({required this.accountId, required this.accountName, required this.strategies});

  @override
  bool operator ==(Object other) =>
      identical(this, other) || (other is AccountInfo && accountId == other.accountId);

  @override
  int get hashCode => accountId.hashCode;

  @override
  String toString() {
    return 'AccountInfo(accountId: $accountId, accountName: $accountName, strategies: $strategies)';
  }
}

class Strategy {
  final int strategyId;
  final String strategyName;

  Strategy({required this.strategyId, required this.strategyName});

  @override
  bool operator ==(Object other) =>
      identical(this, other) || (other is Strategy && strategyId == other.strategyId);

  @override
  int get hashCode => strategyId.hashCode;

  @override
  String toString() {
    return 'Strategy(strategyId: $strategyId, strategyName: $strategyName)';
  }
}
