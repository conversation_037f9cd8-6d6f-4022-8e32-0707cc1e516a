class PositionCompKeyFilter {
  final int clientId;
  final List<int> strategyIds;
  final List<int>? brokers;
  final List<int> accountIds;
  final List<int>? zenSecIds;

  PositionCompKeyFilter({
    required this.clientId,
    required this.strategyIds,
    this.brokers,
    required this.accountIds,
    this.zenSecIds,
  });

  factory PositionCompKeyFilter.fromJson(Map<String, dynamic> json) {
    return PositionCompKeyFilter(
      clientId: json['clientId'],
      strategyIds: List<int>.from(json['strategyIds'] ?? []),
      brokers: json['brokers'] != null ? List<int>.from(json['brokers']) : null,
      accountIds: List<int>.from(json['accountIds'] ?? []),
      zenSecIds: json['zenSecIds'] != null ? List<int>.from(json['zenSecIds']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'clientId': clientId,
      'strategyIds': strategyIds,
      'brokers': brokers,
      'accountIds': accountIds,
      'zenSecIds': zenSecIds,
    };
  }
}

class PortfolioBeta {
  final PositionCompKeyFilter positionCompKeyFilter;
  final double portfolioBeta;

  PortfolioBeta({
    required this.positionCompKeyFilter,
    required this.portfolioBeta,
  });

  factory PortfolioBeta.fromJson(Map<String, dynamic> json) {
    return PortfolioBeta(
      positionCompKeyFilter: PositionCompKeyFilter.fromJson(json['positionCompKeyFilter']),
      portfolioBeta: (json['portfolioBeta'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'positionCompKeyFilter': positionCompKeyFilter.toJson(),
      'portfolioBeta': portfolioBeta,
    };
  }
}
