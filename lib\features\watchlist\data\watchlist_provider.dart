import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/watchlist/model/watchlist_model.dart';
import 'package:phoenix/services/sort_service.dart';
import 'package:phoenix/services/watchlist_service.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/http_service.dart';

class WatchlistProvider extends ChangeNotifier {
  final WatchlistService _watchlistService = WatchlistService();

  List<Watchlist> _watchlists = [];
  Map<int, double> _stockPrices = {};
  Map<int, double> _sodPrices = {};
  bool _isLoading = false;
  String? _error;

  List<Watchlist> get watchlists => _watchlists;
  Map<int, double> get stockPrices => _stockPrices;
  Map<int, double> get sodPrices => _sodPrices;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<List<Watchlist>> loadWatchlists(String clientId) async {
    _setLoading(true);
    _error = null;

    try {
      final watchlists = await _watchlistService.getWatchlists(clientId);
      _watchlists = watchlists;
      await _fetchSodPrices();
      notifyListeners();
      return watchlists; // ✅ return the list here
    } catch (e) {
      _error = e.toString();
      debugPrint('Error loading watchlists: $e');
      return []; // ✅ return empty list on failure
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteWatchlists(
      String clientId, int watchlistIndex, String symbol) async {
    _setLoading(true);
    _error = null;

    try {
      final isDeleted =
          await _watchlistService.removeSecurityFromWatchlistBySymbol(
              clientId, watchlistIndex, symbol);
      await _fetchSodPrices();
      notifyListeners();
      return isDeleted; // ✅ return the list here
    } catch (e) {
      _error = e.toString();
      debugPrint('Error loading watchlists: $e');
      return false; // ✅ return empty list on failure
    } finally {
      _setLoading(false);
    }
  }

  void updateStockPrices(Map<int, double> prices) {
    _stockPrices = prices;
    notifyListeners();
  }

  Future<void> _fetchSodPrices() async {
    final zenIds = _watchlists
        .expand((watchlist) => watchlist.securities)
        .map((security) => security.zenId)
        .where((id) => id != 0)
        .toSet()
        .toList();

    if (zenIds.isEmpty) return;

    try {
      final customHttpService = HttpService();
      final response = await customHttpService.post(
        Uri.parse(ApiPath.getSodPrice()),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'zen_ids': zenIds}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 'SUCCESS' && data['payload'] != null) {
          final List<dynamic> payload = data['payload'];
          _sodPrices = {};
          for (final item in payload) {
            final zenId = item['zen_id'] as int;
            final price = (item['price'] as num).toDouble();
            _sodPrices[zenId] = price;
          }
          debugPrint('_sodPrices: $_sodPrices');
        }
      }
    } catch (e) {
      debugPrint('Error fetching SOD prices: $e');
    }
  }

  List<SecurityModel> sortSecurities(
    List<SecurityModel> securities,
    SortOption? sortOption,
    bool isAscending,
  ) {
    if (sortOption == null) return securities;

    securities.sort((a, b) {
      int comparison = 0;

      switch (sortOption) {
        case SortOption.alphabetical:
          comparison = a.tradingSymbol.compareTo(b.tradingSymbol);
          break;
        case SortOption.lastTradedPrice:
          final priceA = _stockPrices[a.zenId] ?? 0.0;
          final priceB = _stockPrices[b.zenId] ?? 0.0;
          comparison = priceA.compareTo(priceB);
          break;
        case SortOption.percentChange:
          final priceA = _stockPrices[a.zenId] ?? 0.0;
          final priceB = _stockPrices[b.zenId] ?? 0.0;
          final sodPriceA = _sodPrices[a.zenId] ?? 0.0;
          final sodPriceB = _sodPrices[b.zenId] ?? 0.0;
          final changeA =
              sodPriceA != 0 ? ((priceA - sodPriceA) / sodPriceA) * 100 : 0.0;
          final changeB =
              sodPriceB != 0 ? ((priceB - sodPriceB) / sodPriceB) * 100 : 0.0;
          comparison = changeA.compareTo(changeB);
          break;
        default:
          comparison = a.tradingSymbol.compareTo(b.tradingSymbol);
      }

      return isAscending ? comparison : -comparison;
    });

    return securities;
  }

  List<SecurityModel> filterSecurities(
    List<SecurityModel> securities,
    String searchQuery,
  ) {
    if (searchQuery.isEmpty) return securities;

    return securities
        .where((security) => security.tradingSymbol
            .toLowerCase()
            .contains(searchQuery.toLowerCase()))
        .toList();
  }

  Future<void> renameWatchlist(
      String clientId, int watchlistIndex, String newName) async {
    try {
      await _watchlistService.renameWatchlist(
          clientId, watchlistIndex, newName);
      await loadWatchlists(clientId);
    } catch (e) {
      debugPrint('Error renaming watchlist: $e');
      rethrow;
    }
  }

  Future<void> updateWatchlistOrder(
    String clientId,
    int watchlistIndex,
    List<SecurityModel> securities,
  ) async {
    try {
      await _watchlistService.updateWatchlistOrder(
        clientId,
        watchlistIndex,
        securities,
      );
    } catch (e) {
      debugPrint('Error updating watchlist order: $e');
      rethrow;
    }
  }

  Future<bool> removeSecurityFromWatchlist(
    String clientId,
    int watchlistIndex,
    String tradingSymbol,
  ) async {
    try {
      return await _watchlistService.removeSecurityFromWatchlistBySymbol(
        clientId,
        watchlistIndex,
        tradingSymbol,
      );
    } catch (e) {
      debugPrint('Error removing security from watchlist: $e');
      return false;
    }
  }

  List<int> getZenIds() {
    return _watchlists
        .expand((watchlist) => watchlist.securities)
        .map((security) => security.zenId)
        .where((id) => id != 0)
        .toSet()
        .toList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
