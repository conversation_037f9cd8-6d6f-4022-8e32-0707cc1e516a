import 'dart:async';
import 'package:web_socket_channel/io.dart';

class WebSocketProvider {
  IOWebSocketChannel? _channel;
  StreamController<dynamic>? _streamController;

  Stream<dynamic> connect(String url, {Map<String, String>? headers}) {
    print("1.# Data provider connect");
    _streamController = StreamController<dynamic>();

    try {
      _channel = IOWebSocketChannel.connect(url, headers: headers);

      _channel?.stream.listen(
        (data) {
          print("Data received in provider: $data");
          _streamController?.add(data); // Forward data
        },
        onError: (error) {
          print("Error received in provider: $error");
          _streamController?.addError(error); // Forward error
          reconnect(url, headers: headers); // Attempt reconnect
        },
        onDone: () {
          print("Connection closed in provider.");
          _streamController?.close(); // Close stream
        },
        cancelOnError: true,
      );
    } catch (e) {
      print("Exception during connection: $e");
      _streamController?.addError(e);
    }

    return _streamController!.stream;
  }

  void sendPing(String message) {
    try {
      _channel?.sink.add(message);
    } catch (e) {
      print("Error while sending ping: $e");
    }
  }

  void disconnect() {
    _channel?.sink.close();
    _streamController?.close();
  }

  void reconnect(String url, {Map<String, String>? headers}) async {
    print("Attempting to reconnect...");
    disconnect();
    await Future.delayed(Duration(seconds: 10));
    connect(url, headers: headers);
  }
}
