import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class MarketTrendArrow2 extends StatelessWidget {
  const MarketTrendArrow2({
    super.key,
    required this.isPositiveChange,
  });

  final bool isPositiveChange;

  @override
  Widget build(BuildContext context) {
    return Icon(
      isPositiveChange ? Icons.trending_up : Icons.trending_down,
      color: isPositiveChange
          ? ThemeConstants.netWorthGreenColor
          : ThemeConstants.netWorthRedColor,
      size: 14,
    );
  }
}
