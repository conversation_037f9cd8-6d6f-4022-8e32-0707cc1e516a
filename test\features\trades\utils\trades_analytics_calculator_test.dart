import 'package:flutter_test/flutter_test.dart';
import 'package:phoenix/features/trades/model/trades_model.dart';
import 'package:phoenix/features/trades/utils/trades_analytics_calculator.dart';

void main() {
  group('TradesAnalyticsCalculator', () {
    test('should return empty analytics for empty trades list', () {
      final analytics = TradesAnalyticsCalculator.calculateAnalytics([], {});
      
      expect(analytics.totalTrades, 0);
      expect(analytics.buyTrades, 0);
      expect(analytics.sellTrades, 0);
      expect(analytics.totalVolume, 0);
      expect(analytics.totalValue, 0.0);
      expect(analytics.avgTradeSize, 0.0);
      expect(analytics.brokerAnalytics, isEmpty);
    });

    test('should calculate analytics correctly for sample trades', () {
      final trades = [
        TradesModel(
          id: 1,
          clientId: 1,
          accountId: 1,
          strategyId: 1,
          broker: 'ZEN_BROKER',
          symbol: 'AAPL',
          side: 'BUY',
          quantity: 100,
          price: 150.0,
          value: 15000.0,
          timestamp: DateTime.now(),
          status: 'FILLED',
          exchange: 'NSE',
          zenId: 1,
        ),
        TradesModel(
          id: 2,
          clientId: 1,
          accountId: 1,
          strategyId: 1,
          broker: 'ZERODHA',
          symbol: 'GOOGL',
          side: 'SELL',
          quantity: 50,
          price: 2000.0,
          value: 100000.0,
          timestamp: DateTime.now(),
          status: 'FILLED',
          exchange: 'NSE',
          zenId: 2,
        ),
      ];

      final brokerMap = {
        'ZEN_BROKER': 'Zen Broker',
        'ZERODHA': 'Zerodha',
      };

      final analytics = TradesAnalyticsCalculator.calculateAnalytics(trades, brokerMap);

      expect(analytics.totalTrades, 2);
      expect(analytics.buyTrades, 1);
      expect(analytics.sellTrades, 1);
      expect(analytics.totalVolume, 150);
      expect(analytics.totalValue, 115000.0);
      expect(analytics.avgTradeSize, 57500.0);
      expect(analytics.brokerAnalytics.length, 2);
      
      final zenBrokerAnalytics = analytics.brokerAnalytics['ZEN_BROKER'];
      expect(zenBrokerAnalytics?.brokerName, 'Zen Broker');
      expect(zenBrokerAnalytics?.tradeCount, 1);
      expect(zenBrokerAnalytics?.totalInvested, 15000.0);
      expect(zenBrokerAnalytics?.volume, 100);

      final zerodhaAnalytics = analytics.brokerAnalytics['ZERODHA'];
      expect(zerodhaAnalytics?.brokerName, 'Zerodha');
      expect(zerodhaAnalytics?.tradeCount, 1);
      expect(zerodhaAnalytics?.totalInvested, 100000.0);
      expect(zerodhaAnalytics?.volume, 50);
    });
  });
}
