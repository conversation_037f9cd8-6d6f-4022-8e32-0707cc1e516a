import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';
import '../../utils/theme_constants.dart';

// Data Models for better JSON parsing
class StrategyData {
  final List<String> underlying;
  final ConditionData? entryCondition;
  final ConditionData? exitCondition;
  final String? name;
  final String? description;

  StrategyData({
    required this.underlying,
    this.entryCondition,
    this.exitCondition,
    this.name,
    this.description,
  });

  factory StrategyData.fromJson(Map<String, dynamic> json) {
    try {
      return StrategyData(
        underlying: (json['underlying'] as List<dynamic>?)
                ?.map((e) => e.toString())
                .toList() ??
            [],
        entryCondition: json['entryCondition'] != null
            ? ConditionData.fromJson(json['entryCondition'])
            : null,
        exitCondition: json['exitCondition'] != null
            ? ConditionData.fromJson(json['exitCondition'])
            : null,
        name: json['name']?.toString(),
        description: json['description']?.toString(),
      );
    } catch (e) {
      throw FormatException('Invalid strategy data format: $e');
    }
  }
}

class ConditionData {
  final String? type;
  final String? operator;
  final List<ConditionOperand>? operands;
  final ActionData? action;

  ConditionData({
    this.type,
    this.operator,
    this.operands,
    this.action,
  });

  factory ConditionData.fromJson(Map<String, dynamic> json) {
    return ConditionData(
      type: json['type']?.toString(),
      operator: json['operator']?.toString(),
      operands: json['operands'] != null
          ? (json['operands'] as List<dynamic>)
              .map((operand) => ConditionOperand.fromJson(operand))
              .toList()
          : null,
      action: json['action'] != null
          ? ActionData.fromJson(json['action'])
          : null,
    );
  }
}

class ConditionOperand {
  final String type;
  final String? timeValue;
  final String? format;
  final Map<String, dynamic>? additionalData;

  ConditionOperand({
    required this.type,
    this.timeValue,
    this.format,
    this.additionalData,
  });

  factory ConditionOperand.fromJson(Map<String, dynamic> json) {
    return ConditionOperand(
      type: json['type']?.toString() ?? 'UNKNOWN',
      timeValue: json['timeValue']?.toString(),
      format: json['format']?.toString(),
      additionalData: Map<String, dynamic>.from(json)
        ..removeWhere((key, value) => ['type', 'timeValue', 'format'].contains(key)),
    );
  }
}

class ActionData {
  final String? orderType;
  final String? transactionType;
  final dynamic quantity;
  final String? productType;
  final String? tradingSymbol;

  ActionData({
    this.orderType,
    this.transactionType,
    this.quantity,
    this.productType,
    this.tradingSymbol,
  });

  factory ActionData.fromJson(Map<String, dynamic> json) {
    return ActionData(
      orderType: json['orderType']?.toString(),
      transactionType: json['transactionType']?.toString(),
      quantity: json['qty'] ?? json['quantity'],
      productType: json['productType']?.toString(),
      tradingSymbol: json['tradingSymbol']?.toString(),
    );
  }
}

class StrategyFlowchartView extends StatefulWidget {
  final String jsonText;

  const StrategyFlowchartView({super.key, required this.jsonText});

  @override
  State<StrategyFlowchartView> createState() => _StrategyFlowchartViewState();
}

class _StrategyFlowchartViewState extends State<StrategyFlowchartView>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  StrategyData? _strategyData;
  String? _parseError;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _parseStrategyData();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _parseStrategyData() {
    Future.delayed(const Duration(milliseconds: 300), () {
      try {
        final Map<String, dynamic> jsonData = jsonDecode(widget.jsonText);
        _strategyData = StrategyData.fromJson(jsonData);
        _parseError = null;
      } catch (e) {
        _parseError = e.toString();
        _strategyData = null;
      }
      
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        if (_isLoading) {
          return _buildLoadingView(themeState);
        }
        
        if (_parseError != null) {
          return _buildErrorView(context, themeState);
        }
        
        return _buildMainView(themeState);
      },
    );
  }

  Widget _buildLoadingView(ThemeState themeState) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
      appBar: _buildAppBar(themeState, 'Loading Strategy...'),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                AppTheme.primaryColor(themeState.isDarkMode),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Parsing strategy data...',
              style: TextStyle(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, ThemeState themeState) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
      appBar: _buildAppBar(themeState, 'Parse Error'),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Icon(
                  Icons.error_outline_rounded,
                  size: 64,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Invalid Strategy Format',
                style: TextStyle(
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'Unable to parse the strategy JSON data',
                style: TextStyle(
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _parseError ?? 'Unknown error',
                  style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 12,
                    fontFamily: 'monospace',
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.arrow_back),
                label: const Text('Go Back'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor(themeState.isDarkMode),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainView(ThemeState themeState) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
      appBar: _buildAppBar(themeState, 'Strategy Flowchart'),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppTheme.backgroundColor(themeState.isDarkMode),
              AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.3),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  if (_strategyData?.name != null) ...[
                    _buildStrategyHeader(_strategyData!, themeState),
                    const SizedBox(height: 24),
                  ],
                  _buildUnderlyingSection(_strategyData!, themeState),
                  const SizedBox(height: 24),
                  _buildFlowArrow(themeState),
                  const SizedBox(height: 24),
                  _buildEntryConditionSection(_strategyData!, themeState),
                  const SizedBox(height: 24),
                  _buildFlowArrow(themeState),
                  const SizedBox(height: 24),
                  _buildExitConditionSection(_strategyData!, themeState),
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeState themeState, String title) {
    return AppBar(
      backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
      elevation: 0,
      centerTitle: true,
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.account_tree_rounded,
              color: AppTheme.primaryColor(themeState.isDarkMode),
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
        ],
      ),
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios_new_rounded,
          color: AppTheme.textPrimary(themeState.isDarkMode),
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }

  Widget _buildStrategyHeader(StrategyData strategy, ThemeState themeState) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
            AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.ac_unit,
                color: AppTheme.primaryColor(themeState.isDarkMode),
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  strategy.name ?? 'Trading Strategy',
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          if (strategy.description != null) ...[
            const SizedBox(height: 12),
            Text(
              strategy.description!,
              style: TextStyle(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                fontSize: 16,
                height: 1.4,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildUnderlyingSection(StrategyData strategy, ThemeState themeState) {
    return _buildModernCard(
      themeState: themeState,
      title: 'Underlying Assets',
      icon: Icons.trending_up_rounded,
      iconColor: Colors.blue,
      content: strategy.underlying.isNotEmpty
          ? Wrap(
              spacing: 8,
              runSpacing: 8,
              children: strategy.underlying
                  .map((asset) => _buildAssetChip(asset, themeState))
                  .toList(),
            )
          : _buildEmptyState('No underlying assets specified', themeState),
    );
  }

  Widget _buildAssetChip(String asset, ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.show_chart_rounded,
            size: 16,
            color: AppTheme.primaryColor(themeState.isDarkMode),
          ),
          const SizedBox(width: 8),
          Text(
            asset,
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEntryConditionSection(StrategyData strategy, ThemeState themeState) {
    return _buildConditionCard(
      themeState: themeState,
      title: 'Entry Condition',
      icon: Icons.login_rounded,
      iconColor: Colors.green,
      condition: strategy.entryCondition,
      actionType: 'ENTRY',
    );
  }

  Widget _buildExitConditionSection(StrategyData strategy, ThemeState themeState) {
    return _buildConditionCard(
      themeState: themeState,
      title: 'Exit Condition',
      icon: Icons.logout_rounded,
      iconColor: Colors.red,
      condition: strategy.exitCondition,
      actionType: 'EXIT',
    );
  }

  Widget _buildConditionCard({
    required ThemeState themeState,
    required String title,
    required IconData icon,
    required Color iconColor,
    required ConditionData? condition,
    required String actionType,
  }) {
    return _buildModernCard(
      themeState: themeState,
      title: title,
      icon: icon,
      iconColor: iconColor,
      content: condition != null
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildConditionDetails(condition, themeState),
                const SizedBox(height: 16),
                if (condition.action != null)
                  _buildActionDetails(condition.action!, themeState),
              ],
            )
          : _buildEmptyState('No $actionType condition specified', themeState),
    );
  }

  Widget _buildConditionDetails(ConditionData condition, ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeState.isDarkMode
            ? ThemeConstants.zenBlack1.withOpacity(0.3)
            : AppTheme.backgroundColor(themeState.isDarkMode).withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.rule_rounded,
                  size: 16,
                  color: AppTheme.primaryColor(themeState.isDarkMode),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Condition Logic',
                style: TextStyle(
                  color: AppTheme.primaryColor(themeState.isDarkMode),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (condition.type != null)
            _buildInfoRow('Type', condition.type!.toUpperCase(), themeState),
          if (condition.operator != null)
            _buildInfoRow('Operator', condition.operator!, themeState),
          if (condition.operands != null && condition.operands!.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildOperandsList(condition.operands!, themeState),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, ThemeState themeState) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: AppTheme.textPrimary(themeState.isDarkMode),
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOperandsList(List<ConditionOperand> operands, ThemeState themeState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Conditions:',
          style: TextStyle(
            color: AppTheme.textSecondary(themeState.isDarkMode),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        ...operands.map((operand) => _buildConditionItem(operand, themeState)),
      ],
    );
  }

  Widget _buildConditionItem(ConditionOperand operand, ThemeState themeState) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeState.isDarkMode
            ? ThemeConstants.zenBlack.withOpacity(0.5)
            : AppTheme.surfaceColor(themeState.isDarkMode),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              operand.type.contains('time') 
                  ? Icons.access_time_rounded 
                  : Icons.rule_rounded,
              size: 16,
              color: Colors.orange,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  operand.type.toUpperCase(),
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (operand.timeValue != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    '${operand.timeValue}${operand.format?.isNotEmpty == true ? ' (${operand.format})' : ''}',
                    style: TextStyle(
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionDetails(ActionData action, ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeState.isDarkMode
            ? ThemeConstants.zenBlack1.withOpacity(0.3)
            : AppTheme.backgroundColor(themeState.isDarkMode).withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.play_arrow_rounded,
                  size: 16,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Action Details',
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildActionGrid(action, themeState),
        ],
      ),
    );
  }

  Widget _buildActionGrid(ActionData action, ThemeState themeState) {
    final items = [
      if (action.orderType != null) ('Order Type', action.orderType!),
      if (action.transactionType != null) ('Transaction', action.transactionType!),
      if (action.quantity != null) ('Quantity', action.quantity.toString()),
      if (action.productType != null) ('Product Type', action.productType!),
      if (action.tradingSymbol != null) ('Symbol', action.tradingSymbol!),
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: items.map((item) => _buildActionChip(item.$1, item.$2, themeState)).toList(),
    );
  }

  Widget _buildActionChip(String label, String value, ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.borderColor(themeState.isDarkMode),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value.toUpperCase(),
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernCard({
    required ThemeState themeState,
    required String title,
    required IconData icon,
    Color? iconColor,
    required Widget content,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.8),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.borderColor(themeState.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode
                ? Colors.black.withOpacity(0.3)
                : Colors.grey.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: (iconColor ?? AppTheme.primaryColor(themeState.isDarkMode))
                        .withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: iconColor ?? AppTheme.primaryColor(themeState.isDarkMode),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            content,
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message, ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor(themeState.isDarkMode).withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.borderColor(themeState.isDarkMode),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline_rounded,
            color: AppTheme.textSecondary(themeState.isDarkMode),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlowArrow(ThemeState themeState) {
    return Column(
      children: [
        Container(
          width: 3,
          height: 30,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.3),
                AppTheme.primaryColor(themeState.isDarkMode),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.3),
            ),
          ),
          child: Icon(
            Icons.keyboard_arrow_down_rounded,
            color: AppTheme.primaryColor(themeState.isDarkMode),
            size: 24,
          ),
        ),
      ],
    );
  }
}