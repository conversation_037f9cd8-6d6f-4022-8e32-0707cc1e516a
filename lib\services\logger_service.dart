import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

/// A service for logging application events and errors
class LoggerService {
  static final LoggerService _instance = LoggerService._internal();
  
  factory LoggerService() {
    return _instance;
  }
  
  LoggerService._internal();
  
  final List<LogEntry> _logs = [];
  bool _isInitialized = false;
  late Directory _logDirectory;
  
  /// Initialize the logger service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _logDirectory = Directory('${appDir.path}/logs');
      
      if (!await _logDirectory.exists()) {
        await _logDirectory.create(recursive: true);
      }
      
      _isInitialized = true;
      
      // Log app start
      info('Application started');
    } catch (e) {
      debugPrint('Failed to initialize logger: $e');
    }
  }
  
  /// Log an informational message
  void info(String message) {
    _log(LogLevel.info, message);
  }
  
  /// Log a warning message
  void warning(String message) {
    _log(LogLevel.warning, message);
  }
  
  /// Log an error message with optional stack trace
  void error(String message, [dynamic error, StackTrace? stackTrace]) {
    final errorMessage = error != null ? '$message: $error' : message;
    _log(LogLevel.error, errorMessage, stackTrace?.toString());
    
    // Print to console for debugging
    debugPrint('ERROR: $errorMessage');
    if (stackTrace != null) {
      debugPrint(stackTrace.toString());
    }
  }
  
  /// Internal method to log a message
  void _log(LogLevel level, String message, [String? stackTrace]) {
    final timestamp = DateTime.now();
    final entry = LogEntry(
      timestamp: timestamp,
      level: level,
      message: message,
      stackTrace: stackTrace,
    );
    
    _logs.add(entry);
    
    // Write to file if initialized
    if (_isInitialized) {
      _writeToFile(entry);
    }
  }
  
  /// Write a log entry to the log file
  Future<void> _writeToFile(LogEntry entry) async {
    try {
      final date = DateFormat('yyyy-MM-dd').format(entry.timestamp);
      final logFile = File('${_logDirectory.path}/log_$date.txt');
      
      final formattedTime = DateFormat('HH:mm:ss.SSS').format(entry.timestamp);
      final logLine = '${entry.level.name.toUpperCase()} [$formattedTime] ${entry.message}';
      
      String content = logLine;
      if (entry.stackTrace != null) {
        content += '\n${entry.stackTrace}';
      }
      content += '\n';
      
      await logFile.writeAsString('$content', mode: FileMode.append);
    } catch (e) {
      debugPrint('Failed to write log to file: $e');
    }
  }
  
  /// Get all logs
  List<LogEntry> getLogs() {
    return List.unmodifiable(_logs);
  }
  
  /// Clear all logs
  void clearLogs() {
    _logs.clear();
  }
  
  /// Get the log file for the current day
  Future<File?> getCurrentLogFile() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      final date = DateFormat('yyyy-MM-dd').format(DateTime.now());
      final logFile = File('${_logDirectory.path}/log_$date.txt');
      
      if (await logFile.exists()) {
        return logFile;
      }
    } catch (e) {
      debugPrint('Failed to get current log file: $e');
    }
    
    return null;
  }
  
  /// Get all log files
  Future<List<File>> getAllLogFiles() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      final files = await _logDirectory.list().where((entity) => 
        entity is File && entity.path.endsWith('.txt')
      ).map((entity) => entity as File).toList();
      
      return files;
    } catch (e) {
      debugPrint('Failed to get log files: $e');
      return [];
    }
  }
  
  /// Share the current log file
  Future<void> shareCurrentLog(BuildContext context) async {
    final file = await getCurrentLogFile();
    if (file != null) {
      await Share.shareXFiles(
        [XFile(file.path)],
        subject: 'Phoenix App Logs',
        text: 'Phoenix App Debug Logs',
      );
    }
  }
  
  /// Share all log files as a single zip file
  Future<void> shareAllLogs(BuildContext context) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      final files = await getAllLogFiles();
      if (files.isEmpty) {
        return;
      }
      
      if (files.length == 1) {
        await Share.shareXFiles(
          [XFile(files.first.path)],
          subject: 'Phoenix App Logs',
          text: 'Phoenix App Debug Logs',
        );
        return;
      }
      
      // Share multiple files
      await Share.shareXFiles(
        files.map((file) => XFile(file.path)).toList(),
        subject: 'Phoenix App Logs',
        text: 'Phoenix App Debug Logs',
      );
    } catch (e) {
      debugPrint('Failed to share logs: $e');
    }
  }
}

/// Log entry model
class LogEntry {
  final DateTime timestamp;
  final LogLevel level;
  final String message;
  final String? stackTrace;
  
  LogEntry({
    required this.timestamp,
    required this.level,
    required this.message,
    this.stackTrace,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'level': level.name,
      'message': message,
      'stackTrace': stackTrace,
    };
  }
  
  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      timestamp: DateTime.parse(json['timestamp']),
      level: LogLevel.values.firstWhere(
        (e) => e.name == json['level'],
        orElse: () => LogLevel.info,
      ),
      message: json['message'],
      stackTrace: json['stackTrace'],
    );
  }
}

/// Log level enum
enum LogLevel {
  info,
  warning,
  error,
}