import 'package:flutter/material.dart';

class GreeksIcon extends StatelessWidget {
  const GreeksIcon({super.key, required this.name, this.fontSize=18});
  final String name;
  final double fontSize;

  @override
  Widget build(BuildContext context) {
    final greekData = _getGreekData(name.toLowerCase());

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: greekData.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: greekData.color, width: 1),
      ),
      child: ImageIcon(
        AssetImage(greekData.assetPath),
        color: greekData.color,
        size: fontSize,
      ),
    );
  }

  _GreekData _getGreekData(String name) {
    switch (name) {
      case 'theta':
        return _GreekData('images/greeks/theta_icon.png', Colors.orange);
      case 'gamma':
        return _GreekData('images/greeks/gamm_icon.png', Colors.blue); // Fixed filename
      case 'delta':
        return _GreekData('images/greeks/delta_icon.png', const Color.fromARGB(255, 253, 229, 13));
      case 'vega':
        return _GreekData('images/greeks/vega_icon.png', Colors.green);
      case 'rho':
        return _GreekData('images/greeks/theta_icon.png', Colors.purple); // Use theta icon as fallback
      case 'oi':
        return _GreekData('images/greeks/oi_icon.png', Colors.purple);
      case 'iv':
        return _GreekData('images/greeks/iv_icon.png', Colors.red);
      case 'color':
        return _GreekData('images/greeks/color_icon.png', Colors.teal);
      default:
        return _GreekData('images/greeks/delta_icon.png', Colors.grey,); // Use delta icon as fallback
    }
  }
}

class _GreekData {
  final String assetPath;
  final Color color;
  bool isIconAvailable = false;

  _GreekData(this.assetPath, this.color);
}
