import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'bottom_navigation_event.dart';
part 'bottom_navigation_state.dart';

class BottomNavigationBloc
    extends Bloc<BottomNavigationEvent, BottomNavigationState> {
  BottomNavigationBloc() : super(BottomNavigationInitial()) {
    on<BottomNavigationChangeEvent>(_onBottomNavigationChanged);
  }
  void _onBottomNavigationChanged(
      BottomNavigationChangeEvent event, Emitter<BottomNavigationState> emit) {
    emit(BottomNavigaionChanged(currentPageIndex: event.currentPageIndex, isNetWorthBarExpanded: event.isVisible));
  }
}
