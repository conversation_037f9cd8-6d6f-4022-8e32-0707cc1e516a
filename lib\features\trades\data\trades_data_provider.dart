import 'dart:convert';
import 'package:flutter/widgets.dart';
import 'package:phoenix/features/common/api_response_model.dart';
import 'package:phoenix/features/trades/model/trades_model.dart';
import 'package:phoenix/services/request_body_filter_service.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/http_service.dart';
import 'package:phoenix/utils/app_exception.dart';

class TradesDataProvider {
  Future<List<TradesModel>> fetchTradesData(int clientId, {String? startTimestamp, String? endTimestamp}) async {
    final customHttpService = HttpService();

    try {
      final tradesFilter =
          await RequestBodyFilterService.buildPositionCompKeyFilter(clientId);

      final requestBody = jsonEncode({
        ...tradesFilter,
        if (startTimestamp != null) "start_timestamp": startTimestamp,
        if (endTimestamp != null) "end_timestamp": endTimestamp,
      });
      debugPrint('trades requestBody: $requestBody');
      
      final response = await customHttpService.post(
        Uri.parse(ApiPath.getTrades(clientId)),
        body: requestBody,
        headers: {'Content-Type': 'application/json'},
      );

      final jsonData = jsonDecode(response.body);

      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (dynamic payload) => payload,
      );

      if (apiResponse.code == 200 &&
          apiResponse.status == 'SUCCESS' &&
          response.statusCode == 200) {

        print(jsonData);
        final List<TradesModel> tradesData = (apiResponse.payload as List<dynamic>)
            .map((data) => TradesModel.fromJson(data))
            .toList();

        return tradesData;
      } else {
        throw AppException(apiResponse.message);
      }
    } catch (e) {
      // Only wrap if it's not already an AppException
      throw e is AppException
          ? e
          : AppException('Error fetching trades: ${e.toString()}');
    }
  }
}