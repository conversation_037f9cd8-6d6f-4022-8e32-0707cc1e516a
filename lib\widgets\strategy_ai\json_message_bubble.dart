import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class JsonMessageBubble extends StatelessWidget {
  final String jsonText;
  final void Function()? onCopy;
  final void Function()? onDeploy;
  final void Function()? onViewChart;
  final TextSpan Function(String) highlightJson;
  final String Function(String) prettyPrintJson;

  const JsonMessageBubble({
    super.key,
    required this.jsonText,
    required this.onCopy,
    required this.onDeploy,
    required this.onViewChart,
    required this.highlightJson,
    required this.prettyPrintJson,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Align(
          alignment: Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
            decoration: BoxDecoration(
              color: AppTheme.cardColor(themeState.isDarkMode),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppTheme.borderColor(themeState.isDarkMode),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: themeState.isDarkMode ? Colors.black26 : Colors.grey.withOpacity(0.2),
                  blurRadius: 8,
                  offset: Offset(0, 2)
                ),
              ],
            ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header bar
            Container(
              decoration: BoxDecoration(
                color: themeState.isDarkMode 
                    ? const Color(0xFF23272f) 
                    : AppTheme.surfaceColor(themeState.isDarkMode),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(14),
                  topRight: Radius.circular(14),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'json',
                    style: TextStyle(
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 0.5,
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(Icons.account_tree, size: 18, color: AppTheme.primaryColor(themeState.isDarkMode)),
                        tooltip: 'View Chart',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        onPressed: onViewChart,
                      ),
                      IconButton(
                        icon: Icon(Icons.copy, size: 18, color: AppTheme.textSecondary(themeState.isDarkMode)),
                        tooltip: 'Copy code',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        onPressed: onCopy,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Code block area
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: themeState.isDarkMode 
                    ? const Color(0xFF181b20)
                    : AppTheme.backgroundColor(themeState.isDarkMode),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(14),
                  bottomRight: Radius.circular(14),
                ),
              ),
              padding: const EdgeInsets.fromLTRB(16, 14, 16, 18),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: RichText(
                      text: highlightJson(prettyPrintJson(jsonText)),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      ElevatedButton.icon(
                        icon: const Icon(Icons.cloud_upload),
                        label: const Text('Deploy'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor(themeState.isDarkMode),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: onDeploy,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
          ));
        },
      );
    
  }
}
