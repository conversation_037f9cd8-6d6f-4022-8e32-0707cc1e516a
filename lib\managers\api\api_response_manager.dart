import 'dart:convert';

class ApiResponse<T> {
  final String status;
  final int code;
  final String message;
  final T payload;

  ApiResponse({
    required this.status,
    required this.code,
    required this.message,
    required this.payload,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    return ApiResponse(
      status: json['status'] as String,
      code: json['code'] as int,
      message: json['message'] as String,
      payload: json['code'] == 200 && json['status'] == 'SUCCESS'
          ? fromJsonT(json['payload'])
          : (null as T), // placeholder, see below
    );
  }
}

/*
This will:
Decode JSON
Check for status and code
Return an ApiResponse<T> with or without payload depending on success/error
*/

ApiResponse<T> handleApiResponse<T>(
  String responseBody,
  T Function(dynamic) fromJsonT,
) {
  final Map<String, dynamic> jsonData = jsonDecode(responseBody);

  final status = jsonData['status'];
  final code = jsonData['code'];
  final message = jsonData['message'];

  if (status == 'SUCCESS' && code == 200) {
    final payload = fromJsonT(jsonData['payload']);
    return ApiResponse<T>(
      status: status,
      code: code,
      message: message,
      payload: payload,
    );
  } else {
    return ApiResponse<T>(
      status: status,
      code: code,
      message: message,
      payload: null as T, // or pass a default value depending on use case
    );
  }
}

