import 'dart:convert';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/watchlist/model/watchlist_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class WatchlistService {
  static const int maxWatchlists = 5;

  Future<SharedPreferences> _getPrefs() async {
    return await SharedPreferences.getInstance();
  }

  String _getWatchlistKey(String clientId) {
    return 'watchlist_$clientId';
  }

  Future<List<Watchlist>> getWatchlists(String clientId) async {
    final prefs = await _getPrefs();
    final key = _getWatchlistKey(clientId);
    final jsonString = prefs.getString(key);

    if (jsonString != null) {
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((json) => Watchlist.fromJson(json)).toList();
    } else {
      // Create default watchlists if none exist
      final defaultWatchlists = List.generate(
        maxWatchlists,
        (index) => Watchlist(name: 'Watchlist ${index + 1}', securities: []),
      );
      await saveWatchlists(clientId, defaultWatchlists);
      return defaultWatchlists;
    }
  }

  Future<void> saveWatchlists(String clientId, List<Watchlist> watchlists) async {
    final prefs = await _getPrefs();
    final key = _getWatchlistKey(clientId);
    final jsonString = json.encode(watchlists.map((w) => w.toJson()).toList());
    await prefs.setString(key, jsonString);
  }

  Future<void> addSecurityToWatchlist(String clientId, int watchlistIndex, SecurityModel security) async {
    final watchlists = await getWatchlists(clientId);
    if (watchlistIndex >= 0 && watchlistIndex < watchlists.length) {
      // Check if security already exists to prevent duplicates
      final existingIndex = watchlists[watchlistIndex].securities.indexWhere((s) => s.tradingSymbol == security.tradingSymbol);
      if (existingIndex == -1) {
        watchlists[watchlistIndex].securities.add(security);
        await saveWatchlists(clientId, watchlists);
      }
    }
  }

  Future<void> removeSecurityFromWatchlist(String clientId, int watchlistIndex, int securityIndex) async {
    final watchlists = await getWatchlists(clientId);
    if (watchlistIndex >= 0 && watchlistIndex < watchlists.length) {
      if (securityIndex >= 0 && securityIndex < watchlists[watchlistIndex].securities.length) {
        watchlists[watchlistIndex].securities.removeAt(securityIndex);
        await saveWatchlists(clientId, watchlists);
      }
    }
  }

  Future<bool> removeSecurityFromWatchlistBySymbol(String clientId, int watchlistIndex, String symbol) async {
    final watchlists = await getWatchlists(clientId);
    if (watchlistIndex >= 0 && watchlistIndex < watchlists.length) {
      final securityIndex = watchlists[watchlistIndex].securities.indexWhere((s) => s.tradingSymbol == symbol);
      if (securityIndex != -1) {
        watchlists[watchlistIndex].securities.removeAt(securityIndex);
        await saveWatchlists(clientId, watchlists);
        return true;
      }
    }
    return false;
  }

  Future<void> renameWatchlist(String clientId, int watchlistIndex, String newName) async {
    final watchlists = await getWatchlists(clientId);
    if (watchlistIndex >= 0 && watchlistIndex < watchlists.length) {
      watchlists[watchlistIndex].name = newName;
      await saveWatchlists(clientId, watchlists);
    }
  }

  Future<void> reorderSecurityInWatchlist(String clientId, int watchlistIndex, int oldIndex, int newIndex) async {
    final watchlists = await getWatchlists(clientId);
    if (watchlistIndex >= 0 && watchlistIndex < watchlists.length) {
      final securities = watchlists[watchlistIndex].securities;
      if (oldIndex >= 0 && oldIndex < securities.length && newIndex >= 0 && newIndex < securities.length) {
        print('🔄 Reordering security from $oldIndex to $newIndex');
        print('📋 Before reorder: ${securities.map((s) => s.tradingSymbol).toList()}');
        
        // Remove the security from old position
        final SecurityModel security = securities.removeAt(oldIndex);
        // Insert at new position
        securities.insert(newIndex, security);
        
        print('📋 After reorder: ${securities.map((s) => s.tradingSymbol).toList()}');
        
        await saveWatchlists(clientId, watchlists);
        print('💾 Watchlist order saved successfully');
      }
    }
  }

  Future<void> updateWatchlistOrder(String clientId, int watchlistIndex, List<SecurityModel> newSecurities) async {
    final watchlists = await getWatchlists(clientId);
    if (watchlistIndex >= 0 && watchlistIndex < watchlists.length) {
      print('🔄 Updating watchlist order');
      print('📋 New order: ${newSecurities.map((s) => s.tradingSymbol).toList()}');
      
      watchlists[watchlistIndex].securities = newSecurities;
      await saveWatchlists(clientId, watchlists);
      print('💾 Watchlist order saved successfully');
    }
  }
}