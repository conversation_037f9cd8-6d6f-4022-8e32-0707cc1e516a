import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';
import 'package:phoenix/services/broker_data_mapper_service.dart';

part 'broker_data_map_event.dart';
part 'broker_data_map_state.dart';

class BrokerDataMapBloc extends Bloc<BrokerDataMapEvent, BrokerDataMapState> {
  BrokerDataMapBloc() : super(BrokerDataMapInitial()) {
    on<LoadBrokerData>(_onLoadBrokerData);
  }

  void _onLoadBrokerData(
    LoadBrokerData event,
    Emitter<BrokerDataMapState> emit,
  ) {
    final mapper = BrokerDataMapperService(brokers: event.brokers);
    emit(BrokerDataMapProcessedState(
      accountIdToNameMap: mapper.accountIdToNameMap,
      strategyIdToNameMap: mapper.strategyIdToNameMap,
      brokerNameToLabelMap: mapper.brokerNameToLabelMap,
    ));
  }
}
