class UserDetailsModel {
  final int userId;
  final String emailId;
  final bool isActive;
  final bool isAuthAdmin;
  final String profileData;

  UserDetailsModel({
    required this.userId,
    required this.emailId,
    required this.isActive,
    required this.isAuthAdmin,
    required this.profileData,
  });

  factory UserDetailsModel.fromJson(Map<String, dynamic> json) {
    return UserDetailsModel(
      userId: json['user_id'],
      emailId: json['email_id'],
      isActive: json['is_active'],
      isAuthAdmin: json['is_auth_admin'],
      profileData: json['profile_data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'email_id': emailId,
      'is_active': isActive,
      'is_auth_admin': isAuthAdmin,
      'profile_data': profileData,
    };
  }

  @override
  String toString() {
    return 'UserDetailsModel(userId: $userId, emailId: $emailId, isActive: $isActive, '
        'isAuthAdmin: $isAuthAdmin, profileData: $profileData)';
  }
}
