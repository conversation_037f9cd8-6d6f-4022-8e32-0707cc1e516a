import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/theme_constants.dart';

class JsonViewerPage extends StatelessWidget {
  final String jsonText;
  final TextSpan Function(String) highlightJson;
  final String Function(String) prettyPrintJson;

  const JsonViewerPage({
    super.key,
    required this.jsonText,
    required this.highlightJson,
    required this.prettyPrintJson,
  });

  void _copyToClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: jsonText));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 20),
            SizedBox(width: 8),
            Text('JSON copied to clipboard!'),
          ],
        ),
        backgroundColor: ThemeConstants.toastSuccessColor,
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: ThemeConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.data_object, color: ThemeConstants.blue, size: 28),
            SizedBox(width: 8),
            Text(
              'Strategy JSON',
              style: TextStyle(
                color: ThemeConstants.zenWhite,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
          ],
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios_new_rounded, color: ThemeConstants.zenWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.copy, color: ThemeConstants.zenWhite),
            onPressed: () => _copyToClipboard(context),
            tooltip: 'Copy JSON',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              ThemeConstants.backgroundColor,
              ThemeConstants.zenBlack1.withOpacity(0.7)
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: ThemeConstants.zenBlack,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 8,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header bar
                Container(
                  decoration: BoxDecoration(
                    color: Color(0xFF23272f),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.data_object, color: Color(0xFFbdbdbd), size: 18),
                          SizedBox(width: 8),
                          Text(
                            'strategy.json',
                            style: TextStyle(
                              color: Color(0xFFbdbdbd),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: ThemeConstants.blue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: ThemeConstants.blue.withOpacity(0.3)),
                        ),
                        child: IconButton(
                          icon: Icon(Icons.copy, size: 18, color: ThemeConstants.blue),
                          tooltip: 'Copy JSON',
                          padding: EdgeInsets.all(8),
                          constraints: BoxConstraints(),
                          onPressed: () => _copyToClipboard(context),
                        ),
                      ),
                    ],
                  ),
                ),
                // JSON content area
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Color(0xFF181b20),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                  padding: EdgeInsets.all(20),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: RichText(
                      text: highlightJson(prettyPrintJson(jsonText)),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}