import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/custom_radio_button/custom_radio_widget.dart';

class ExchangeSelector2 extends StatelessWidget {
  final bool isNSE;
  final double defaultPrice;
  final ValueChanged<bool> action1; // Using ValueChanged<bool>
  final ValueChanged<bool> action2; // Using ValueChanged<bool>
  final SecurityModel? selectedSecurity;

  const ExchangeSelector2({
    super.key,
    required this.isNSE,
    required this.defaultPrice,
    required this.action1,
    required this.action2,
    this.selectedSecurity,
  });

  @override
  Widget build(BuildContext context) {
    // Determine if NSE and BSE should be enabled
    final bool enableNSE = selectedSecurity == null ||
        (selectedSecurity!.exchanges.contains("NSE") ||
            selectedSecurity!.exchanges.contains("NFO"));
    final bool enableBSE =
        selectedSecurity == null || selectedSecurity!.exchanges.contains("BSE");

    return Container(
      height: 30,

      ///Exchange bar background color, radius styles
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 100, 100, 100),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(10)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _buildExchangeOption(
            label: 'NSE',
            isSelected: isNSE,
            value: true,
            onChanged: enableNSE ? action1 : (_) {}, // Fallback function
            isEnabled: enableNSE,
          ),
          const SizedBox(width: 4),
          _buildExchangeOption(
            label: 'BSE',
            isSelected: isNSE,
            value: false,
            onChanged: enableBSE ? action2 : (_) {}, // Fallback function
            isEnabled: enableBSE,
          ),
        ],
      ),
    );
  }

  Widget _buildExchangeOption({
    required String label,
    required bool isSelected,
    required bool value,
    required ValueChanged<bool> onChanged, // Enforced non-nullable type
    required bool isEnabled,
  }) {
    return Row(
      children: [
        CustomRadioWidget(
          groupValue: isSelected,
          value: value,
          onChanged: onChanged, // Ensured non-null
          height: 15,
          width: 15,
          isActiveColor: Colors.white,
          isNotActiveColor: Color.fromARGB(255, 126, 126, 126),
          borderColor: Colors.white,
          isDisabled: !isEnabled,
        ),
        BlocBuilder<WebSocketBloc, WebSocketState>(
          builder: (context, state) {
            if (!isEnabled) {
              //if not fount then showing --
              return Text(
                '$label --',
                style: TextStyle(
                  color: isEnabled ? Colors.white : Colors.grey,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
              );
            }
            if (state is WebSocketStockPriceUpdated) {
              return Text(
                '$label ₹ ${state.stockPrice == 0 ? defaultPrice == 0 ? '--' : UtilFunctions.formatIndianCurrency(defaultPrice) : UtilFunctions.formatIndianCurrency(state.stockPrice as double)}',
                style: TextStyle(
                  color: isEnabled ? Colors.white : Colors.grey,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
              );
            }
            return Text(
              '$label ₹ ${defaultPrice == 0 ? '--' : UtilFunctions.formatIndianCurrency(defaultPrice)}',
              style: TextStyle(
                color: isEnabled ? Colors.white : Colors.grey,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            );
          },
        ),
      ],
    );
  }
}
