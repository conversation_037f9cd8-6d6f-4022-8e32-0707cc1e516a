import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';

class DateRangeSelector extends StatefulWidget {
  final Function(DateTime startDate, DateTime endDate) onDateRangeChanged;

  const DateRangeSelector({
    super.key,
    required this.onDateRangeChanged,
  });

  @override
  State<DateRangeSelector> createState() => _DateRangeSelectorState();
}

class _DateRangeSelectorState extends State<DateRangeSelector> {
  late DateTime _startDate;
  late DateTime _endDate;

  @override
  void initState() {
    super.initState();
    // Set today as default
    final today = DateTime.now();
    _startDate = DateTime(today.year, today.month, today.day);
    _endDate = DateTime(today.year, today.month, today.day);

    // Trigger initial callback
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onDateRangeChanged(_startDate, _endDate);
    });
  }

  void _showDateRangePicker() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _DateRangePickerDialog(
        initialStartDate: _startDate,
        initialEndDate: _endDate,
        onDateRangeSelected: (startDate, endDate) {
          setState(() {
            _startDate = startDate;
            _endDate = endDate;
          });
          widget.onDateRangeChanged(_startDate, _endDate);
        },
      ),
    );
  }

  String _getDateRangeText() {
    if (_startDate == _endDate) {
      // Same date - show as single date
      return _formatSingleDate(_startDate);
    } else if (_startDate.year == _endDate.year) {
      // Same year - compact format
      if (_startDate.month == _endDate.month) {
        // Same month
        return '${_startDate.day}-${_endDate.day} ${_getMonthName(_startDate.month)} ${_startDate.year}';
      } else {
        // Different months, same year
        return '${_startDate.day} ${_getMonthName(_startDate.month)} - ${_endDate.day} ${_getMonthName(_endDate.month)} ${_startDate.year}';
      }
    } else {
      // Different years
      return '${_formatSingleDate(_startDate)} - ${_formatSingleDate(_endDate)}';
    }
  }

  String _formatSingleDate(DateTime date) {
    return '${date.day} ${_getMonthName(date.month)} ${date.year}';
  }

  String _getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }

  String _getDateRangeLabel() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    if (_startDate == _endDate) {
      if (_startDate == today) {
        return 'Today';
      } else if (_startDate == yesterday) {
        return 'Yesterday';
      } else {
        return 'Single Day';
      }
    } else {
      final difference = _endDate.difference(_startDate).inDays + 1;
      if (difference <= 7) {
        return '$difference days';
      } else if (difference <= 31) {
        return '${(difference / 7).ceil()} weeks';
      } else {
        final months = (difference / 30).ceil();
        return '$months month${months > 1 ? 's' : ''}';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return GestureDetector(
            onTap: _showDateRangePicker,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14),
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor(themeState.isDarkMode),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.borderColor(themeState.isDarkMode),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: themeState.isDarkMode
                        ? Colors.black.withOpacity(0.25)
                        : Colors.grey.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                    spreadRadius: 0,
                  ),
                  if (themeState.isDarkMode)
                    BoxShadow(
                      color: const Color(0xff3A3E44).withOpacity(0.8),
                      blurRadius: 0,
                      offset: const Offset(0, 1),
                      spreadRadius: 0,
                    ),
                ],
              ),
              child: IntrinsicWidth(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Calendar icon with background
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor(themeState.isDarkMode)
                            .withOpacity(0.15),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        CupertinoIcons.calendar,
                        color: AppTheme.primaryColor(themeState.isDarkMode),
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 14),
                    // Date information
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Date range text
                          Text(
                            _getDateRangeText(),
                            style: TextStyle(
                              color: AppTheme.textPrimary(themeState.isDarkMode),
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.none,
                              letterSpacing: 0.2,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          // Label (Today, Yesterday, X days, etc.)
                          Text(
                            _getDateRangeLabel(),
                            style: TextStyle(
                              color:
                                  AppTheme.primaryColor(themeState.isDarkMode)
                                      .withOpacity(0.8),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              decoration: TextDecoration.none,
                              letterSpacing: 0.1,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Chevron with subtle animation hint
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: AppTheme.textPrimary(themeState.isDarkMode)
                            .withOpacity(0.05),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Icon(
                        CupertinoIcons.chevron_down,
                        color: AppTheme.textPrimary(themeState.isDarkMode)
                            .withOpacity(0.6),
                        size: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ));
      },
    );
  }
}

class _DateRangePickerDialog extends StatefulWidget {
  final DateTime initialStartDate;
  final DateTime initialEndDate;
  final Function(DateTime startDate, DateTime endDate) onDateRangeSelected;

  const _DateRangePickerDialog({
    required this.initialStartDate,
    required this.initialEndDate,
    required this.onDateRangeSelected,
  });

  @override
  State<_DateRangePickerDialog> createState() => _DateRangePickerDialogState();
}

class _DateRangePickerDialogState extends State<_DateRangePickerDialog>
    with TickerProviderStateMixin {
  bool _isSelectingEndDate = false;
  late DateTime _selectedStartDate;
  late DateTime _tempEndDate;

  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _selectedStartDate = widget.initialStartDate;
    _tempEndDate = widget.initialEndDate;

    // Animation controllers
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(-1.0, 0.0),
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  void _goToEndDateSelection() async {
    setState(() {
      _isSelectingEndDate = true;
      // Ensure end date is not before start date
      if (_tempEndDate.isBefore(_selectedStartDate)) {
        _tempEndDate = _selectedStartDate;
      }
    });

    await _slideController.forward();
  }

  void _goBackToStartDateSelection() async {
    await _slideController.reverse();
    setState(() {
      _isSelectingEndDate = false;
    });
  }

  void _onStartDateChanged(DateTime date) {
    setState(() {
      _selectedStartDate = date;
    });
  }

  void _onEndDateChanged(DateTime date) {
    setState(() {
      _tempEndDate = date;
    });
  }

  void _onCancel() {
    Navigator.of(context).pop();
  }

  void _onDone() {
    widget.onDateRangeSelected(_selectedStartDate, _tempEndDate);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Dialog(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            constraints: const BoxConstraints(maxWidth: 400),
            decoration: BoxDecoration(
              color: AppTheme.backgroundColor(themeState.isDarkMode),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: AppTheme.borderColor(themeState.isDarkMode)),
            ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with navigation
            Row(
              children: [
                // Left button (Cancel/Back)
                TextButton(
                  onPressed: _isSelectingEndDate
                      ? _goBackToStartDateSelection
                      : _onCancel,
                  style: TextButton.styleFrom(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (_isSelectingEndDate) ...[
                        Icon(
                          CupertinoIcons.chevron_left,
                          color: AppTheme.textPrimary(themeState.isDarkMode),
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                      ],
                      Text(
                        _isSelectingEndDate ? 'Back' : 'Cancel',
                        style: TextStyle(
                          color: AppTheme.textPrimary(themeState.isDarkMode),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                // Title - using Expanded to take available space
                // Expanded(
                //   child: Center(
                //     child: AnimatedSwitcher(
                //       duration: const Duration(milliseconds: 300),
                //       child: Text(
                //         _isSelectingEndDate
                //             ? 'Select End Date'
                //             : 'Select Start Date',
                //         key: ValueKey(_isSelectingEndDate),
                //         style: const TextStyle(
                //           color: Colors.white,
                //           fontSize: 16,
                //           fontWeight: FontWeight.w600,
                //         ),
                //         textAlign: TextAlign.center,
                //       ),
                //     ),
                //   ),
                // ),
                // // Empty container to maintain layout balance (buttons moved to bottom)
                // const SizedBox(width: 48),
              ],
            ),

            const SizedBox(height: 10),

            // Progress indicator
            // Row(
            //   children: [
            //     Expanded(
            //       child: Container(
            //         height: 3,
            //         decoration: BoxDecoration(
            //           color: ThemeConstants.blue,
            //           borderRadius: BorderRadius.circular(2),
            //         ),
            //       ),
            //     ),
            //     const SizedBox(width: 8),
            //     Expanded(
            //       child: Container(
            //         height: 3,
            //         decoration: BoxDecoration(
            //           color: _isSelectingEndDate
            //               ? ThemeConstants.blue
            //               : Colors.grey.withOpacity(0.3),
            //           borderRadius: BorderRadius.circular(2),
            //         ),
            //       ),
            //     ),
            //   ],
            // ),

            // const SizedBox(height: 24),

            // Modern side by side selected dates display - Compact Version
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor(themeState.isDarkMode),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.borderColor(themeState.isDarkMode),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  // Selected step indicator
                  Row(
                    children: [
                      Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor(themeState.isDarkMode),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _isSelectingEndDate
                            ? 'Select End Date'
                            : 'Select Start Date',
                        style: TextStyle(
                          color: AppTheme.primaryColor(themeState.isDarkMode),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${_isSelectingEndDate ? '2' : '1'} of 2',
                        style: TextStyle(
                          color: AppTheme.textSecondary(themeState.isDarkMode),
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Date range display
                  Row(
                    children: [
                      // Start date
                      Expanded(
                        child: GestureDetector(
                          onTap: _goBackToStartDateSelection,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: !_isSelectingEndDate
                                  ? AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.15)
                                  : AppTheme.cardColor(themeState.isDarkMode),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: !_isSelectingEndDate
                                    ? AppTheme.primaryColor(themeState.isDarkMode)
                                    : Colors.transparent,
                              ),
                            ),
                            child: Column(
                              children: [
                                Text(
                                  'Start',
                                  style: TextStyle(
                                    color: !_isSelectingEndDate
                                        ? AppTheme.primaryColor(themeState.isDarkMode)
                                        : AppTheme.textSecondary(themeState.isDarkMode),
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${_selectedStartDate.day}/${_selectedStartDate.month}/${_selectedStartDate.year}',
                                  style: TextStyle(
                                    color: AppTheme.textPrimary(themeState.isDarkMode),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Arrow
                      Icon(
                        CupertinoIcons.arrow_right,
                        color: AppTheme.primaryColor(themeState.isDarkMode),
                        size: 16,
                      ),
                      const SizedBox(width: 12),
                      // End date
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (_isSelectingEndDate &&
                                _tempEndDate.isAfter(_selectedStartDate)) {
                              _onDone();
                            } else {
                              _goToEndDateSelection();
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: _isSelectingEndDate
                                  ? AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.15)
                                  : AppTheme.cardColor(themeState.isDarkMode),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: _isSelectingEndDate
                                    ? AppTheme.primaryColor(themeState.isDarkMode)
                                    : Colors.transparent,
                              ),
                            ),
                            child: Column(
                              children: [
                                Text(
                                  'End',
                                  style: TextStyle(
                                    color: _isSelectingEndDate
                                        ? AppTheme.primaryColor(themeState.isDarkMode)
                                        : AppTheme.textSecondary(themeState.isDarkMode),
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${_tempEndDate.day}/${_tempEndDate.month}/${_tempEndDate.year}',
                                  style: TextStyle(
                                    color: AppTheme.textPrimary(themeState.isDarkMode),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // const SizedBox(height: 12),

            // Content with slide animation
            SizedBox(
              height: 320,
              child: ClipRect(
                child: Stack(
                  children: [
                    // Start date selection
                    SlideTransition(
                      position: _slideAnimation,
                      child: _buildDateSelection(
                        isStartDate: true,
                        selectedDate: _selectedStartDate,
                        onDateChanged: _onStartDateChanged,
                        minimumDate: null,
                        maximumDate: DateTime.now(),
                      ),
                    ),

                    // End date selection
                    if (_isSelectingEndDate)
                      SlideTransition(
                        position: Tween<Offset>(
                          begin: const Offset(1.0, 0.0),
                          end: Offset.zero,
                        ).animate(CurvedAnimation(
                          parent: _slideController,
                          curve: Curves.easeInOut,
                        )),
                        child: _buildDateSelection(
                          isStartDate: false,
                          selectedDate: _tempEndDate,
                          onDateChanged: _onEndDateChanged,
                          minimumDate: _selectedStartDate,
                          maximumDate: DateTime.now(),
                          startDateReference: _selectedStartDate,
                        ),
                      ),
                  ],
                ),
              ),
            ),

            // Action buttons at bottom
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed:
                    _isSelectingEndDate ? _onDone : _goToEndDateSelection,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor(themeState.isDarkMode),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _isSelectingEndDate ? 'Done' : 'Next',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (!_isSelectingEndDate) ...[
                      const SizedBox(width: 8),
                      Icon(
                        CupertinoIcons.chevron_right,
                        color: Colors.white,
                        size: 16,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
        ));
      },
    );
  }

  Widget _buildDateSelection({
    required bool isStartDate,
    required DateTime selectedDate,
    required Function(DateTime) onDateChanged,
    DateTime? minimumDate,
    DateTime? maximumDate,
    DateTime? startDateReference,
  }) {
    return Column(
      children: [
        // Reference to start date (only shown in end date selection)
        // if (!isStartDate && startDateReference != null) ...[
        //   Container(
        //     padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        //     decoration: BoxDecoration(
        //       color: Colors.grey.withOpacity(0.1),
        //       borderRadius: BorderRadius.circular(6),
        //       border: Border.all(color: Colors.grey.withOpacity(0.3)),
        //     ),
        //     child: Row(
        //       mainAxisSize: MainAxisSize.min,
        //       children: [
        //         Text(
        //           'Start: ',
        //           style: TextStyle(
        //             color: ThemeConstants.zenWhite.withOpacity(0.7),
        //             fontSize: 12,
        //             fontWeight: FontWeight.w400,
        //           ),
        //         ),
        //         Text(
        //           '${startDateReference.day}/${startDateReference.month}/${startDateReference.year}',
        //           style: TextStyle(
        //             color: ThemeConstants.blue,
        //             fontSize: 12,
        //             fontWeight: FontWeight.w500,
        //           ),
        //         ),
        //       ],
        //     ),
        //   ),
        //   const SizedBox(height: 12),
        // ],

        // Selected date display
        // Container(
        //   padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        //   decoration: BoxDecoration(
        //     color: Colors.grey.shade900,
        //     borderRadius: BorderRadius.circular(8),
        //     border: Border.all(color: Colors.grey.withOpacity(0.3)),
        //   ),
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.center,
        //     children: [
        //       Icon(
        //         CupertinoIcons.calendar,
        //         color: ThemeConstants.zenWhite,
        //         size: 18,
        //       ),
        //       const SizedBox(width: 10),
        //       Text(
        //         '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
        //         style: const TextStyle(
        //           color: Colors.white,
        //           fontSize: 15,
        //           fontWeight: FontWeight.w500,
        //         ),
        //       ),
        //     ],
        //   ),
        // ),

        // const SizedBox(height: 16),

        // Date Picker - Use Expanded to take remaining space
        Expanded(
          child: CupertinoTheme(
            data: CupertinoThemeData(
              brightness: Brightness.dark,
              primaryColor: ThemeConstants.blue,
              textTheme: CupertinoTextThemeData(
                dateTimePickerTextStyle: TextStyle(
                  color: ThemeConstants.zenWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
                pickerTextStyle: TextStyle(
                  color: ThemeConstants.zenWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            child: CupertinoDatePicker(
              mode: CupertinoDatePickerMode.date,
              initialDateTime: selectedDate,
              minimumDate: minimumDate,
              maximumDate: maximumDate,
              onDateTimeChanged: onDateChanged,
            ),
          ),
        ),
      ],
    );
  }
}
