import 'dart:async';
import 'package:phoenix/features/websocket/model/proto/option_greeks/option_greeks.pb.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/status.dart' as status;
import 'package:flutter/foundation.dart';

class OptionGreeksWebSocketProvider {
  IOWebSocketChannel? _channel;
  final StreamController<List<ZenOptionGreeks>> _streamController = StreamController<List<ZenOptionGreeks>>.broadcast();
  final StreamController<bool> _connectionStatusController = StreamController<bool>.broadcast();
  bool _isConnected = false;

  OptionGreeksWebSocketProvider();

  /// Exposes a stream of option Greeks messages.
  Stream<List<ZenOptionGreeks>> get stream => _streamController.stream;

  /// Exposes a stream for connection status updates.
  Stream<bool> get connectionStatusStream => _connectionStatusController.stream;

  /// Connects to the WebSocket.
  void connect(String url, {Map<String, String>? headers}) {
    if (_isConnected) return;

    debugPrint("🎈 Attempting Option Greeks WebSocket connection...");

    try {
      _channel = IOWebSocketChannel.connect(url, headers: headers, connectTimeout: Duration(days: 1));
      debugPrint("🎈🎈 Option Greeks Connection open");
      _isConnected = true;
      _connectionStatusController.add(true); // Emit connected status

      var request = OptionGreeksEmptyRequest();
      _channel?.sink.add(request.writeToBuffer());
      debugPrint("🎈🎈🎈 Option Greeks Initial request sent");

      _channel?.stream.listen(
        (message) {
          _processWebSocketMessage(message);
        },
        onError: (error) {
          debugPrint("❌ Option Greeks WebSocket Error: $error");
          _isConnected = false;
          _connectionStatusController.add(false); // Emit disconnected status
          _reconnect(url, headers: headers);
        },
        onDone: () {
          debugPrint("⚠️ Option Greeks WebSocket connection closed.");
          _isConnected = false;
          _connectionStatusController.add(false); // Emit disconnected status
          _reconnect(url, headers: headers);
        },
        cancelOnError: true,
      );
    } catch (e) {
      debugPrint("❌ Exception during Option Greeks WebSocket connection: $e");
      _isConnected = false;
      _connectionStatusController.add(false); // Emit disconnected status
      _reconnect(url, headers: headers);
    }
  }

  /// Processes incoming WebSocket messages and emits option Greeks data.
  void _processWebSocketMessage(dynamic data) {
    try {
      var optionGreeksList = ZenOptionGreeksList.fromBuffer(data).zenOptionGreeks;
      debugPrint("⚡ Received ${optionGreeksList.length} option Greeks");
      _streamController.add(optionGreeksList); // Emit the full list
    } catch (e) {
      debugPrint("😶‍🌫️ Error decoding Option Greeks: $e");
    }
  }

  /// Sends a ping message to keep the connection alive.
  void sendPing(String message) {
    try {
      _channel?.sink.add(message);
    } catch (e) {
      print("😶‍🌫️ Error while sending ping to Option Greeks: $e");
    }
  }

  /// Gracefully disconnects the WebSocket connection.
  void disconnect() {
    debugPrint("🔌 Disconnecting Option Greeks WebSocket...");
    _channel?.sink.close(status.goingAway);
    _isConnected = false;
    _connectionStatusController.add(false); // Emit disconnected status
  }

  /// Attempts to reconnect after a delay.
  void _reconnect(String url, {Map<String, String>? headers}) {
    if (_isConnected) return;
    
    _connectionStatusController.add(false); // Emit disconnected status
    Future.delayed(const Duration(seconds: 30), () {
      debugPrint("♻️ Attempting to reconnect Option Greeks WebSocket...");
      connect(url, headers: headers);
    });
  }
}