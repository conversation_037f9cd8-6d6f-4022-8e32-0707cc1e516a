import 'package:flutter/material.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';

class SecurityListTextFormatter extends StatelessWidget {
  final SecurityModel security;
  final String type;

  const SecurityListTextFormatter({
    super.key,
    required this.security,
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    // If it's not an option, return the trading symbol
    if (type == 'equity' || security.expiry == null) {
      return Text(
        security.tradingSymbol,
        overflow: TextOverflow.ellipsis,
        textAlign: TextAlign.center,
        style: const TextStyle(
          color: Color(0xffADADAD),
          fontWeight: FontWeight.w400,
          fontSize: 15,
        ),
      );
    }

    final symbol =
        SecurityListTextFormatter._extractUnderlying(security.tradingSymbol);
    final expiryDate = DateTime.parse(security.expiry!);
    final day = expiryDate.day;
    final year = expiryDate.year.toString().substring(2);
    final showYear = year != DateTime.now().year.toString().substring(2);
    final suffix = SecurityListTextFormatter._getDaySuffix(day);

    if (security.instrumentType == 'FUT') {
      final type = security.instrumentType.toUpperCase(); // FUT
      return Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: '$symbol ',
              style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
            ),
            if (showYear)
              TextSpan(
                text: '$year ',
                style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
              ),
            TextSpan(
              text: '$day',
              style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
            ),
            WidgetSpan(
              alignment: PlaceholderAlignment.top,
              child: Transform.translate(
                offset: const Offset(1.5, -5),
                child: Text(
                  suffix,
                  style: const TextStyle(
                    fontSize: 9,
                    color: Color(0xffADADAD),
                  ),
                ),
              ),
            ),
            TextSpan(
              text:
                  ' ${SecurityListTextFormatter._monthName(expiryDate.month)} $type',
              style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
            ),
          ],
        ),
        overflow: TextOverflow.ellipsis,
      );
    }

    // For options (CE/PE)
    final strike = security.strike.toStringAsFixed(0);
    final typeStr = security.instrumentType.toUpperCase(); // CE or PE

    if (security.expiryType == "MONTHLY") {
      return Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: symbol,
              style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
            ),
            if (showYear)
              TextSpan(
                text: '$year ',
                style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
              ),
            
            TextSpan(
              text:
                  ' ${SecurityListTextFormatter._monthName(expiryDate.month)} $strike $typeStr',
              style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
            ),
          ],
        ),
        overflow: TextOverflow.ellipsis,
      );
    }

    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: symbol,
            style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
          ),
          if (showYear)
            TextSpan(
              text: '$year ',
              style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
            ),
          TextSpan(
            text: ' $day',
            style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
          ),
          WidgetSpan(
            alignment: PlaceholderAlignment.top,
            child: Transform.translate(
              offset: const Offset(1.5, -5),
              child: Text(
                suffix,
                style: const TextStyle(
                  fontSize: 9,
                  color: Color(0xffADADAD),
                ),
              ),
            ),
          ),
          TextSpan(
            text:
                ' ${SecurityListTextFormatter._monthName(expiryDate.month)} $strike $typeStr',
            style: const TextStyle(color: Color(0xffADADAD), fontSize: 15),
          ),
        ],
      ),
      overflow: TextOverflow.ellipsis,
    );
  }

  // Instance methods now call static helpers
  static String format(SecurityModel security, String type) {
    if (type == 'equity' || security.expiry == null) {
      return security.tradingSymbol;
    }
    final symbol = _extractUnderlying(security.tradingSymbol);
    final expiryDate = DateTime.parse(security.expiry!);
    final day = expiryDate.day;
    final year = expiryDate.year.toString().substring(2);
    final showYear = year != DateTime.now().year.toString().substring(2);
    final suffix = _getDaySuffix(day);
    if (security.instrumentType == 'FUT') {
      final typeStr = security.instrumentType.toUpperCase();
      return [
        symbol,
        if (showYear) year,
        '$day$suffix',
        _monthName(expiryDate.month),
        typeStr
      ].join(' ').replaceAll('  ', ' ').trim();
    }
    // For options (CE/PE)
    final strike = security.strike.toStringAsFixed(0);
    final typeStr = security.instrumentType.toUpperCase();
    return [
      symbol,
      if (showYear) year,
      if(security.expiryType != "MONTHLY") '$day$suffix',
      _monthName(expiryDate.month),
      strike,
      typeStr
    ].join(' ').replaceAll('  ', ' ').trim();
  }

  static String _extractUnderlying(String tradingSymbol) {
    final regex = RegExp(r'^[A-Z]+');
    final match = regex.firstMatch(tradingSymbol);
    return match?.group(0) ?? tradingSymbol;
  }

  static String _getDaySuffix(int day) {
    if (day >= 11 && day <= 13) return 'th';
    switch (day % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }

  static String _monthName(int month) {
    const months = [
      '',
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month];
  }
}
