import 'package:flutter/material.dart';


class TestScreen extends StatefulWidget {
  const TestScreen({Key? key}) : super(key: key);

  @override
  State<TestScreen> createState() => _PortfolioScreenState();
}

class _PortfolioScreenState extends State<TestScreen> {
  bool showOpenPositions = true;

  // Mock data for positions
  final List<Map<String, dynamic>> openedPositions = [
    {
      'name': 'NIFTYBEES',
      'exchange': '(BSE)',
      'qty': 120,
      'price': 14700.54,
      'ltp': 277.60,
      'ltpChange': 0.28,
      'profit': 1230.90,
      'profitPercentage': 4.28,
      'avg': 249.56,
    },
    {
      'name': 'IOC',
      'exchange': '',
      'qty': 120,
      'price': 14700.54,
      'ltp': 277.60,
      'ltpChange': 0.28,
      'profit': 1230.90,
      'profitPercentage': 4.28,
      'avg': 249.56,
    },
    {
      'name': 'GOLDBEES',
      'exchange': '',
      'qty': 120,
      'price': 14700.54,
      'ltp': 277.60,
      'ltpChange': 0.28,
      'profit': 1230.90,
      'profitPercentage': 4.28,
      'avg': 249.56,
    },
    {
      'name': 'MRF',
      'exchange': '',
      'qty': 120,
      'price': 14700.54,
      'ltp': 277.60,
      'ltpChange': 0.28,
      'profit': 1230.90,
      'profitPercentage': 4.28,
      'avg': 249.56,
    },
  ];

  final List<Map<String, dynamic>> closedPositions = [
    {
      'name': 'RELIANCE',
      'exchange': '(NSE)',
      'qty': 50,
      'price': 2500.75,
      'ltp': 2510.60,
      'ltpChange': 0.39,
      'profit': 492.50,
      'profitPercentage': 0.39,
      'avg': 2500.75,
    },
    {
      'name': 'HDFCBANK',
      'exchange': '(NSE)',
      'qty': 75,
      'price': 1650.30,
      'ltp': 1645.20,
      'ltpChange': -0.31,
      'profit': -382.50,
      'profitPercentage': -0.31,
      'avg': 1650.30,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: () {},
          ),
        ],
      ),
      body: Column(
        children: [
          // Fixed position titles that act as tabs
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Open Positions Title (always visible)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      showOpenPositions = true;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Text(
                          'Opened Positions (${openedPositions.length})',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: showOpenPositions ? Colors.white : Colors.grey,
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (showOpenPositions)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.blue,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                
                // Closed Positions Title (always visible)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      showOpenPositions = false;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Text(
                          'Closed Positions (${closedPositions.length})',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: !showOpenPositions ? Colors.white : Colors.grey,
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (!showOpenPositions)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.blue,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Content area with animated switching
          Expanded(
            child: Stack(
              children: [
                // Open Positions Content
                AnimatedOpacity(
                  opacity: showOpenPositions ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 300),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    transform: Matrix4.translationValues(
                      0, 
                      showOpenPositions ? 0 : -50, 
                      0
                    ),
                    child: showOpenPositions 
                      ? ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          itemCount: openedPositions.length,
                          itemBuilder: (context, index) {
                            final position = openedPositions[index];
                            return PositionCard(position: position);
                          },
                        )
                      : const SizedBox.shrink(),
                  ),
                ),
                
                // Closed Positions Content
                AnimatedOpacity(
                  opacity: !showOpenPositions ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 300),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    transform: Matrix4.translationValues(
                      0, 
                      !showOpenPositions ? 0 : 50, 
                      0
                    ),
                    child: !showOpenPositions 
                      ? ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          itemCount: closedPositions.length,
                          itemBuilder: (context, index) {
                            final position = closedPositions[index];
                            return PositionCard(position: position);
                          },
                        )
                      : const SizedBox.shrink(),
                  ),
                ),
              ],
            ),
          ),
          
          // Portfolio Net Worth Section
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: const Color(0xFF252525),
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: Column(
              children: [
                const Text(
                  "Portfolio's Net Worth",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Invested',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        Row(
                          children: [
                            const Icon(Icons.circle, color: Colors.green, size: 12),
                            const SizedBox(width: 4),
                            Text(
                              '₹24,080.52',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.arrow_drop_down, color: Colors.red, size: 16),
                            Text(
                              '₹1,622.22',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.red,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          '-5.32%',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        const Text(
                          'Current',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        Row(
                          children: [
                            const Icon(Icons.arrow_drop_down, color: Colors.red, size: 16),
                            Text(
                              '₹22,458.30',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      "Day's P&L",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    Row(
                      children: [
                        const Icon(Icons.arrow_drop_up, color: Colors.green, size: 16),
                        Text(
                          '₹1,062.74',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '(*****%)',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          
        ],
      ),
    );
  }
}

class PositionCard extends StatelessWidget {
  final Map<String, dynamic> position;

  const PositionCard({
    Key? key,
    required this.position,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: const Color(0xFF252525),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(
                    position['name'],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    position['exchange'],
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  const Text(
                    'Qty. ',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    '${position['qty']}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Icon(Icons.edit, size: 14, color: Colors.blue),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Icon(Icons.remove_red_eye, size: 14, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    '₹${position['price']}',
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              Text(
                '+${position['profit']}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Text(
                    'LTP ',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    '${position['ltp']}',
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    ' (+${position['ltpChange']}%)',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              Text(
                '+${position['profitPercentage']}%',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const Text(
                'Avg. ',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              Text(
                '${position['avg']}',
                style: const TextStyle(
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
