import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BiometricService {
  final LocalAuthentication _localAuth = LocalAuthentication();
  final String _biometricEnabledKey = 'biometric_enabled';

  Future<bool> isBiometricsAvailable() async {
    try {
      final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();
      return canCheckBiometrics && isDeviceSupported;
    } on PlatformException catch (e) {
      debugPrint('Error checking biometrics: $e');
      return false;
    }
  }

  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } on PlatformException catch (e) {
      debugPrint('Error getting available biometrics: $e');
      return [];
    }
  }

  Future<bool> authenticate() async {
    try {
      final available = await isBiometricsAvailable();
      if (!available) {
        debugPrint('Biometrics not available');
        return false;
      }

      final List<BiometricType> availableBiometrics = 
          await getAvailableBiometrics();
      
      if (availableBiometrics.isEmpty) {
        debugPrint('No biometrics enrolled');
        return false;
      }

      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to access Phoenix',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
          useErrorDialogs: true,
        ),
      );

      debugPrint('Authentication result: $didAuthenticate');
      return didAuthenticate;
    } on PlatformException catch (e) {
      debugPrint('Authentication error: $e');
      // Handle authentication cancellation specifically
      if (e.code == 'auth_in_progress' || e.code == 'NotAvailable' || e.code == 'UserCanceled') {
        return false;
      }
      return false;
    }
  }

  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      if (enabled) {
        // Verify biometrics are available before enabling
        final available = await isBiometricsAvailable();
        if (!available) {
          throw PlatformException(
            code: 'NO_BIOMETRICS',
            message: 'Biometrics not available on this device',
          );
        }
      }
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_biometricEnabledKey, enabled);
    } catch (e) {
      debugPrint('Error setting biometric enabled: $e');
      rethrow;
    }
  }

  Future<bool> isBiometricEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_biometricEnabledKey) ?? false;
    } catch (e) {
      debugPrint('Error checking if biometric enabled: $e');
      return false;
    }
  }
}

