import UIKit
import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        let controller = window?.rootViewController as! FlutterViewController
        let channel = FlutterMethodChannel(
            name: "keyboard_done_button",
            binaryMessenger: controller.binaryMessenger)
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillShow),
            name: UIResponder.keyboardWillShowNotification,
            object: nil
        )
        
        channel.setMethodCallHandler { [weak self] (call, result) in
            if call.method == "addDoneButton" {
                DispatchQueue.main.async {
                    self?.findAndAddDoneButtonToAllWindows()
                }
                result(true)
            } else {
                result(FlutterMethodNotImplemented)
            }
        }
        
        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    @objc private func keyboardWillShow(_ notification: Notification) {
        DispatchQueue.main.async {
            self.findAndAddDoneButtonToAllWindows()
        }
    }
    
    private func findAndAddDoneButtonToAllWindows() {
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            for window in windowScene.windows {
                findAndAddDoneButton(in: window)
            }
        }
    }
    
    private func findAndAddDoneButton(in view: UIView) {
        // First, handle any text fields in the current view
        if let textField = view as? UITextField {
            addDoneButton(to: textField)
        }
        
        // Then recursively check all subviews
        for subview in view.subviews {
            findAndAddDoneButton(in: subview)
        }
    }
    
    private func addDoneButton(to textField: UITextField) {
        // Check if toolbar is already added
        if textField.inputAccessoryView is UIToolbar {
            return
        }
        
        let toolbar = UIToolbar(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 44))
        toolbar.barStyle = .default
        
        let flexSpace = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
        let doneButton = UIBarButtonItem(barButtonSystemItem: .done, target: self, action: #selector(dismissKeyboard))
        
        toolbar.items = [flexSpace, doneButton]
        toolbar.sizeToFit()
        
        textField.inputAccessoryView = toolbar
    }
    
    @objc private func dismissKeyboard() {
        DispatchQueue.main.async {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

extension UIView {
    func findTextField() -> [UITextField] {
        var textFields: [UITextField] = []
        
        for view in self.subviews {
            if let textField = view as? UITextField {
                textFields.append(textField)
            } else {
                textFields.append(contentsOf: view.findTextField())
            }
        }
        
        return textFields
    }
}
