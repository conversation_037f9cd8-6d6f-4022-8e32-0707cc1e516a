part of 'margin_state_bloc.dart';

@immutable
sealed class MarginState {}

final class MarginInitial extends MarginState {}

final class MarginLoading extends MarginState {}

final class MarginLoaded extends MarginState {
  final double margin;

  MarginLoaded({required this.margin});
}

final class MarginMultipleLoaded extends MarginState {
  final Map<int, MarginData> margins; // accountId -> MarginData

  MarginMultipleLoaded({required this.margins});
}

final class MarginError extends MarginState {
  final String error;

  MarginError({required this.error});
}