import 'dart:async';
import 'dart:convert';
import 'package:phoenix/features/websocket/data/provider/web_socket_provider.dart';
import 'package:phoenix/features/websocket/model/tick_model.dart';

class WebSocketRepository {
  final WebSocketProvider _provider;
  final String url;
  late final Stream<TickModel> _tickStream;

  WebSocketRepository(this.url, this._provider);

  void connect(Map<String, String> map, {Map<String, String>? headers}) {
    try {
      final stream = _provider.connect(url, headers: headers);

      _tickStream = stream.map<TickModel>((data) {
        try {
          final decoded = jsonDecode(data);
          return TickModel.fromJson(decoded);
        } catch (e) {
          print("Error decoding data: $e");
          throw Exception("Invalid data format received");
        }
      }).handleError((error) {
        print("Error in WebSocket stream: $error");
        throw Exception(error); // Propagate error
      });
    } catch (e) {
      print("Error during WebSocket connection: $e");
      rethrow; // Alert Bloc/UI
    }
  }

  Stream<TickModel> get tickStream => _tickStream;

  void disconnect() {
    try {
      _provider.disconnect();
    } catch (e) {
      print('Error while disconnecting WebSocket: $e');
    }
  }

  void sendMessage(String message) {
    try {
      _provider.sendPing(message);
    } catch (e) {
      print('Error while sending message: $e');
    }
  }
}
