import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:provider/provider.dart';
import 'package:web_socket_channel/io.dart';

class TickStream extends StatefulWidget {
  const TickStream({super.key});

  @override
  State<TickStream> createState() => _TickStreamState();
}

class _TickStreamState extends State<TickStream> with WidgetsBindingObserver {
  dynamic _channel;
  late Stream stream;

  Timer? _pingTimer;
  bool isConnecting = false; // Flag to avoid duplicate reconnection attempts

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this); // Add observer for app lifecycle changes
    try {
      startConnection();
      debugPrint("WebSocket connection initiated");
    } catch (e) {
      debugPrint("WebSocket connection error: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Text(
            'Random tick Data from Server:',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 20),
          StreamBuilder(
            stream: stream,
            builder: streamBuilderMethod,
          ),
          const SizedBox(height: 20),
          ElevatedButton(
              onPressed: startConnection, child: const Text("Start")),
          const SizedBox(height: 20),
          ElevatedButton(onPressed: stopConnection, child: const Text("Stop")),
        ],
      ),
    );
  }

  Widget streamBuilderMethod(context, snapshot) {
    if (snapshot.connectionState == ConnectionState.waiting) {
      return const Text(
        'Waiting for connection...',
        style: TextStyle(fontSize: 16, color: Colors.white),
      );
    } else if (snapshot.hasError) {
      reconnect();
      return Text(
        'Error: ${snapshot.error}',
        style: const TextStyle(color: Colors.red, fontSize: 16),
      );
    } else if (snapshot.hasData) {
      var data = jsonDecode(snapshot.data);
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'index ${data['index']}',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          Text(
            'Instrument token ${data['data']['instrument_token']}',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          Text(
            'last price ${data['data']['last_price']}',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          Text(
            'last_traded_quantity ${data['data']['last_traded_quantity']}',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
        ],
      );
    } else {
      return const Text(
        'No data received yet.',
        style: TextStyle(fontSize: 16,color: Colors.red),
      );
    }
  }

  void startConnection() {
    if (isConnecting) return; // Avoid duplicate attempts
    isConnecting = true;

    final authState = context.read<AuthBloc>().state;
    if (authState is! AuthAuthenticated) {
      debugPrint("No authentication credentials available");
      isConnecting = false;
      return;
    }
    try {
      debugPrint("Attempting to establish WebSocket connection...");
      _channel = IOWebSocketChannel.connect('ws://192.168.10.5:8765',headers: {
        'Authorization': 'Bearer ${authState.credentialsModel.accessToken}'
      });
      stream = _channel.stream;
      _startPing(); // Start sending periodic pings
      setState(() {});
      
    } catch (e) {
      debugPrint("WebSocket connection error: $e");
      reconnect(); // Attempt to reconnect
    } finally {
      isConnecting = false; // Reset the flag
    }
  }


  void _startPing() {
    _pingTimer?.cancel();
    _pingTimer = Timer.periodic(const Duration(seconds: 60), (timer) {
      try {
        _channel.sink.add('ping'); // Send a ping message
        debugPrint("Ping sent to server");
      } catch (e) {
        debugPrint("Ping error: $e");
      }
    });
  }

  void stopConnection() {
    _pingTimer?.cancel(); // Stop the ping timer
    _channel.sink.close();
    setState(() {});
  }

  void reconnect() {
    if (isConnecting) return;
    stopConnection();
    Future.delayed(const Duration(seconds: 3), () {
      debugPrint("Reconnecting...");
      startConnection();
    });
  }

  void instantReconnet() {
    if (isConnecting) return;
    stopConnection();
    debugPrint("Instant Reconnecting...");
    Future.delayed(const Duration(seconds: 0), () {
      startConnection();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this); // Remove observer
    _pingTimer?.cancel();
    _channel.sink.close();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.paused) {
      // App goes to background
      debugPrint("App moved to background. Stopping connection...");
      stopConnection();
    } else if (state == AppLifecycleState.resumed) {
      // App comes back to foreground
      debugPrint("App resumed. Reconnecting...");
     instantReconnet();
    }
  }
}
