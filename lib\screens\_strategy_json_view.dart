import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class StrategyJsonView extends StatelessWidget {
  final String jsonString;
  const StrategyJsonView({required this.jsonString});

  @override
  Widget build(BuildContext context) {
    Map<String, dynamic> data;
    try {
      data = jsonDecode(jsonString);
    } catch (e) {
      return Center(child: Text('Invalid JSON', style: TextStyle(color: ThemeConstants.titleRedColor)));
    }
    return Scaffold(
      backgroundColor: ThemeConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: ThemeConstants.backgroundColor,
        elevation: 0,
        title: Text('Strategy JSON', style: TextStyle(color: ThemeConstants.zenWhite)),
        iconTheme: IconThemeData(color: ThemeConstants.zenWhite),
        leading: BackButton(
          color: ThemeConstants.zenWhite,
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Container(
          decoration: BoxDecoration(
            color: ThemeConstants.zenBlack,
            borderRadius: BorderRadius.circular(18),
            boxShadow: ThemeConstants.neomorpicShadow,
          ),
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: SelectableText(
              const JsonEncoder.withIndent('  ').convert(data),
              style: TextStyle(
                color: ThemeConstants.zenWhite,
                fontFamily: 'FiraMono',
                fontSize: 15,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
