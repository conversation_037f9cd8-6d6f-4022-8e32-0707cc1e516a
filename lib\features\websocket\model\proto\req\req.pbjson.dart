//
//  Generated code. Do not modify.
//  source: req.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use zenTicksEmptyRequestDescriptor instead')
const ZenTicksEmptyRequest$json = {
  '1': 'ZenTicksEmptyRequest',
};

/// Descriptor for `ZenTicksEmptyRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List zenTicksEmptyRequestDescriptor = $convert.base64Decode(
    'ChRaZW5UaWNrc0VtcHR5UmVxdWVzdA==');

