import 'package:phoenix/features/common/position_comp_key.dart';

class OrderResponseModel {
  final PositionCompKey positionCompKey;
  final int zenOrderId;
  final String status;
  final String message;
  final String tradingSymbol;
  final String methodType;

  OrderResponseModel({
    required this.positionCompKey,
    required this.zenOrderId,
    required this.status,
    required this.message,
    required this.tradingSymbol,
    required this.methodType,
  });

  factory OrderResponseModel.fromJson(Map<String, dynamic> json) {
    return OrderResponseModel(
      positionCompKey: PositionCompKey.fromJson(json['position_comp_key']),
      zenOrderId: json['zen_order_id'],
      status: json['status'],
      message: json['message'],
      tradingSymbol: json['trading_symbol'],
      methodType: json['method_type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'position_comp_key': positionCompKey.toJson(),
      'zen_order_id': zenOrderId,
      'status': status,
      'message': message,
      'trading_symbol': tradingSymbol,
      'method_type': methodType,
    };
  }
}

class OrderResponseModelForBucket {
  final String status;
  final String message;
  OrderResponseModelForBucket({
    required this.status,
    required this.message,
  });

  factory OrderResponseModelForBucket.fromJson(Map<String, dynamic> json) {
    return OrderResponseModelForBucket(
      status: json['status'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
    };
  }
}
