import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class AppTabBar extends StatelessWidget {
  final String title1;
  final String title2;
  final bool isTitleOnly;
  const AppTabBar(
      {super.key,
      required this.title1,
      required this.title2,
      this.isTitleOnly = false});

  @override
  Widget build(BuildContext context) {
    if (isTitleOnly) {
      return Container(
        width: MediaQuery.of(context).size.width * 0.90,
        padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),

        alignment: Alignment.center,
        child: TabBar(
          isScrollable: false,
          indicatorColor: ThemeConstants.blue,
          indicatorPadding: EdgeInsets.symmetric(vertical: -1),
          // Custom indicator to control width and alignment
          indicator: UnderlineTabIndicator(
            borderSide: BorderSide(width: 1.0, color: ThemeConstants.blue),
            insets: EdgeInsets.only(
              right: 30.0,
              left: 20.0,
            ), // Adjust the right inset to control indicator width
          ),
          labelColor: ThemeConstants.blue,
          unselectedLabelColor: Colors.white,
          dividerColor: Colors.transparent,
          tabs: [
            Container(
              height: 32,
              width: MediaQuery.of(context).size.width * 0.50,
              padding: const EdgeInsets.only(
                right: 34.0,
              ), // Adjust padding for divider spacing

              child: Tab(
                child: Text(
                  title1,
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }
    return Container(
      width: MediaQuery.of(context).size.width * 0.90,
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
      alignment: Alignment.center,
      child: TabBar(
        isScrollable: false,
        indicatorColor: ThemeConstants.blue,
        indicatorPadding: EdgeInsets.symmetric(vertical: -1),
        // Custom indicator to control width and alignment
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(width: 1.0, color: ThemeConstants.blue),
          insets: EdgeInsets.only(
            right: 30.0,
            left: 20.0,
          ), // Adjust the right inset to control indicator width
        ),
        labelColor: ThemeConstants.blue,
        unselectedLabelColor: Colors.white,
        dividerColor: Colors.transparent,
        tabs: [
          Container(
            height: 32,
            width: MediaQuery.of(context).size.width * 0.50,
            padding: const EdgeInsets.only(
              right: 34.0,
            ), // Adjust padding for divider spacing
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  color: Colors.white,
                  width: 0.5,
                ),
              ),
            ),
            child: Tab(
              child: Text(
                title1,
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
          Container(
            height: 32,
            padding: const EdgeInsets.only(
              right: 30.0,
            ),
            child: Tab(
              child: Text(
                title2,
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
