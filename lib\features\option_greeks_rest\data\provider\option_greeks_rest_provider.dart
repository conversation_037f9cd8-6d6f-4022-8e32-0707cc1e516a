import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:phoenix/features/option_greeks_rest/model/option_greeks_rest_model.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/http_service.dart';

class OptionGreeksRestProvider {
  final HttpService _httpService = HttpService();
  

  /// Fetches option greeks data from REST API
  Future<OptionGreeksRestResponse> fetchOptionGreeks() async {
    try {
      debugPrint('🌐 Fetching option greeks from REST API...');
      
      final response = await _httpService.get(
        Uri.parse(ApiPath.getIndexOptionGreeks()),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      debugPrint('📡 Option greeks REST API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final optionGreeksResponse = OptionGreeksRestResponse.fromJson(jsonData);
        
        debugPrint('✅ Successfully fetched ${optionGreeksResponse.payload.length} option greeks from REST API');
        return optionGreeksResponse;
      } else {
        debugPrint('❌ Failed to fetch option greeks: ${response.statusCode} - ${response.body}');
        throw HttpException(
          'Failed to fetch option greeks: ${response.statusCode}',
          uri: Uri.parse(ApiPath.getIndexOptionGreeks()),
        );
      }
    } catch (e) {
      debugPrint('💥 Error fetching option greeks from REST API: $e');
      rethrow;
    }
  }
}