part of 'option_greeks_rest_bloc.dart';

abstract class OptionGreeksRestState {}

class OptionGreeksRestInitial extends OptionGreeksRestState {}

class OptionGreeksRestLoading extends OptionGreeksRestState {}

class OptionGreeksRestLoaded extends OptionGreeksRestState {
  final List<OptionGreeksRestData> optionGreeks;
  final DateTime lastUpdated;

  OptionGreeksRestLoaded({
    required this.optionGreeks,
    required this.lastUpdated,
  });
}

class OptionGreeksRestError extends OptionGreeksRestState {
  final String message;

  OptionGreeksRestError(this.message);
}