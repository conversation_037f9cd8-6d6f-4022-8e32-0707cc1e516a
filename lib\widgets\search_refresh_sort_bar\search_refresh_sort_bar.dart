import 'package:flutter/material.dart';
import 'package:phoenix/services/sort_service.dart';

class SearchRefreshSortBar extends StatelessWidget {
  final Function refresh;
  final Function toggleSearch;
  final Function showSortOptions;
  final SortOption? currentSortOption;
  final bool isAscending;

  const SearchRefreshSortBar({super.key, required this.refresh, required this.toggleSearch, required this.showSortOptions, this.currentSortOption, required this.isAscending});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 18,
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 24, vertical: 1),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          InkWell(
            onTap: (){
              refresh();
            },
            child: ImageIcon(
              AssetImage("images/general/refresh.png"),
              color: Color(0xffCDCDCD),
            ),
          ),
          SizedBox(width: 4),
          InkWell(
            onTap: (){
              toggleSearch();
            },
            child: ImageIcon(
              AssetImage("images/general/search.png"),
              color: Color(0xffCDCDCD),
            ),
          ),
          SizedBox(width: 4),
          InkWell(
            onTap: (){
              showSortOptions();
            },
            child: ImageIcon(
              AssetImage("images/general/sort.png"),
              color:
                  currentSortOption != null ? Colors.blue : Color(0xffCDCDCD),
            ),
          ),
        ],
      ),
    );
  }
}
