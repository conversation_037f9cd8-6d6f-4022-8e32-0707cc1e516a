part of 'auth_bloc.dart';

@immutable
sealed class AuthState {}

final class AuthInitial extends AuthState {}

final class AuthAuthenticated extends AuthState {
  final CredentialsModel credentialsModel;

  AuthAuthenticated({required this.credentialsModel});
}

final class AuthUnauthenticated extends AuthState {}

final class AuthLoading extends AuthState {}

class AuthError extends AuthState {
  final String errorMessage;

  AuthError({required this.errorMessage});
}

