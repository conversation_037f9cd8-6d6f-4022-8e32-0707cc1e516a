# Keep Tink and related classes
-keep class com.google.crypto.tink.** { *; }
-keep class com.google.api.client.http.** { *; }
-keep class com.google.errorprone.annotations.** { *; }
-keep class org.joda.time.** { *; }

# Keep all classes referenced by Tink
-keep class * implements com.google.crypto.tink.proto.KeyTypeManager {
    *;
}
-keep class * implements com.google.crypto.tink.shaded.protobuf.MessageLiteOrBuilder {
    *;
}