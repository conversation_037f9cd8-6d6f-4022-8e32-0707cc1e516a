import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';

class AppTabBar2 extends StatelessWidget {
  final int count1;
  final int count2;
  final int? count3;
  final String title1;
  final String title2;
  final String? title3;
  final TabController? controller;

  const AppTabBar2({
    super.key,
    required this.count1,
    required this.count2,
    required this.title1,
    required this.title2,
    this.count3,
    this.title3,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        final tabs = <Widget>[
      Container(
        height: 32,
        child: Tab(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                fit: FlexFit.loose,
                child: Text(
                  title1,
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              SizedBox(width: 8),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  "$count1",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor(themeState.isDarkMode),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      Container(
        height: 32,
        child: Tab(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                fit: FlexFit.loose,
                child: Text(
                  title2,
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              SizedBox(width: 8),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  "$count2",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                   color: AppTheme.primaryColor(themeState.isDarkMode),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ];
    if (title3 != null && count3 != null) {
      tabs.add(
        Container(
          height: 32,
          child: Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  fit: FlexFit.loose,
                  child: Text(
                    title3!,
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    "$count3",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor(themeState.isDarkMode),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
    return Container(
      width: MediaQuery.of(context).size.width * 0.90,
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
      alignment: Alignment.center,
      child: TabBar(
        controller: controller,
        isScrollable: false,
        indicatorColor: AppTheme.primaryColor(themeState.isDarkMode),
        indicatorPadding: EdgeInsets.symmetric(vertical: -1),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(width: 1.0, color: AppTheme.primaryColor(themeState.isDarkMode)),
          insets: EdgeInsets.only(
            right: 30.0,
            left: 20.0,
          ),
        ),
        labelColor: AppTheme.primaryColor(themeState.isDarkMode),
        unselectedLabelColor: AppTheme.textPrimary(themeState.isDarkMode),
        dividerColor: Colors.transparent,
        labelPadding: EdgeInsets.zero,
        tabs: tabs,
      ),
        );
      },
    );
  }
}
