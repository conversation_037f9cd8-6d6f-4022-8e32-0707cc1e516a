import 'package:phoenix/features/orders_state/data/provider/order_state_provider.dart';
import 'package:phoenix/features/orders_state/model/unified_order_data.dart';
import 'package:phoenix/utils/app_exception.dart';


class OrderStateRepository {
  final OrderStateProvider provider;

  OrderStateRepository({required this.provider});

  Future<List<UnifiedOrderData>> getUnifiedOrderState( int clientId)  async {
   try {
      final List<UnifiedOrderData> data = await provider.getUnifiedOrderState(clientId );
      return data;
    } catch (e) {
      throw e is AppException ? e : AppException(e.toString());
    }
  }
}
