import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class MarketTrendArrow extends StatelessWidget {
  const MarketTrendArrow({
    super.key,
    required this.isPositiveChange,
  });

  final bool isPositiveChange;

  @override
  Widget build(BuildContext context) {
    return ImageIcon(AssetImage(isPositiveChange ? "images/arrows/up.png" : "images/arrows/down.png"),
      color: isPositiveChange
          ? ThemeConstants.netWorthGreenColor
          : ThemeConstants.netWorthRedColor,
      size: 12,
    );
  }
}
