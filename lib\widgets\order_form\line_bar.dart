import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class LineBar extends StatelessWidget {
  const LineBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          padding: EdgeInsets.all(2),
          child: Divider(
            color: AppTheme.borderColor(themeState.isDarkMode).withOpacity(0.6),
            thickness: 3.5,
            height: 15,
            endIndent: 125,
            indent: 125,
          ),
        );
      },
    );
  }
}
