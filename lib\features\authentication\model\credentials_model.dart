class CredentialsModel {
  final String accessToken;
  final String refreshToken;
  final String idToken;
  final String name;
  final String email;
  final String pictureUrl;
  final String userId;
  final String zenUserId;
  final int clientId;
  final String clientName;
  final List<BrokerInfo> brokers;

  CredentialsModel({
    required this.accessToken,
    required this.refreshToken,
    required this.idToken,
    required this.name,
    required this.email,
    required this.pictureUrl,
    required this.userId,
    required this.zenUserId,
    required this.clientId,
    required this.clientName,
    required this.brokers,
  });

  // Factory to parse from JSON
  factory CredentialsModel.fromJson(Map<String, dynamic> json) {
    return CredentialsModel(
      accessToken: json['accessToken'],
      refreshToken: json['refreshToken'],
      idToken: json['idToken'],
      name: json['name'],
      email: json['email'],
      pictureUrl: json['pictureUrl'],
      userId: json['userId'],
      zenUserId: json['zenUserId'],
      clientId: json['clientId'],
      clientName: json['clientName'],
      brokers: (json['brokers'] as List<dynamic>)
          .map((broker) => BrokerInfo.fromJson(broker))
          .toList(),
    );
  }

  // Method to serialize to JSON
  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'idToken': idToken,
      'name': name,
      'email': email,
      'pictureUrl': pictureUrl,
      'userId': userId,
      'zenUserId': zenUserId,
      'clientId': clientId,
      'clientName': clientName,
      'brokers': brokers.map((broker) => broker.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'CredentialsModel(accessToken: , refreshToken: , idToken: , name: $name, email: $email, pictureUrl: $pictureUrl, userId: $userId, zenUserId: $zenUserId, clientId: $clientId, clientName: $clientName, brokers: ${brokers.toString()})';
  }

  // Fetch all brokers
  List<Map<String, dynamic>> getBrokers() {
    return brokers.map((broker) {
      return {'brokerId': broker.brokerId, 'brokerName': broker.brokerName};
    }).toList();
  }

  // Fetch accounts for a given broker ID
  List<AccountInfo> getAccounts(int brokerId) {
    final broker = brokers.firstWhere(
      (broker) => broker.brokerId == brokerId,
      orElse: () => throw Exception('Broker with ID $brokerId not found'),
    );
    return broker.accounts;
  }

  // Fetch strategies for a given account ID
  List<Strategy> getStrategies(int accountId) {
    for (final broker in brokers) {
      for (final account in broker.accounts) {
        if (account.accountId == accountId) {
          return account.strategies;
        }
      }
    }
    throw Exception('Account with ID $accountId not found');
  }

  // Copy with
  CredentialsModel copyWith({
    String? accessToken,
    String? refreshToken,
    String? idToken,
    String? name,
    String? email,
    String? pictureUrl,
    String? userId,
    int? clientId,
    String? clientName,
    String? zenUserId,
    List<BrokerInfo>? brokers,
  }) {
    return CredentialsModel(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      idToken: idToken ?? this.idToken,
      name: name ?? this.name,
      email: email ?? this.email,
      pictureUrl: pictureUrl ?? this.pictureUrl,
      userId: userId ?? this.userId,
      zenUserId: zenUserId ?? this.zenUserId,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      brokers: brokers ?? this.brokers,
    );
  }

  // Getter to update clientId
  CredentialsModel updateClientId(int newClientId) {
    return copyWith(clientId: newClientId);
  }
}

class BrokerInfo {
  final int brokerId;
  final String brokerName;
  final List<AccountInfo> accounts;

  BrokerInfo({required this.brokerId, required this.brokerName, required this.accounts});

  factory BrokerInfo.fromJson(Map<String, dynamic> json) {
    return BrokerInfo(
      brokerId: json['broker_id'],
      brokerName: json['broker_name'],
      accounts: (json['account_infos'] as List<dynamic>)
          .map((account) => AccountInfo.fromJson(account))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'broker_id': brokerId,
      'broker_name': brokerName,
      'account_infos': accounts.map((account) => account.toJson()).toList(),
    };
  }
  @override
  String toString() {
    return 'BrokerInfo(brokerId: $brokerId, brokerName: $brokerName, accounts: $accounts)';
  }
}

class AccountInfo {
  final int accountId;
  final String accountName;
  final List<Strategy> strategies;

  AccountInfo({required this.accountId, required this.accountName, required this.strategies});

  factory AccountInfo.fromJson(Map<String, dynamic> json) {
    return AccountInfo(
      accountId: json['account_id'],
      accountName: json['account_name'],
      strategies: (json['strategies'] as List<dynamic>)
          .map((strategy) => Strategy.fromJson(strategy))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'account_id': accountId,
      'account_name': accountName,
      'strategies': strategies.map((strategy) => strategy.toJson()).toList(),
    };
  }
  @override
  String toString() {
    return 'AccountInfo(accountId: $accountId, accountName: $accountName, strategies: $strategies)';
  }
}

class Strategy {
  final int strategyId;
  final String strategyName;

  Strategy({required this.strategyId, required this.strategyName});

  factory Strategy.fromJson(Map<String, dynamic> json) {
    return Strategy(
      strategyId: json['strategy_id'],
      strategyName: json['strategy_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'strategy_id': strategyId,
      'strategy_name': strategyName,
    };
  }
  @override
  String toString() {
    return 'Strategy(strategyId: $strategyId, strategyName: $strategyName)';
  }
}
