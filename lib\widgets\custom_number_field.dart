import 'dart:io' show Platform;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomNumberField extends StatefulWidget {
  final TextEditingController controller;
  final String? labelText;
  final String? hintText;
  final void Function(String)? onChanged;
  final bool enabled;
  final TextStyle? style;
  final InputDecoration? decoration;
  final List<TextInputFormatter>? formattersList;

  const CustomNumberField({
    super.key,
    required this.controller,
    this.labelText,
    this.hintText,
    this.onChanged,
    required this.enabled,
    this.style,
    this.decoration,
    this.formattersList,
  });

  @override
  State<CustomNumberField> createState() => _CustomNumberFieldState();
}

class _CustomNumberFieldState extends State<CustomNumberField> with WidgetsBindingObserver {
  static const platform = MethodChannel('keyboard_done_button');
  FocusNode? _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    WidgetsBinding.instance.addObserver(this);
    if (Platform.isIOS) {
      _addDoneButton();
    }

    // Add listener to focus node
    _focusNode?.addListener(() {
      if (_focusNode?.hasFocus ?? false) {
        if (Platform.isIOS) {
          _addDoneButton();
        }
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _focusNode?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && Platform.isIOS) {
      _addDoneButton();
    }
  }

  Future<void> _addDoneButton() async {
    try {
      await platform.invokeMethod('addDoneButton');
    } on PlatformException catch (e) {
      debugPrint("Failed to add done button: '${e.message}'.");
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      enabled: widget.enabled,
      style: widget.style,
      keyboardType: const TextInputType.numberWithOptions(
        decimal: true,
        signed: false,
      ),
      decoration: widget.decoration ?? InputDecoration(
        labelText: widget.labelText,
        hintText: widget.hintText,
        border: const OutlineInputBorder(),
      ),
      textInputAction: TextInputAction.done,
      onChanged: widget.onChanged,
      onTapOutside: (_) => _focusNode?.unfocus(),
      //onSubmitted: (_) => _focusNode?.unfocus(),
      inputFormatters: widget.formattersList,
      validator: (value) {
        if(!(widget.enabled))return null; 
        // Access the parent InputBox's notifier to check validation state
        // This will ensure form validation works for InputBox fields
        if (widget.onChanged != null && (value == null || value.isEmpty)) {
          // Call onChanged with empty string to trigger the validation logic there
          widget.onChanged!('');
          return 'Field cannot be empty';
        }
        return null;
      },
    );
  }
}
