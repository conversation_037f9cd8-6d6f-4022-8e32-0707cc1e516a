import 'package:phoenix/features/trades/model/trades_model.dart';
import 'package:phoenix/features/trades/model/trades_analytics_model.dart';

class TradesAnalyticsCalculator {
  static TradesAnalyticsModel calculateAnalytics(
    List<TradesModel> trades,
    Map<String, String> brokerNameToLabelMap,
  ) {
    if (trades.isEmpty) {
      return TradesAnalyticsModel.empty();
    }

    int totalTrades = trades.length;
    int buyTrades = 0;
    int sellTrades = 0;
    int totalVolume = 0;
    double totalValue = 0.0;
    Map<String, BrokerAnalytics> brokerAnalytics = {};

    // Calculate overall statistics
    for (final trade in trades) {
      // Count buy/sell trades
      if (trade.side.toLowerCase() == 'buy') {
        buyTrades++;
      } else if (trade.side.toLowerCase() == 'sell') {
        sellTrades++;
      }

      // Sum volume and value
      totalVolume += trade.quantity;
      totalValue +=  (trade.side.toLowerCase() == 'buy' ? trade.price * trade.quantity : 0.0);

      // Calculate broker-wise analytics
      final brokerName = trade.broker;
      final displayName = brokerNameToLabelMap[brokerName] ?? brokerName;
      
      if (brokerAnalytics.containsKey(brokerName)) {
        final existing = brokerAnalytics[brokerName]!;
        brokerAnalytics[brokerName] = BrokerAnalytics(
          brokerName: displayName,
          tradeCount: existing.tradeCount + 1,
          totalInvested: existing.totalInvested + (trade.side.toLowerCase() == 'buy' ? trade.price * trade.quantity : 0.0),
          volume: existing.volume + trade.quantity,
        );
      } else {
        brokerAnalytics[brokerName] = BrokerAnalytics(
          brokerName: displayName,
          tradeCount: 1,
          totalInvested: trade.side.toLowerCase() == 'buy' ? trade.price * trade.quantity : 0.0,
          volume: trade.quantity,
        );
      }
    }

    // Calculate average trade size
    double avgTradeSize = totalTrades > 0 ? totalValue / totalTrades : 0.0;

    return TradesAnalyticsModel(
      totalTrades: totalTrades,
      buyTrades: buyTrades,
      sellTrades: sellTrades,
      totalVolume: totalVolume,
      totalValue: totalValue,
      avgTradeSize: avgTradeSize,
      brokerAnalytics: brokerAnalytics,
    );
  }
}
