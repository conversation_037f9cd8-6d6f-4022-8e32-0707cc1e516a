part of 'broker_data_map_bloc.dart';

@immutable
sealed class BrokerDataMapState {}

final class BrokerDataMapInitial extends BrokerDataMapState {}

final class BrokerDataMapProcessedState extends BrokerDataMapState {
  final Map<int, String> accountIdToNameMap;
  final Map<int, String> strategyIdToNameMap;
  final Map<String, String> brokerNameToLabelMap;

   BrokerDataMapProcessedState({
    this.accountIdToNameMap = const {},
    this.strategyIdToNameMap = const {},
    this.brokerNameToLabelMap = const {},
  });
}

final class BrokerDataMapErrorState extends BrokerDataMapState {
  final String error;

  BrokerDataMapErrorState(this.error);
}
