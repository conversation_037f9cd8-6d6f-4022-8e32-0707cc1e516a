import 'dart:convert';
import 'package:flutter/material.dart';
import 'strategy_flowchart_view.dart';
import 'json_viewer_page.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class FlowchartMessageBubble extends StatefulWidget {
  final String jsonText;
  final void Function()? onDeploy;
  final TextSpan Function(String) highlightJson;
  final String Function(String) prettyPrintJson;
  final VoidCallback? onViewJson;

  const FlowchartMessageBubble({
    super.key,
    required this.jsonText,
    required this.onDeploy,
    required this.highlightJson,
    required this.prettyPrintJson,
    this.onViewJson,
  });

  @override
  State<FlowchartMessageBubble> createState() => _FlowchartMessageBubbleState();
}

class _FlowchartMessageBubbleState extends State<FlowchartMessageBubble>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    // Start animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  StrategyData? _parseStrategyData() {
    try {
      final Map<String, dynamic> jsonData = jsonDecode(widget.jsonText);
      return StrategyData.fromJson(jsonData);
    } catch (e) {
      return null;
    }
  }

  void _viewJsonPage(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => JsonViewerPage(
          jsonText: widget.jsonText,
          highlightJson: widget.highlightJson,
          prettyPrintJson: widget.prettyPrintJson,
        ),
      ),
    );
  }

  void _viewFullFlowchart(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StrategyFlowchartView(
          jsonText: widget.jsonText,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final strategyData = _parseStrategyData();
    
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.9),
                      AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.7),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.2),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: themeState.isDarkMode 
                          ? Colors.black.withOpacity(0.4)
                          : Colors.grey.withOpacity(0.15),
                      blurRadius: 16,
                      offset: const Offset(0, 8),
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildModernHeader(themeState),
                    _buildContentArea(strategyData, themeState),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernHeader(ThemeState themeState) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
            AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.account_tree_rounded,
                  color: AppTheme.primaryColor(themeState.isDarkMode),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Strategy Flowchart',
                style: TextStyle(
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          if (widget.onViewJson != null)
            Container(
              decoration: BoxDecoration(
                color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.2),
                ),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.data_object_rounded,
                  size: 18,
                  color: AppTheme.primaryColor(themeState.isDarkMode),
                ),
                tooltip: 'View JSON',
                padding: const EdgeInsets.all(8),
                constraints: const BoxConstraints(),
                onPressed: widget.onViewJson,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContentArea(StrategyData? strategyData, ThemeState themeState) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.backgroundColor(themeState.isDarkMode).withOpacity(0.3),
            AppTheme.backgroundColor(themeState.isDarkMode).withOpacity(0.1),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      padding: const EdgeInsets.all(20),
      child: strategyData != null
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildModernMiniFlowchart(strategyData, themeState),
                const SizedBox(height: 20),
                _buildModernActionButtons(themeState),
              ],
            )
          : _buildModernErrorView(themeState),
    );
  }

  Widget _buildModernMiniFlowchart(StrategyData strategyData, ThemeState themeState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModernMiniCard(
          'Underlying Assets',
          Icons.trending_up_rounded,
          _buildModernUnderlyingContent(strategyData.underlying, themeState),
          Colors.blue,
          themeState,
        ),
        const SizedBox(height: 16),
        _buildModernFlowArrow(themeState),
        const SizedBox(height: 16),
        _buildModernMiniCard(
          'Entry Condition',
          Icons.login_rounded,
          _buildModernConditionContent(strategyData.entryCondition, themeState),
          Colors.green,
          themeState,
        ),
        const SizedBox(height: 16),
        _buildModernFlowArrow(themeState),
        const SizedBox(height: 16),
        _buildModernMiniCard(
          'Exit Condition',
          Icons.logout_rounded,
          _buildModernConditionContent(strategyData.exitCondition, themeState),
          Colors.red,
          themeState,
        ),
      ],
    );
  }

  Widget _buildModernActionButtons(ThemeState themeState) {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.8),
                  AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.6),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppTheme.borderColor(themeState.isDarkMode),
              ),
            ),
            child: ElevatedButton.icon(
              icon: const Icon(Icons.fullscreen_rounded, size: 20),
              label: const Text('View Full Chart'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: AppTheme.textPrimary(themeState.isDarkMode),
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(vertical: 14),
              ),
              onPressed: () => _viewFullFlowchart(context),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.2),
                  AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.3),
              ),
            ),
            child: ElevatedButton.icon(
              icon: const Icon(Icons.rocket_launch_rounded, size: 20),
              label: const Text('Deploy'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: AppTheme.primaryColor(themeState.isDarkMode),
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(vertical: 14),
              ),
              onPressed: widget.onDeploy,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernErrorView(ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.error_outline_rounded,
              size: 32,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Invalid Strategy Format',
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Unable to parse the strategy JSON data',
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildModernMiniCard(String title, IconData icon, Widget content, Color iconColor, ThemeState themeState) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.8),
            AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.6),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: iconColor.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode
                ? Colors.black.withOpacity(0.2)
                : Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: iconColor, size: 18),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            content,
          ],
        ),
      ),
    );
  }

  Widget _buildModernFlowArrow(ThemeState themeState) {
    return Center(
      child: Column(
        children: [
          Container(
            width: 2,
            height: 20,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.3),
                  AppTheme.primaryColor(themeState.isDarkMode),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(1),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.3),
              ),
            ),
            child: Icon(
              Icons.keyboard_arrow_down_rounded,
              color: AppTheme.primaryColor(themeState.isDarkMode),
              size: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernUnderlyingContent(List<String> underlying, ThemeState themeState) {
    if (underlying.isNotEmpty) {
      return Wrap(
        spacing: 8,
        runSpacing: 8,
        children: underlying.take(3).map((asset) => Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.2),
                AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.show_chart_rounded,
                size: 14,
                color: AppTheme.primaryColor(themeState.isDarkMode),
              ),
              const SizedBox(width: 6),
              Text(
                asset.toString(),
                style: TextStyle(
                  color: AppTheme.primaryColor(themeState.isDarkMode),
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        )).toList(),
      );
    }
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor(themeState.isDarkMode).withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.borderColor(themeState.isDarkMode),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline_rounded,
            color: AppTheme.textSecondary(themeState.isDarkMode),
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'No assets specified',
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 13,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernConditionContent(ConditionData? condition, ThemeState themeState) {
    if (condition != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Condition Logic Section
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.backgroundColor(themeState.isDarkMode).withOpacity(0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.borderColor(themeState.isDarkMode),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (condition.type != null)
                  _buildInfoChip('Type', condition.type!.toUpperCase(), themeState),
                if (condition.type != null && condition.operator != null)
                  const SizedBox(height: 8),
                if (condition.operator != null)
                  _buildInfoChip('Operator', condition.operator!, themeState),
                if (condition.operands != null && condition.operands!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  _buildInfoChip('Conditions', '${condition.operands!.length} operand(s) defined', themeState),
                ],
              ],
            ),
          ),
          // Action Section
          if (condition.action != null) ...[
            const SizedBox(height: 12),
            _buildActionSection(condition.action!, themeState),
          ],
        ],
      );
    }
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor(themeState.isDarkMode).withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.borderColor(themeState.isDarkMode),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline_rounded,
            color: AppTheme.textSecondary(themeState.isDarkMode),
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'No condition specified',
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 13,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionSection(ActionData action, ThemeState themeState) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.play_arrow_rounded,
            color: Colors.orange,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (action.transactionType != null)
                  Text(
                    '${action.transactionType!.toUpperCase()}${action.quantity != null ? ' ${action.quantity}' : ''}',
                    style: TextStyle(
                      color: Colors.orange,
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                if (action.orderType != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    'Order: ${action.orderType!.toUpperCase()}',
                    style: TextStyle(
                      color: Colors.orange.withOpacity(0.8),
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
                if (action.tradingSymbol != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    'Symbol: ${action.tradingSymbol!.toUpperCase()}',
                    style: TextStyle(
                      color: Colors.orange.withOpacity(0.8),
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String label, String value, ThemeState themeState) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            label,
            style: TextStyle(
              color: AppTheme.primaryColor(themeState.isDarkMode),
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}