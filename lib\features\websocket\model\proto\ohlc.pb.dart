//
//  Generated code. Do not modify.
//  source: ohlc.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'google/protobuf/wrappers.pb.dart' as $0;

class OHLC extends $pb.GeneratedMessage {
  factory OHLC({
    $0.DoubleValue? open,
    $0.DoubleValue? high,
    $0.DoubleValue? low,
    $0.DoubleValue? close,
  }) {
    final $result = create();
    if (open != null) {
      $result.open = open;
    }
    if (high != null) {
      $result.high = high;
    }
    if (low != null) {
      $result.low = low;
    }
    if (close != null) {
      $result.close = close;
    }
    return $result;
  }
  OHLC._() : super();
  factory OHLC.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory OHLC.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'OHLC', package: const $pb.PackageName(_omitMessageNames ? '' : 'websocket'), createEmptyInstance: create)
    ..aOM<$0.DoubleValue>(1, _omitFieldNames ? '' : 'open', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(2, _omitFieldNames ? '' : 'high', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(3, _omitFieldNames ? '' : 'low', subBuilder: $0.DoubleValue.create)
    ..aOM<$0.DoubleValue>(4, _omitFieldNames ? '' : 'close', subBuilder: $0.DoubleValue.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  OHLC clone() => OHLC()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  OHLC copyWith(void Function(OHLC) updates) => super.copyWith((message) => updates(message as OHLC)) as OHLC;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static OHLC create() => OHLC._();
  OHLC createEmptyInstance() => create();
  static $pb.PbList<OHLC> createRepeated() => $pb.PbList<OHLC>();
  @$core.pragma('dart2js:noInline')
  static OHLC getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<OHLC>(create);
  static OHLC? _defaultInstance;

  @$pb.TagNumber(1)
  $0.DoubleValue get open => $_getN(0);
  @$pb.TagNumber(1)
  set open($0.DoubleValue v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasOpen() => $_has(0);
  @$pb.TagNumber(1)
  void clearOpen() => clearField(1);
  @$pb.TagNumber(1)
  $0.DoubleValue ensureOpen() => $_ensure(0);

  @$pb.TagNumber(2)
  $0.DoubleValue get high => $_getN(1);
  @$pb.TagNumber(2)
  set high($0.DoubleValue v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasHigh() => $_has(1);
  @$pb.TagNumber(2)
  void clearHigh() => clearField(2);
  @$pb.TagNumber(2)
  $0.DoubleValue ensureHigh() => $_ensure(1);

  @$pb.TagNumber(3)
  $0.DoubleValue get low => $_getN(2);
  @$pb.TagNumber(3)
  set low($0.DoubleValue v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasLow() => $_has(2);
  @$pb.TagNumber(3)
  void clearLow() => clearField(3);
  @$pb.TagNumber(3)
  $0.DoubleValue ensureLow() => $_ensure(2);

  @$pb.TagNumber(4)
  $0.DoubleValue get close => $_getN(3);
  @$pb.TagNumber(4)
  set close($0.DoubleValue v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasClose() => $_has(3);
  @$pb.TagNumber(4)
  void clearClose() => clearField(4);
  @$pb.TagNumber(4)
  $0.DoubleValue ensureClose() => $_ensure(3);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
