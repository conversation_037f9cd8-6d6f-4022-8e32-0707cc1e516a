import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_event.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/theme/data/theme_service.dart';
import 'package:phoenix/utils/app_theme.dart';

class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  ThemeBloc() : super(DarkThemeState(themeData: AppTheme.darkTheme, isDarkMode: true)) {
    on<LoadTheme>(_onLoadTheme);
    on<ToggleTheme>(_onToggleTheme);
    on<SetDarkTheme>(_onSetDarkTheme);
    on<SetLightTheme>(_onSetLightTheme);
  }

  void _onLoadTheme(LoadTheme event, Emitter<ThemeState> emit) {
    final isDarkMode = ThemeService.getThemeMode();
    
    if (isDarkMode) {
      emit(DarkThemeState(themeData: AppTheme.darkTheme, isDarkMode: true));
    } else {
      emit(LightThemeState(themeData: AppTheme.lightTheme, isDarkMode: false));
    }
  }

  void _onToggleTheme(ToggleTheme event, Emitter<ThemeState> emit) async {
    final isDarkMode = !state.isDarkMode;
    await ThemeService.setThemeMode(isDarkMode);
    
    if (isDarkMode) {
      emit(DarkThemeState(themeData: AppTheme.darkTheme, isDarkMode: true));
    } else {
      emit(LightThemeState(themeData: AppTheme.lightTheme, isDarkMode: false));
    }
  }

  void _onSetDarkTheme(SetDarkTheme event, Emitter<ThemeState> emit) async {
    if (!state.isDarkMode) {
      await ThemeService.setThemeMode(true);
      emit(DarkThemeState(themeData: AppTheme.darkTheme, isDarkMode: true));
    }
  }

  void _onSetLightTheme(SetLightTheme event, Emitter<ThemeState> emit) async {
    if (state.isDarkMode) {
      await ThemeService.setThemeMode(false);
      emit(LightThemeState(themeData: AppTheme.lightTheme, isDarkMode: false));
    }
  }
}