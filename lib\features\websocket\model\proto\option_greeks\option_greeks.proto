syntax = "proto3";

package com.zentropy.phoenix.common.dataset.derivatives;

import "google/protobuf/wrappers.proto";
import "google/type/date.proto";

option java_package = "com.zentropy.phoenix.common.dataset.derivatives";
option java_multiple_files = true;

message ZenOptionGreeks {
  google.protobuf.Int64Value zen_id = 1;
  google.protobuf.Int64Value underlying_zen_id = 2;
  google.protobuf.DoubleValue implied_volatility = 3;
  google.protobuf.DoubleValue delta = 4;
  google.protobuf.DoubleValue delta2 = 5;
  google.protobuf.DoubleValue theta = 6;
  google.protobuf.DoubleValue gamma = 7;
  google.protobuf.DoubleValue vega = 8;
  google.protobuf.DoubleValue rho = 9;
  google.protobuf.DoubleValue color = 10;
  google.protobuf.DoubleValue charm = 11;
  google.protobuf.StringValue moneyness = 12;
  google.protobuf.StringValue trading_symbol = 13;
  google.protobuf.DoubleValue strike = 14;
  google.type.Date expiry = 15;
  google.protobuf.DoubleValue last_price = 16;
  google.protobuf.Int32Value oi = 17;
  google.protobuf.Int32Value oi_day_high = 18;
  google.protobuf.Int32Value oi_day_low = 19;
}

message ZenOptionGreeksList {
  repeated ZenOptionGreeks zen_option_greeks = 1;
}

message OptionGreeksEmptyRequest {
  // Empty request message for initial connection
}