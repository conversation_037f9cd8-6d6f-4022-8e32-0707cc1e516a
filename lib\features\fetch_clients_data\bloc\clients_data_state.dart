part of 'clients_data_bloc.dart';

@immutable
sealed class ClientsDataState {}

final class ClientsDataInitial extends ClientsDataState {}

final class ClientsDataLoading extends ClientsDataState {}

final class ClientsDataLoaded extends ClientsDataState {
  final List<ClientData> clientsList;

  ClientsDataLoaded({required this.clientsList});
}

final class ClientsDataError extends ClientsDataState {
  final String message;

  ClientsDataError({required this.message});
}
