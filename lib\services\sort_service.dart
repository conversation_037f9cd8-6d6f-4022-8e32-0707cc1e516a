import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../features/theme/bloc/theme_bloc.dart';
import '../features/theme/bloc/theme_state.dart';
import '../utils/theme_constants.dart';

enum SortOption {
  alphabetical,
  percentChange,
  invested,
  date,
  lastTradedPrice,
  unrealized,
  realized,
}

class SortService {
  static final SortService _instance = SortService._internal();

  factory SortService() {
    return _instance;
  }

  SortService._internal();

  static SortService get instance => _instance;

  void showSortOptions<T>({
    required BuildContext context,
    required SortOption? currentSortOption,
    required bool isAscending,
    required Function(SortOption?, bool) onSortChanged,
    List<SortOption>? availableOptions,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) => Container(
          decoration: BoxDecoration(
            color: ThemeConstants.getBackgroundColor(themeState.isDarkMode),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: _buildSortOptionsMenu(
            context: context,
            currentSortOption: currentSortOption,
            isAscending: isAscending,
            onSortChanged: onSortChanged,
            availableOptions: availableOptions,
          ),
        ),
      ),
    );
  }

  Widget _buildSortOptionsMenu({
    required BuildContext context,
    required SortOption? currentSortOption,
    required bool isAscending,
    required Function(SortOption?, bool) onSortChanged,
    List<SortOption>? availableOptions,
  }) {
    final options = availableOptions ?? SortOption.values;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (currentSortOption != null) ...[
                ListTile(
                  leading: Icon(Icons.clear_all,
                      color:
                          ThemeConstants.getTextColor(themeState.isDarkMode)),
                  title: Text('Clear Sort',
                      style: TextStyle(
                          color: ThemeConstants.getTextColor(
                              themeState.isDarkMode))),
                  onTap: () {
                    onSortChanged(null, true);
                    Navigator.pop(context);
                  },
                ),
                Divider(
                    color: ThemeConstants.getNetWorthAmountColor(
                        themeState.isDarkMode),
                    height: 1),
              ],
              ...options.map((option) => _buildSortOptionTile(
                    context: context,
                    option: option,
                    currentSortOption: currentSortOption,
                    isAscending: isAscending,
                    onSortChanged: onSortChanged,
                  )),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSortOptionTile({
    required BuildContext context,
    required SortOption option,
    required SortOption? currentSortOption,
    required bool isAscending,
    required Function(SortOption?, bool) onSortChanged,
  }) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        final sortConfig = _getSortOptionConfig(option, themeState.isDarkMode);
        
        return ListTile(
          leading: sortConfig.icon,
          title: Text(sortConfig.title,
              style: TextStyle(
                  color: ThemeConstants.getTextColor(themeState.isDarkMode))),
          onTap: () {
            final newIsAscending =
                currentSortOption == option ? !isAscending : true;
            onSortChanged(option, newIsAscending);
            Navigator.pop(context);
          },
          trailing: currentSortOption == option
              ? Icon(
                  isAscending ? Icons.arrow_upward : Icons.arrow_downward,
                  color: ThemeConstants.getTextColor(themeState.isDarkMode),
                )
              : null,
        );
      },
    );
  }

  _SortOptionConfig _getSortOptionConfig(SortOption option, bool isDarkMode) {
    final iconColor = ThemeConstants.getTextColor(isDarkMode);
    
    switch (option) {
      case SortOption.alphabetical:
        return _SortOptionConfig(
          title: 'A-Z Alphabetically',
          icon: Icon(Icons.sort_by_alpha, color: iconColor),
        );
      case SortOption.percentChange:
        return _SortOptionConfig(
          title: 'Change',
          icon: Text('%',
              style: TextStyle(color: iconColor, fontSize: 20)),
        );
      case SortOption.invested:
        return _SortOptionConfig(
          title: 'Invested',
          icon: Icon(Icons.currency_rupee, color: iconColor),
        );
      case SortOption.date:
        return _SortOptionConfig(
          title: 'Date',
          icon: Icon(Icons.date_range, color: iconColor),
        );
      case SortOption.lastTradedPrice:
        return _SortOptionConfig(
          title: 'Last Traded Price',
          icon: Text('LTP',
              style: TextStyle(color: iconColor, fontSize: 16)),
        );
      case SortOption.unrealized:
        return _SortOptionConfig(
          title: 'Unrealized Price',
          icon: Icon(Icons.punch_clock_outlined, color: iconColor),
        );
      case SortOption.realized:
        return _SortOptionConfig(
          title: 'Realized Price',
          icon: Icon(Icons.trending_up, color: iconColor),
        );
    }
  }

  List<T> sortList<T>({
    required List<T> items,
    required SortOption? sortOption,
    required bool isAscending,
    required Map<SortOption, Comparable Function(T)> sortFunctions,
  }) {
    if (sortOption == null || !sortFunctions.containsKey(sortOption)) {
      return items;
    }

    return List<T>.from(items)
      ..sort((a, b) {
        final comparison = sortFunctions[sortOption]!(a)
            .compareTo(sortFunctions[sortOption]!(b));
        return isAscending ? comparison : -comparison;
      });
  }
}

class _SortOptionConfig {
  final String title;
  final Widget icon;

  _SortOptionConfig({
    required this.title,
    required this.icon,
  });
}
