import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/websocket_streaming/bloc/tick_bloc.dart';
import 'package:phoenix/widgets/circular_loader.dart';

class TickStreamScreen extends StatefulWidget {
  const TickStreamScreen({super.key});

  @override
  _TickStreamScreenState createState() => _TickStreamScreenState();
}

class _TickStreamScreenState extends State<TickStreamScreen> with WidgetsBindingObserver {
  late TickBloc _tickBloc;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Save the reference to the TickBloc here
    _tickBloc = BlocProvider.of<TickBloc>(context);
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this); // Observe app lifecycle
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this); // Remove observer
    _tickBloc.add(DisconnectWebSocketEvent()); // Use the saved reference
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      // Reconnect when app comes to foreground
      _tickBloc.add(
        ConnectWebSocketEvent('ws://192.168.10.5:8765', {'Authorization': 'Bearer TOKEN'}),
      );
    } else if (state == AppLifecycleState.paused) {
      // Disconnect when app goes to background
      _tickBloc.add(DisconnectWebSocketEvent());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff24272C),
      body: BlocBuilder<TickBloc, TickState>(
        builder: (context, state) {
          if (state is TickInitial) {
            return Center(
              child: ElevatedButton(
                onPressed: () {
                  _tickBloc.add(
                    ConnectWebSocketEvent('ws://192.168.10.5:8765',
                        {'Authorization': 'Bearer TOKEN'}),
                  );
                },
                child: const Text('Connect'),
              ),
            );
          } else if (state is TickLoading) {
            return const Center(
              child: CircularLoader(),
            );
          } else if (state is TickLoaded) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Index: ${state.tickData.index}', style: TextStyle(color: Colors.white)),
                  Text('Instrument: ${state.tickData.instrumentToken}', style: TextStyle(color: Colors.white)),
                  Text('Price: ${state.tickData.lastPrice}', style: TextStyle(color: Colors.white)),
                  Text('Quantity: ${state.tickData.lastTradedQuantity}', style: TextStyle(color: Colors.white)),
                ],
              ),
            );
          } else if (state is TickError) {
            return Center(
              child: Text(
                'Error: ${state.error}',
                style: const TextStyle(color: Colors.red),
              ),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }
}
