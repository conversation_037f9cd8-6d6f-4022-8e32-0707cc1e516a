class SpiderOrderData {
  final int zenId;
  final String tradingSymbol;
  final String transactionType;

  SpiderOrderData({
    required this.zenId,
    required this.tradingSymbol,
    required this.transactionType,
  });

  // Convert object to JSON
  Map<String, dynamic> toJson() {
    return {
      'zenId': zenId,
      'tradingSymbol': tradingSymbol,
    };
  }

  // Create object from JSON
  factory SpiderOrderData.fromJson(Map<String, dynamic> json) {
    return SpiderOrderData(
      zenId: json['zenId'],
      tradingSymbol: json['tradingSymbol'],
      transactionType: json['transactionType']
    );
  }

  @override
  String toString() {
    return 'SpiderDataOrder(zenId: $zenId, tradingSymbol: $tradingSymbol, transactionType: $transactionType)';
  }
}
