import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class DisabledDropdown extends StatelessWidget {
  final String value;

  const DisabledDropdown({
    super.key,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          alignment: Alignment.center,
          height: 40,
          width: MediaQuery.of(context).size.width * 0.445,
          decoration: BoxDecoration(
            color: AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.5),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: AppTheme.borderColor(themeState.isDarkMode).withOpacity(0.5),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              value,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 15,
                color: AppTheme.textPrimary(themeState.isDarkMode).withOpacity(0.6),
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        );
      },
    );
  }
}
