import 'package:phoenix/features/common/position_comp_key.dart';

import '../../../models/base_model.dart';

class PositionsModel implements BaseModel {
  final PositionCompKey positionCompositeKey;
  final String tradingSymbol;
  final int position;
  final double openCost;
  final double averageCostPerShare;
  final double latestPrice;
  final double sodPrice;
  final double unrealizedPnl;
  final double unrealizedPnlPercentageChange;
  final double realizedPnl;
  final double curMarketValue;
  final DateTime date;
  final bool isOpen;

  PositionsModel({
    required this.positionCompositeKey,
    required this.tradingSymbol,
    required this.position,
    required this.openCost,
    required this.averageCostPerShare,
    required this.latestPrice,
    required this.sodPrice,
    required this.unrealizedPnl,
    required this.unrealizedPnlPercentageChange,
    required this.realizedPnl,
    required this.curMarketValue,
    required this.date,
    required this.isOpen,
  });

  factory PositionsModel.fromJson(Map<String, dynamic> json, bool isOpen) {
    return PositionsModel(
      positionCompositeKey: PositionCompKey.fromJson(json['position_composite_key']),
      tradingSymbol: json['trading_symbol'],
      position: json['position'],
      openCost: (json['open_cost'] as num).toDouble(),
      averageCostPerShare: (json['average_cost_per_share'] as num).toDouble(),
      latestPrice: (json['latest_price'] as num).toDouble(),
      sodPrice: (json['sod_price'] as num).toDouble(),
      unrealizedPnl: (json['unrealized_pnl'] as num).toDouble(),
      unrealizedPnlPercentageChange: (((json['unrealized_pnl'] as num ).toDouble() / (json['open_cost'] as num).toDouble())*100),
      realizedPnl: (json['realized_pnl'] as num).toDouble(),
      curMarketValue: (json['cur_market_value'] as num).toDouble(),
      date: DateTime.parse(json['date']),
      isOpen: isOpen,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'position_composite_key': positionCompositeKey.toJson(),
      'trading_symbol': tradingSymbol,
      'position': position,
      'open_cost': openCost,
      'average_cost_per_share': averageCostPerShare,
      'latest_price': latestPrice,
      'sod_price': sodPrice,
      'unrealized_pnl': unrealizedPnl,
      'unrealizedPnlPercentageChange' : unrealizedPnlPercentageChange,
      'realized_pnl': realizedPnl,
      'cur_market_value': curMarketValue,
      'date': date.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'PositionsModel(positionCompositeKey: $positionCompositeKey, tradingSymbol: $tradingSymbol, position: $position, openCost: $openCost, averageCostPerShare: $averageCostPerShare, latestPrice: $latestPrice, sodPrice: $sodPrice, unrealizedPnl: $unrealizedPnl, unrealizedPnlPercentageChange : $unrealizedPnlPercentageChange, realizedPnl: $realizedPnl, curMarketValue: $curMarketValue, date: $date)';
  }

  @override
  List<String> getSearchableFields() {
    return [tradingSymbol];
  }
}
