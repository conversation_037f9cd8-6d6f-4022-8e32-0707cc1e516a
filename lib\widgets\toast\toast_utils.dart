import 'package:flutter/material.dart';
import 'package:phoenix/widgets/toast/custom_toast.dart';

class ToastUtil {
  static OverlayEntry? _currentToast;

  static void showToast(
    BuildContext context,
    String message,
    ToastType type, {
    Duration duration = const Duration(seconds: 3),
  }) {
    // Remove the current toast if it exists
    _currentToast?.remove();

    // Declare overlayEntry first
    late OverlayEntry overlayEntry; 

    overlayEntry = OverlayEntry(
      builder: (BuildContext context) => Positioned(
        bottom: 24.0,
        left: 16.0,
        right: 16.0,
        child: SafeArea(
          child: Material(
            color: Colors.transparent,
            child: AnimatedOpacity(
              opacity: 1.0,
              duration: const Duration(milliseconds: 300),
              child: CustomToast(
                message: message,
                type: type,
                onClose: () {
                  overlayEntry.remove(); 
                  _currentToast = null;
                },
              ),
            ),
          ),
        ),
      ),
    );

    _currentToast = overlayEntry;

    try {
      Overlay.of(context).insert(overlayEntry);

      if (duration != Duration.zero) {
        Future.delayed(duration, () {
          if (_currentToast == overlayEntry) {
            overlayEntry.remove();
            _currentToast = null;
          }
        });
      }
    } catch (e) {
      debugPrint('Error showing toast: $e');
    }
  }
}
