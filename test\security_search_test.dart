import 'package:flutter_test/flutter_test.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/services/security_list_search_service.dart';

void main() {
  group('SecurityListSearchService Name Field Tests', () {
    // Sample test data
    final equityList = [
      SecurityModel(
        zenId: 1,
        tradingSymbol: 'TCS',
        name: 'Tata Consultancy Service',
        strike: 0.0,
        exchanges: ['NSE'],
        lotSize: 1,
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      ),
      SecurityModel(
        zenId: 2,
        tradingSymbol: 'RELIANCE',
        name: 'Reliance Industries Limited',
        strike: 0.0,
        exchanges: ['NSE'],
        lotSize: 1,
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      ),
      SecurityModel(
        zenId: 3,
        tradingSymbol: 'TATASTEEL',
        name: 'Tata Steel Limited',
        strike: 0.0,
        exchanges: ['NSE'],
        lotSize: 1,
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      ),
      SecurityModel(
        zenId: 4,
        tradingSymbol: 'JSWSTEEL',
        name: 'JSW Steel Limited',
        strike: 0.0,
        exchanges: ['NSE'],
        lotSize: 1,
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      ),
    ];

    final fnoList = [
      SecurityModel(
        zenId: 5,
        tradingSymbol: 'NIFTY25JULFUT',
        name: 'Nifty 50',
        strike: 0.0,
        exchanges: ['NSE'],
        lotSize: 50,
        instrumentType: 'FUT',
        expiryType: 'MONTHLY',
        expiry: '2025-07-31',
      ),
      SecurityModel(
        zenId: 6,
        tradingSymbol: 'BANKNIFTY25JULCE25000',
        name: 'Bank Nifty',
        strike: 25000.0,
        exchanges: ['NSE'],
        lotSize: 25,
        instrumentType: 'CE',
        expiryType: 'MONTHLY',
        expiry: '2025-07-31',
      ),
      SecurityModel(
        zenId: 7,
        tradingSymbol: 'MIDCPNIFTY25JULPE15000',
        name: 'Nifty Midcap 50',
        strike: 15000.0,
        exchanges: ['NSE'],
        lotSize: 75,
        instrumentType: 'PE',
        expiryType: 'MONTHLY',
        expiry: '2025-07-31',
      ),
    ];

    test('_matchesNameField should work with simple contains', () {
      // Test simple contains matching
      expect(
        SecurityListSearchService.testMatchesNameField('Tata Consultancy Service', 'tata'),
        true,
      );
      expect(
        SecurityListSearchService.testMatchesNameField('Tata Consultancy Service', 'consultancy'),
        true,
      );
      expect(
        SecurityListSearchService.testMatchesNameField('JSW Steel Limited', 'steel'),
        true,
      );
      expect(
        SecurityListSearchService.testMatchesNameField('Reliance Industries Limited', 'xyz'),
        false,
      );
    });

    test('_matchesNameField should work with multi-word queries', () {
      // Test multi-word matching
      expect(
        SecurityListSearchService.testMatchesNameField('Tata Consultancy Service', 'tata consultancy'),
        true,
      );
      expect(
        SecurityListSearchService.testMatchesNameField('JSW Steel Limited', 'jsw steel'),
        true,
      );
      expect(
        SecurityListSearchService.testMatchesNameField('Reliance Industries Limited', 'reliance industries'),
        true,
      );
      expect(
        SecurityListSearchService.testMatchesNameField('Tata Steel Limited', 'tata industries'),
        false,
      );
    });

    test('Name field search should find steel companies', () {
      // This test demonstrates how searching for "steel" would find both steel companies
      final steelMatches = equityList.where((security) =>
        SecurityListSearchService.testMatchesNameField(security.name, 'steel')
      ).toList();
      
      expect(steelMatches.length, 2);
      expect(steelMatches.any((s) => s.tradingSymbol == 'TATASTEEL'), true);
      expect(steelMatches.any((s) => s.tradingSymbol == 'JSWSTEEL'), true);
    });

    test('Name field search should work for F&O with exact word matching', () {
      // Test F&O name field matching - should only match exact words
      final niftyMatches = fnoList.where((security) =>
        SecurityListSearchService.testMatchesNameField(security.name, 'nifty', isEquity: false)
      ).toList();

      // Should only find "Nifty 50", not "Nifty Midcap 50" when searching for "nifty"
      expect(niftyMatches.length, 1);
      expect(niftyMatches.any((s) => s.tradingSymbol == 'NIFTY25JULFUT'), true);
      expect(niftyMatches.any((s) => s.tradingSymbol == 'MIDCPNIFTY25JULPE15000'), false);

      // Test searching for "midcap" should find the midcap nifty
      final midcapMatches = fnoList.where((security) =>
        SecurityListSearchService.testMatchesNameField(security.name, 'midcap', isEquity: false)
      ).toList();

      expect(midcapMatches.length, 1);
      expect(midcapMatches.any((s) => s.tradingSymbol == 'MIDCPNIFTY25JULPE15000'), true);
    });

    test('Equity vs F&O name matching should behave differently', () {
      const testName = 'Nifty Midcap 50';

      // For equity (contains matching) - "nifty" should match "Nifty Midcap 50"
      expect(
        SecurityListSearchService.testMatchesNameField(testName, 'nifty', isEquity: true),
        true,
      );

      // For F&O (exact word matching) - "nifty" should match "Nifty Midcap 50"
      // because "Nifty" is an exact word match
      expect(
        SecurityListSearchService.testMatchesNameField(testName, 'nifty', isEquity: false),
        true,
      );

      // But searching for "nift" (partial) should work for equity but not F&O
      expect(
        SecurityListSearchService.testMatchesNameField(testName, 'nift', isEquity: true),
        true,
      );
      expect(
        SecurityListSearchService.testMatchesNameField(testName, 'nift', isEquity: false),
        false,
      );
    });
  });
}
