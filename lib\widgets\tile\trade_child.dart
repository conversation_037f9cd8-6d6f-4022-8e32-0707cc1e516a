import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/trades/model/trades_model.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/broker_avatar/broker_avatar.dart';

class TradeChild extends StatefulWidget {
  const TradeChild({
    super.key,
    required this.data,
    required this.brokerAccountStrategyData,
  });

  final TradesModel data;
  final BrokerAccountStrategyData? brokerAccountStrategyData;

  @override
  State<TradeChild> createState() => _TradeChildState();
}

class _TradeChildState extends State<TradeChild> {
  @override
  Widget build(BuildContext context) {
    final isBuyTrade = widget.data.side.toLowerCase() == 'buy';

    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: AppTheme.surfaceColor(themeState.isDarkMode),
              boxShadow:
                  ThemeConstants.getNeomorpicShadow(themeState.isDarkMode)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Left Column
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Flexible(
                          child: Text(
                            widget.data.symbol,
                            style: TextStyle(
                              color:
                                  AppTheme.textPrimary(themeState.isDarkMode),
                              fontSize: 15,
                              fontWeight: FontWeight.w400,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 4),
                        if (widget.data.broker.isNotEmpty)
                          BrokerAvatar(
                            brokerName:
                                widget.brokerAccountStrategyData?.brokerName ??
                                    "N/A",
                          ),
                      ],
                    ),

                    // Strategy
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const ImageIcon(
                          AssetImage("images/tile-generic/strategy.png"),
                          color: Colors.blue,
                          size: 15,
                        ),
                        const SizedBox(width: 4),
                        Flexible(
                          child: Text(
                            widget.brokerAccountStrategyData?.strategyName ??
                                "N/A",
                            style: TextStyle(
                              color:
                                  AppTheme.textSecondary(themeState.isDarkMode),
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 19),

                    // Quantity and Price
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const ImageIcon(
                          AssetImage("images/tile-generic/qty_icon.png"),
                          color: ThemeConstants.blue,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          "${widget.data.quantity}",
                          style: TextStyle(
                            color:
                                AppTheme.textSecondary(themeState.isDarkMode),
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Right Column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Buy/Sell indicator with trade value

                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: isBuyTrade
                            ? ThemeConstants.tileGreenColor.withOpacity(0.2)
                            : ThemeConstants.titleRedColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        widget.data.side.toUpperCase(),
                        style: TextStyle(
                          color: isBuyTrade
                              ? ThemeConstants.tileGreenColor
                              : ThemeConstants.titleRedColor,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    

                    // // Trade Value
                    // TradeValueFormatter(value: tradeValue, isBuy: isBuyTrade),
                    const SizedBox(height: 20),
                    Text(
                      UtilFunctions.formatIndianCurrency(widget.data.price),
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),

                    // Timestamp
                    Text(
                      _formatDateTimestamp(widget.data.timestamp),
                      style: TextStyle(
                        color: AppTheme.textSecondary(themeState.isDarkMode),
                        fontSize: 11,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatDateTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tradeDate = DateTime(timestamp.year, timestamp.month, timestamp.day);

    if (tradeDate == today) {
      return "Today ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}";
    } else {
      return "${timestamp.day.toString().padLeft(2, '0')}/${timestamp.month.toString().padLeft(2, '0')}/${timestamp.year.toString()}";
    }
  }
}

class TradeValueFormatter extends StatelessWidget {
  const TradeValueFormatter({
    super.key,
    required this.value,
    required this.isBuy,
  });

  final double value;
  final bool isBuy;

  @override
  Widget build(BuildContext context) {
    Color textColor;
    String displayText;

    if (value.isInfinite || value.isNaN) {
      displayText = "--";
      textColor = ThemeConstants.zenGrey;
    } else if (value == 0) {
      displayText = "--";
      textColor = ThemeConstants.zenGrey;
    } else {
      displayText = UtilFunctions.formatIndianCurrency(value);
      textColor =
          isBuy ? ThemeConstants.titleRedColor : ThemeConstants.tileGreenColor;
    }

    return Text(
      displayText,
      style: TextStyle(
        color: textColor,
        fontSize: 15,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}
