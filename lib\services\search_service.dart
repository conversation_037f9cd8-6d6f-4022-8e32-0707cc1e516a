import 'package:phoenix/models/base_model.dart';

class SearchService {
  static final SearchService _instance = SearchService._internal();
  
  factory SearchService() {
    return _instance;
  }
  
  SearchService._internal();

  static SearchService get instance => _instance;

  /// Generic search function that can be used with any model that extends BaseModel
  bool matchesSearchQuery<T extends BaseModel>(T item, String query) {
    final searchTerm = query.toLowerCase();

    // Check if it's an advanced search
    if (searchTerm.contains(":")) {
      return _handleAdvancedSearch(item, searchTerm);
    }

    // Basic search through all searchable fields
    return _handleBasicSearch(item, searchTerm);
  }

  bool _handleAdvancedSearch<T extends BaseModel>(T item, String searchTerm) {
    // Extract search pattern and query
    final pattern = searchTerm.split(":")[0].trim();
    final query = searchTerm.split(":")[1].trim();

    switch (pattern) {
      case "qty":
        return _searchQuantity(item, query);
      case "price":
        return _searchPrice(item, query);
      case "type":
        return _searchType(item, query);
      case "status":
        return _searchStatus(item, query);
      case "product":
        return _searchProduct(item, query);
      case "date":
        return _searchDate(item, query);
      case "id":
        return _searchId(item, query);
      default:
        return false;
    }
  }

  bool _handleBasicSearch<T extends BaseModel>(T item, String searchTerm) {
    // Get all searchable fields from the model
    final searchableFields = item.getSearchableFields();
    
    // Search through all fields
    return searchableFields.any((field) {
      final value = field.toString().toLowerCase();
      return value.contains(searchTerm);
    });
  }

  bool _searchQuantity<T extends BaseModel>(T item, String query) {
    if (item is HasQuantity) {
      return (item as HasQuantity).quantity.toString().contains(query);
    }
    return false;
  }

  bool _searchPrice<T extends BaseModel>(T item, String query) {
    if (item is HasPrice) {
      return (item as HasPrice).price.toString().contains(query) ||
             ((item as HasPrice).triggerPrice?.toString().contains(query) ?? false) ||
             ((item as HasPrice).averagePrice?.toString().contains(query) ?? false);
    }
    return false;
  }

  bool _searchType<T extends BaseModel>(T item, String query) {
    if (item is HasType) {
      return (item as HasType).type.toLowerCase().contains(query);
    }
    return false;
  }

  bool _searchStatus<T extends BaseModel>(T item, String query) {
    if (item is HasStatus) {
      return (item as HasStatus).status.toLowerCase().contains(query);
    }
    return false;
  }

  bool _searchProduct<T extends BaseModel>(T item, String query) {
    if (item is HasProduct) {
      return (item as HasProduct).product.toLowerCase().contains(query);
    }
    return false;
  }

  bool _searchDate<T extends BaseModel>(T item, String query) {
    if (item is HasTimestamp) {
      final dateStr = (item as HasTimestamp).timestamp.toString().split(" ")[0];
      return dateStr.contains(query);
    }
    return false;
  }

  bool _searchId<T extends BaseModel>(T item, String query) {
    if (item is HasId) {
      return (item as HasId).id.toString().toLowerCase().contains(query);
    }
    return false;
  }
}

// Interfaces for type checking
abstract class HasQuantity {
  num get quantity;
}

abstract class HasPrice {
  num get price;
  num? get triggerPrice;
  num? get averagePrice;
}

abstract class HasType {
  String get type;
}

abstract class HasStatus {
  String get status;
}

abstract class HasProduct {
  String get product;
}

abstract class HasTimestamp {
  DateTime get timestamp;
}

abstract class HasId {
  String get id;
}






