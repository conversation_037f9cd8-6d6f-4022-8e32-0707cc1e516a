class PortfolioModel {
  final String name;
  final String exchange;
  final int zenId;
  final int quantity;
  final double totalPnl;
  final double pnlChangePercentage;
  final double averagePrice;
  final double ltp;
  final double ltpChangePercentage;
  final double investedAmount;

  PortfolioModel({
    required this.name,
    required this.exchange,
    required this.zenId,
    required this.quantity,
    required this.totalPnl,
    required this.pnlChangePercentage,
    required this.averagePrice,
    required this.ltp,
    required this.ltpChangePercentage,
    required this.investedAmount
  });

  

  /// Factory method to create an instance from JSON
  factory PortfolioModel.fromJson(Map<String, dynamic> json) {
   
    return PortfolioModel(
      name: json['tradingSymbol'] as String,
      exchange: json['positionCompositeKey']['broker'] as String,
      quantity: json['position'] as int,
      zenId: json['positionCompositeKey']["zenSecId"] as int,
      totalPnl: (json['unrealizedPnl'] as num).toDouble(),
      pnlChangePercentage: (((json['unrealizedPnl'] as num ).toDouble() / (json['openCost'] as num).toDouble())*100) ,
      averagePrice: (json['averageCostPerShare'] as num).toDouble(),
      ltp: (json['latestPrice'] as num).toDouble(),
      investedAmount: (json['openCost'] as num).toDouble(),
      ltpChangePercentage: 0,
    );
  }

  /// Method to convert an instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'exchange': exchange,
      'zenId':zenId,
      'quantity': quantity,
      'totalPnl': totalPnl,
      'pnlChangePercentage': pnlChangePercentage,
      'averagePrice': averagePrice,
      'ltp': ltp,
      'ltpChangePercentage': ltpChangePercentage,
      'investedAmount': investedAmount,
    };
  }

  //Copy With
  PortfolioModel copyWith({
  String? name,
  String? exchange,
  int? zenId,
  int? quantity,
  double? totalPnl,
  double? pnlChangePercentage,
  double? averagePrice,
  double? ltp,
  double? ltpChangePercentage,
  double? investedAmount
}) {
  return PortfolioModel(
    name: name ?? this.name,
    exchange: exchange ?? this.exchange,
    zenId: zenId ?? this.zenId,
    quantity: quantity ?? this.quantity,
    totalPnl: totalPnl ?? this.totalPnl,
    pnlChangePercentage: pnlChangePercentage ?? this.pnlChangePercentage,
    averagePrice: averagePrice ?? this.averagePrice,
    ltp: ltp ?? this.ltp,
    ltpChangePercentage: ltpChangePercentage ?? this.ltpChangePercentage,
    investedAmount: investedAmount ?? this.investedAmount
  );
}


  @override
  String toString() {
    return 'PortfolioModel(name: $name, exchange: $exchange, zenId: $zenId, quantity: $quantity, totalPnl: $totalPnl, pnlChangePercentage: $pnlChangePercentage, averagePrice: $averagePrice, ltp: $ltp, ltpChangePercentage: $ltpChangePercentage, investedAmount: $investedAmount)';
  }

  
}
