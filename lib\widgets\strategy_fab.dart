import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/screens/strategy_chat_screen.dart';
import 'package:phoenix/utils/theme_constants.dart';

class StrategyFAB extends StatelessWidget {
  final bool isNetWorthBarExpanded;

  const StrategyFAB({super.key, required this.isNetWorthBarExpanded});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        int? userId;
        if (state is AuthAuthenticated) {
          userId = int.tryParse(state.credentialsModel.zenUserId);
        }
        return FloatingActionButton(
          heroTag: 'strategy_ai_fab',
          shape: RoundedRectangleBorder(side: BorderSide(width: 1,color: ThemeConstants.blue),borderRadius: BorderRadius.circular(15)),
          backgroundColor: isNetWorthBarExpanded ? ThemeConstants.blue.withOpacity(0.2) : ThemeConstants.blue,
          onPressed: () {
            if (userId == null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('User ID not available. Please login again.')),
              );
              return;
            }
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => StrategyChatScreen(userId: userId!),
              ),
            );
          },
          tooltip: 'Strategy AI',
          child: Icon(Icons.auto_awesome, color: Colors.white),
        );
      },
    );
  }
}
