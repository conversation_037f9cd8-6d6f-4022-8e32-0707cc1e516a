import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/broker_account_strategy_selection/bloc/broker_account_strategy_selection_bloc.dart';
import 'package:phoenix/widgets/broker_account_strategy_selector/broker_account_strategy_modal.dart';

/// Helper to show broker/account selection modal and return selected values.
Future<Map<String, dynamic>?> showBrokerAccountStrategySelector(BuildContext context) async {
  final authState = context.read<AuthBloc>().state;
  if (authState is! AuthAuthenticated) return null;
  final credentials = authState.credentialsModel;
  final result = await showDialog<Map<String, dynamic>>(
    context: context,
    builder: (context) {
      return BlocProvider(
        create: (_) => BrokerAccountStrategySelectionBloc(credentials),
        child: BrokerAccountStrategyModal(onDefaultSet: (_) {}),
      );
    },
  );
  return result;
}
