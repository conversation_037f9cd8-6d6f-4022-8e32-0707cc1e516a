part of 'broker_account_strategy_selection_bloc.dart';

@immutable
sealed class BrokerAccountStrategySelectionEvent {}

class BrokerSelected extends BrokerAccountStrategySelectionEvent {
  final BrokerInfo broker;
  BrokerSelected(this.broker);
}

class AccountSelected extends BrokerAccountStrategySelectionEvent {
  final AccountInfo account;
  AccountSelected(this.account);
}

class StrategySelected extends BrokerAccountStrategySelectionEvent {
  final Strategy strategy;
  StrategySelected(this.strategy);
}
