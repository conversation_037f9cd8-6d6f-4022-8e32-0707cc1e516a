import 'package:web_socket_channel/io.dart';
//We are going to make the connection here and return the stream
class WebSocketDataProvider {
  IOWebSocketChannel? _channel;

  Stream<dynamic> connect(String url, {Map<String,String>? headers}){
    print("1. data provider connect");
    _channel = IOWebSocketChannel.connect(url,headers: headers);
    return _channel!.stream;
  }

  void sendPing() => _channel?.sink.add('ping');

  void disconnect() => _channel?.sink.close();
}