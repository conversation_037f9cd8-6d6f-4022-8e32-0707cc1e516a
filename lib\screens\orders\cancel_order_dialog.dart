import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class CancelOrderDialog extends StatelessWidget {
  final VoidCallback onConfirm;

  const CancelOrderDialog({super.key, required this.onConfirm});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return AlertDialog(
          backgroundColor: AppTheme.surfaceColor(themeState.isDarkMode),
          title: Text("Delete Order", 
            style: TextStyle(color: AppTheme.textPrimary(themeState.isDarkMode))),
          content: Text("Are you sure you want to cancel this order? This action cannot be undone.",
            style: TextStyle(color: AppTheme.textSecondary(themeState.isDarkMode))),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text("No", style: TextStyle(color: AppTheme.textSecondary(themeState.isDarkMode))),
            ),
            TextButton(
              onPressed: () {
                onConfirm();
                Navigator.of(context).pop();
              },
              child: const Text("Yes", style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
