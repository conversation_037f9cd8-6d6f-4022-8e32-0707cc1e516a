import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/margin_state/data/provider/margin_provider.dart';
import 'package:phoenix/features/margin_state/model/margin_data.dart';

part 'margin_state_event.dart';
part 'margin_state_state.dart';

class MarginBloc extends Bloc<MarginEvent, MarginState> {
  final MarginProvider _marginProvider;

  MarginBloc(this._marginProvider) : super(MarginInitial()) {
    on<MarginFetchEvent>(_onMarginFetchEvent);
    on<MarginFetchMultipleEvent>(_onMarginFetchMultipleEvent);
  }
  
  void _onMarginFetchEvent(
      MarginFetchEvent event, Emitter<MarginState> emit) async {
    debugPrint("reach __onMarginFetchEvent");
    emit(MarginLoading());
    try {
      final marginData = await _marginProvider.fetchMarginData(
          event.clientId, event.accountId);

      emit(MarginLoaded(margin: marginData.availableCash));
    } catch (e) {
      emit(MarginError(error: e.toString()));
    }
  }

  void _onMarginFetchMultipleEvent(
      MarginFetchMultipleEvent event, Emitter<MarginState> emit) async {
    debugPrint("reach _onMarginFetchMultipleEvent for ${event.accountIds.length} accounts");
    emit(MarginLoading());
    try {
      Map<int, MarginData> margins = {};
      
      // Fetch margin data for each account
      for (int accountId in event.accountIds) {
        try {
          final marginData = await _marginProvider.fetchMarginData(
              event.clientId, accountId);
          margins[accountId] = marginData;
        } catch (e) {
          debugPrint("Error fetching margin for account $accountId: $e");
          // Add default margin data for failed requests
          margins[accountId] = MarginData(
            clientId: event.clientId,
            brokerName: "",
            accountName: "",
            accountId: accountId,
            availableCash: 0.0,
          );
        }
      }

      emit(MarginMultipleLoaded(margins: margins));
    } catch (e) {
      emit(MarginError(error: e.toString()));
    }
  }
}
