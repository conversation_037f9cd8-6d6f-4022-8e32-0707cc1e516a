class ApiResponse<T> {
  final String status;
  final int code;
  final String message;
  final T payload;

  ApiResponse({
    required this.status,
    required this.code,
    required this.message,
    required this.payload,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    return ApiResponse(
      status: json['status'] as String,
      code: json['code'] as int,
      message: json['message'] as String,
      payload: fromJsonT(json['payload']),
    );
  }
}