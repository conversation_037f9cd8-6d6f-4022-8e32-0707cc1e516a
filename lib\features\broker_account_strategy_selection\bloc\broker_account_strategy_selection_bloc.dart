import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';

part 'broker_account_strategy_selection_event.dart';
part 'broker_account_strategy_selection_state.dart';

class BrokerAccountStrategySelectionBloc extends Bloc<BrokerAccountStrategySelectionEvent, BrokerAccountStrategyState> {
  BrokerAccountStrategySelectionBloc(CredentialsModel credentials)
      : super(_initializeState(credentials)) {
    on<BrokerSelected>(_onBrokerSelected);
    on<AccountSelected>(_onAccountSelected);
    on<StrategySelected>(_onStrategySelected);
  }

  // Initialize state with the first broker, account, and strategy as default selections
  //we are initailizing the first broker -> account -> strategy in the given list
  static BrokerAccountStrategyState _initializeState(CredentialsModel credentials) {
    final List<BrokerInfo> brokers = credentials.brokers;
    final BrokerInfo? firstBroker = brokers.isNotEmpty ? brokers.first : null;
    final List<AccountInfo> accounts = firstBroker?.accounts ?? [];
    final AccountInfo? firstAccount = accounts.isNotEmpty ? accounts.first : null;
    final List<Strategy> strategies = firstAccount?.strategies ?? [];
    final Strategy? firstStrategy = strategies.isNotEmpty ? strategies.first : null;

    return BrokerAccountStrategyState(
      brokers: brokers,
      selectedBroker: firstBroker,
      availableAccounts: accounts,
      selectedAccount: firstAccount,
      availableStrategies: strategies,
      selectedStrategy: firstStrategy,
    );
  }

  //When changing the broker the availabe accounts will be changed and the first account in the list will be set as seleced account
  // and same for strategy
  // brokerChange -> updates accounts -> sets 1st account -> updates strategy -> sets strategy
  void _onBrokerSelected(BrokerSelected event, Emitter<BrokerAccountStrategyState> emit) {
    print("Broker changed: ${event.broker.brokerName}");

    // Check if "All" broker is selected (broker with ID -1)
    if (event.broker.brokerId == -1) {
      // For "All" broker, we need to collect all accounts from all brokers
      final List<AccountInfo> allAccounts = [];
      
      // Get unique accounts (avoid duplicates)
      final Set<int> addedAccountIds = {};
      
      for (final broker in state.brokers) {
        for (final account in broker.accounts) {
          if (!addedAccountIds.contains(account.accountId)) {
            allAccounts.add(account);
            addedAccountIds.add(account.accountId);
          }
        }
      }
      
      // Select the first account by default
      final AccountInfo? firstAccount = allAccounts.isNotEmpty ? allAccounts.first : null;
      
      // Get strategies for the first account
      final List<Strategy> strategies = firstAccount?.strategies ?? [];
      final Strategy? firstStrategy = strategies.isNotEmpty ? strategies.first : null;
      
      emit(state.copyWith(
        selectedBroker: event.broker,
        availableAccounts: allAccounts,
        selectedAccount: firstAccount,
        availableStrategies: strategies,
        selectedStrategy: firstStrategy,
      ));
    } else {
      // Regular broker selection
      final List<AccountInfo> accounts = event.broker.accounts;
      final AccountInfo? firstAccount = accounts.isNotEmpty ? accounts.first : null;
      final List<Strategy> strategies = firstAccount?.strategies ?? [];
      final Strategy? firstStrategy = strategies.isNotEmpty ? strategies.first : null;

      emit(state.copyWith(
        selectedBroker: event.broker,
        availableAccounts: accounts,
        selectedAccount: firstAccount,
        availableStrategies: strategies,
        selectedStrategy: firstStrategy,
      ));
    }
  }

  //account changes -> sets 1st account -> updates strategy -> sets strategy
  void _onAccountSelected(AccountSelected event, Emitter<BrokerAccountStrategyState> emit) {
    print("Account changed: ${event.account.accountName}");

    // Check if "All" account is selected (account with ID -1)
    if (event.account.accountId == -1) {
      // For "All" account, we need to collect all strategies from all available accounts
      final List<Strategy> allStrategies = [];
      
      // Get unique strategies (avoid duplicates)
      final Set<int> addedStrategyIds = {};
      
      for (final account in state.availableAccounts) {
        if (account.accountId != -1) { // Skip the "All" account itself
          for (final strategy in account.strategies) {
            if (!addedStrategyIds.contains(strategy.strategyId)) {
              allStrategies.add(strategy);
              addedStrategyIds.add(strategy.strategyId);
            }
          }
        }
      }
      
      // Select the first strategy by default
      final Strategy? firstStrategy = allStrategies.isNotEmpty ? allStrategies.first : null;
      
      emit(state.copyWith(
        selectedAccount: event.account,
        availableStrategies: allStrategies,
        selectedStrategy: firstStrategy,
      ));
    } else {
      // Regular account selection
      final List<Strategy> strategies = event.account.strategies;
      final Strategy? firstStrategy = strategies.isNotEmpty ? strategies.first : null;

      emit(state.copyWith(
        selectedAccount: event.account,
        availableStrategies: strategies,
        selectedStrategy: firstStrategy,
      ));
    }
  }

  //Strategy chages sets strategy
  void _onStrategySelected(StrategySelected event, Emitter<BrokerAccountStrategyState> emit) {
    print("Strategy changed: ${event.strategy.strategyName}");
     
    emit(state.copyWith(
      selectedStrategy: event.strategy,
    ));
  }
}


