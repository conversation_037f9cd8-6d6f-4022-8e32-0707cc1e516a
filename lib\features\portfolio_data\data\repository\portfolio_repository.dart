import 'package:phoenix/features/portfolio_data/data/provider/portfolio_provider.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';
import 'package:phoenix/utils/app_exception.dart';


class PortfolioRepository {
  final PortfolioProvider provider;

  PortfolioRepository(this.provider);

  Future<Map<String, List<PositionsModel>>> getPortfolioData(int clientId) async {
    try {
      // Use the provider to fetch data for eqHedge
      final Map<String, List<PositionsModel>> portfolioData = await provider.fetchPositionsData(clientId);
      return portfolioData;
    } catch (e) {
      //debugPrint(e.toString());
      throw e is AppException ? e : AppException(e.toString());
    }
    
  }

}
